spring.profiles.active=dev
server.port=48080
# 2MB 请求头大小
server.max-http-header-size=2MB
#支持URL带上特殊的字符
server.tomcat.uri-encoding=UTF-8
server.tomcat.relaxed-query-chars=[,],|,{,},^,`,<,>,@,:,&,=,+,$
gpt.withdraw.limit=50000
# 新注册赠送500电力
gpt.freeCostToken=500
# 新注册赠送的次数
gpt.initCount=500
# 推广赠送的token数量
gpt.introduceTokenCount=100000
api.version=V2.0.2_Build
spring.mail.username=<EMAIL>
spring.mail.password=RVGMZOTWIYSAGMIZ
spring.mail.host=smtp.163.com
spring.mail.port=465
spring.mail.properties.mail.smtp.ssl.enable=true
spring.mail.default-encoding=UTF-8
divide.ratio=0.5
baidu.translation.appId=20240207001960840
baidu.translation.key=fccgWD3AEqY0PCHvmc0Y
# 全局转换器工厂
retrofit.global-converter-factories=com.github.lianjiatech.retrofit.spring.boot.core.BasicTypeConverterFactory,retrofit2.converter.jackson.JacksonConverterFactory
# 全局日志打印配置
# 启用日志打印
retrofit.global-log.enable=true
retrofit.global-log.log-level=info
# 全局日志打印策略
retrofit.global-log.log-strategy=basic
# 全局重试配置
# 是否启用全局重试
retrofit.global-retry.enable=false
# 全局重试间隔时间
retrofit.global-retry.interval-ms=100
# 全局最大重试次数
retrofit.global-retry.max-retries=2
# 全局重试规则
retrofit.global-retry.retry-rules=response_status_not_2xx,occur_io_exception
# 全局超时时间配置
# 全局读取超时时间
retrofit.global-timeout.read-timeout-ms=120000
# 全局写入超时时间
retrofit.global-timeout.write-timeout-ms=120000
# 全局连接超时时间
retrofit.global-timeout.connect-timeout-ms=120000
# 全局完整调用超时时间
retrofit.global-timeout.call-timeout-ms=0
# 熔断降级类型。默认none，表示不启用熔断降级
retrofit.degrade.degrade-type=none
# 全局sentinel降级配置
retrofit.degrade.global-sentinel-degrade.enable=true
# 各降级策略对应的阈值。平均响应时间(ms)，异常比例(0-1)，异常数量(1-N)
retrofit.degrade.global-sentinel-degrade.count=500
# 触发熔断后,熔断的时长，单位为 s
retrofit.degrade.global-sentinel-degrade.time-window=5
# 降级策略（0：平均响应时间；1：异常比例；2：异常数量）
retrofit.degrade.global-sentinel-degrade.grade=0
# 全局resilience4j降级配置
retrofit.degrade.global-resilience4j-degrade.enable=false
# 根据该名称从#{@link CircuitBreakerConfigRegistry}获取CircuitBreakerConfig，作为全局熔断配置
retrofit.degrade.global-resilience4j-degrade.circuit-breaker-config-name=defaultCircuitBreakerConfig
# 自动设置PathMathInterceptor的scope为prototype
retrofit.auto-set-prototype-scope-for-path-math-interceptor=true
#websocke传输文件大小
server.tomcat.websocket.text.max-text-message-size=20480
server.tomcat.websocket.binary.max-buffer-size=20480
# 接收文件大小
spring.servlet.multipart.max-file-size=5MB
spring.servlet.multipart.max-request-size=10MB
#需要超清修复的modelId
modelIds=93b2d438-88d0-4b78-8883-6bfa91554666,6e67c9b3-7c29-46dd-a75f-ae443e8fe402,e54469ae-47eb-482a-bcf4-8182e4c20901
realistic.ModelId=34ec1b5a-8962-4a93-b047-68cec9691dc2
anime.ModelId=cb4af9c7-41b0-47d3-944a-221446c7b8bc
animeV3.ModelId=f87a123e-4b5c-4d6e-b7a1-2c3d4e5f6a7b
lineart.ModelId=2f0d593a-47db-42e6-b90b-e4534df65a98
pony.ModelId=14a399de-69d9-4e3b-961d-e95b35853557
art.ModelId=23887bba-507e-4249-a0e3-6951e4027f2b
fluxschell.modelId=e40d01af-dec2-49dd-8944-f2aae4ba0b05
fluxdev.modelId=08e4f71a-416d-4f1e-a853-6b0178af1f09
fluxkrea.modelId=krea87d6-5e4f-3a2b-1c0d-9e8f7g6h5i4j
ttapimj.modelId=tmj82006-d2a9-4f93-ac2c-74275f80597c
fluxKontextPro.modelId=flux3dc-cff2-4177-ad3a-28d9b4d3ff48
picflow.modelId=picFlow1-58cc-4372-a567-0e02b2c3d479
# 配置sendcloud信息
sendcloud.api.user=piclumen_api
sendcloud.api.key=39411ea65c526a551434263b62f72e4b
sendcloud.Url=https://api.sendcloud.net
#配置websocket结果消息重试次数
wbMessage.max.retries=3
#mybatis-plus设置
mybatis-plus.type-aliases-package=com.lx.pl.db.mysql.gen.entity
contactUs.opex.loginName=<EMAIL>;\
<EMAIL>;\
<EMAIL>;\
<EMAIL>;\
<EMAIL>;\
<EMAIL>;\
<EMAIL>;\
<EMAIL>;\
<EMAIL>;\
<EMAIL>
#腾讯云 测试和开发不走全球加速
tencent.appId=1324066212
tencent.secretId=AKIDb9XjhRyaVMsBHSbaM1Yt593i9duxnUHd
tencent.secretKey=IWxEa8X2TjsvnSKEpJbhZDejmeSL6e9V
tencent.region=na-siliconvalley
tencent.bucket=testauth-1324066212
tencent.cos.domain=https://upload.piclumen.com
tencent.cos-accelerate.domain=https://uploads.piclumen.com
tencent.base.suffix=cos.na-siliconvalley.myqcloud.com
tencent.accelerate.cosSuffix=cos.accelerate.myqcloud.com
tencent.internal-accelerate.cosSuffix=cos.accelerate.myqcloud.com
#----------------------------------------------MQ----------------------------------------------
## MQ
rocketmq.producer.endpoints=rmq-58xzpeoqr.rocketmq.hk.public.tencenttdmq.com:8080
rocketmq.producer.accessKey=ak58xzpeoqre08c9cfe6b9a
rocketmq.producer.secretKey=sk7ea7d0db968550a3
rocketmq.pushConsumer.endpoints=rmq-58xzpeoqr.rocketmq.hk.public.tencenttdmq.com:8080
rocketmq.pushConsumer.access-key=ak58xzpeoqre08c9cfe6b9a
rocketmq.pushConsumer.secret-key=sk7ea7d0db968550a3
## midjourney polling queue
rocketmq.midjourney.polling.topic=tp_midjourney_polling_prod
rocketmq.midjourney.polling.group=gid_midjourney_polling_prod
rocketmq.midjourney.polling.tag=tag_midjourney_polling_prod
# ----------------------------------------------------------apple pay config ---------------------------------------------------------------------
apple.pay.sandboxUrl=https://sandbox.itunes.apple.com/verifyReceipt
apple.pay.productionUrl=https://buy.itunes.apple.com/verifyReceipt
apple.pay.sharedSecret=4dbab35248a4412395566665471213a4
#--------------------------------------------------------------
knife4j.enable=true
knife4j.production=false
knife4j.basic.enable=false
knife4j.basic.username=admin # Basic 认证用户名
knife4j.basic.password=123456 # Basic 认证密码
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.persist-authorization=true
springdoc.api-docs.enabled=true
springdoc.api-docs.path=/v3/api-docs
#springdoc.group-configs[0].group=业务接口
#springdoc.group-configs[0].packages-to-scan=com.lx.pl.controller,com.lx.pl.pay.controller
#springdoc.group-configs[1].group=支付相关
#springdoc.group-configs[1].packages-to-scan=com.lx.pl.pay.controller
knife4j.setting.language=zh-CN
management.endpoints.web.exposure.include=health,info
management.endpoint.health.show-details=always
management.endpoint.health.status.http-mapping.DOWN=503
management.endpoints.web.base-path=/api
management.health.mail.enabled=false
# google play
google.project-id=piclumen-454007
google.subscription-id=vip-msg-sub-test
google.subscription-name=projects/piclumen-454007/subscriptions/vip-msg-sub-test
google.package-name=nullart.ai
google.verifier-audience=https://s4.piclumen.art/api/google/pub-sub-push,\
     https://test.piclumen.art/api/google/pub-sub-push,\
     https://0200-103-184-129-96.ngrok-free.app/api/google/pub-sub-push,\
     https://ai.piclumen.art/api/google/pub-sub-push,\
     https://api.piclumen.com/api/google/pub-sub-push
google.public-key=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAxNSUxpC9UV11pAYuk252usjgbCjp0iz1w5mjBOy3j4CXWRwQInzD/PsQajEORILRGgOunuBs33vfOVhQu1X/4qEZKl2m3v22V+ZUJkG+Mogyb3FmgLST6fXDgTGPf8BRRArP0t6Fc3p2LQr0hXac3stZ4NZIiCGHgI2v/AOEr7h9Q3zXI1OA6QqNO3pF4njF8ZgF5289M9XbDjfBNSAm/4ZDeCXVZNvtL8qe4M5FEOWSBoeObkIbRoY6VqrNU7vNTIXsq2mI3TZ+RyozyjDuMNc5ZZZzXG/75WDeb7gx13XvlGgSuuLy1NJ1uLgRShVfWZ821gIdglZ5Wsc6QXYYjwIDAQAB
# 转盘
pay.lottery.discounts:standard_month:100,standard_month:90,pro_month:60,pro_month:50,lumen:500
pay.lottery.weights:5,10,45,30,10



