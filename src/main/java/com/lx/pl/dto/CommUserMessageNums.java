package com.lx.pl.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class CommUserMessageNums {

    @Schema(description = "未读点赞数")
    private Integer nLikeNums;

    @Schema(description = "未读点评论数")
    private Integer nCommentNums;

    @Schema(description = "未读系统更新数")
    private Integer nSysUpdateNums;

    @Schema(description = "未读平台消息数")
    private Integer nPlatformMessageNums;

    @Schema(description = "未读平台活动数")
    private Integer nPlatformActivityNums;

    @Schema(description = "未读消息总数")
    private Integer nMessageTotalNums;

    @Schema(description = "未读社区活动信息数量")
    private Integer commActivityNums;

    @Schema(description = "用户当前可用lumen点数")
    private Integer currentLumen;

    @Schema(description = "当前系统资源版本号")
    private String currentResourcesVersion;
}
