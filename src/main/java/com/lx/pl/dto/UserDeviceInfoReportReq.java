package com.lx.pl.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserDeviceInfoReportReq {

    /**
     * SE平台分配的appKey，开发者初始化传入
     */
    @Schema(description = "SE平台分配的appKey，开发者初始化传入")
    @JsonProperty("_appkey")
    private String _appkey;

    /**
     * SE生成的设备ID
     */
    @Schema(description = "SE生成的设备ID")
    @JsonProperty("_distinct_id")
    private String _distinct_id;

    /**
     * 开发者通过login接口传入的accountID
     */
    @Schema(description = "开发者通过login接口传入的accountID")
    @JsonProperty("_account_id")
    private String _account_id;

    /**
     * 开发者通过setVisitorID接口传入的visitorID
     */
    @Schema(description = "开发者通过setVisitorID接口传入的visitorID")
    @JsonProperty("_visitor_id")
    private String _visitor_id;

    /**
     * SE内部每次冷启动生成的sessionID
     */
    @Schema(description = "SE内部每次冷启动生成的sessionID")
    @JsonProperty("_session_id")
    private String _session_id;

    /**
     * SE安装时产生的唯一UUID (Android)
     */
    @Schema(description = "SE安装时产生的唯一UUID (Android)")
    @JsonProperty("_uuid")
    private String _uuid;

    /**
     * 设备IMEI (Android)
     */
    @Schema(description = "设备IMEI (Android)")
    @JsonProperty("_imei")
    private String _imei;

    /**
     * 设备IMEI2 (Android)
     */
    @Schema(description = "设备IMEI2 (Android)")
    @JsonProperty("_imei2")
    private String _imei2;

    /**
     * 设备gaid (Android)
     */
    @Schema(description = "设备gaid (Android)")
    @JsonProperty("_gaid")
    private String _gaid;

    /**
     * 设备oaid (Android)
     */
    @Schema(description = "设备oaid (Android)")
    @JsonProperty("_oaid")
    private String _oaid;

    /**
     * 设备AndroidId (Android)
     */
    @Schema(description = "设备AndroidId (Android)")
    @JsonProperty("_android_id")
    private String _android_id;

    /**
     * 设备IDFA (iOS)
     */
    @Schema(description = "设备IDFA (iOS)")
    @JsonProperty("_idfa")
    private String _idfa;

    /**
     * 设备的IDFV (iOS)
     */
    @Schema(description = "设备的IDFV (iOS)")
    @JsonProperty("_idfv")
    private String _idfv;

    /**
     * 设备的UA
     */
    @Schema(description = "设备的UA")
    @JsonProperty("_ua")
    private String _ua;

    /**
     * 设备的系统设置的语言
     */
    @Schema(description = "设备的系统设置的语言")
    @JsonProperty("_language")
    private String _language;

    /**
     * 设备的时区
     */
    @Schema(description = "设备的时区")
    @JsonProperty("_time_zone")
    private String _time_zone;

    /**
     * 设备生成厂商
     */
    @Schema(description = "设备生成厂商")
    @JsonProperty("_manufacturer")
    private String _manufacturer;

    /**
     * SDK平台，1：Android，2：iOS
     */
    @Schema(description = "SDK平台，1：Android，2：iOS")
    @JsonProperty("_platform")
    private Integer _platform;

    /**
     * 设备系统版本
     */
    @Schema(description = "设备系统版本")
    @JsonProperty("_os_version")
    private String _os_version;

    /**
     * 屏幕高
     */
    @Schema(description = "屏幕高")
    @JsonProperty("_screen_height")
    private Integer _screen_height;

    /**
     * 屏幕宽
     */
    @Schema(description = "屏幕宽")
    @JsonProperty("_screen_width")
    private Integer _screen_width;

    /**
     * 屏幕密度 (Android)
     */
    @Schema(description = "屏幕密度 (Android)")
    @JsonProperty("_density")
    private Float _density;

    /**
     * 设备型号
     */
    @Schema(description = "设备型号")
    @JsonProperty("_device_model")
    private String _device_model;

    /**
     * 设备类型
     */
    @Schema(description = "设备类型")
    @JsonProperty("_device_type")
    private Integer _device_type;

    /**
     * 应用版本号
     */
    @Schema(description = "应用版本号")
    @JsonProperty("_app_version")
    private String _app_version;

    /**
     * 应用版本code
     */
    @Schema(description = "应用版本code")
    @JsonProperty("_app_version_code")
    private String _app_version_code;

    /**
     * 应用包名
     */
    @Schema(description = "应用包名")
    @JsonProperty("_package_name")
    private String _package_name;

    /**
     * 应用名称
     */
    @Schema(description = "应用名称")
    @JsonProperty("_app_name")
    private String _app_name;

    /**
     * 渠道名称
     */
    @Schema(description = "渠道名称")
    @JsonProperty("_channel")
    private String _channel;

    /**
     * 固定字段
     */
    @Schema(description = "固定字段")
    @JsonProperty("_lib")
    private Integer _lib;

    /**
     * SDK版本号
     */
    @Schema(description = "SDK版本号")
    @JsonProperty("_lib_version")
    private String _lib_version;
}
