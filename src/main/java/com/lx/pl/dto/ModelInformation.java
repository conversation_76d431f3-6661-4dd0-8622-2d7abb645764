package com.lx.pl.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.List;
import java.util.Map;


@Data
public class ModelInformation {

    private List<ModelAbout> modelMessage;

    @ToString
    @Data
    public static class ModelAbout {

        @Schema(description = "模型id")
        private String modelId;

        @Schema(description = "模型名称")
        private String modelDisplay;

        @Schema(description = "模型类型")
        private String modelType;

        @Schema(description = "默认超分降噪指数")
        private Double defaultHdFixDenoise;

        @Schema(description = "模型图标")
        private String modelAvatar;

        @Schema(description = "模型描述")
        private String modelDesc;

        @Schema(description = "模型排序")
        private int modelOrder;

        @Schema(description = "模型详情")
        private DefaultConfig defaultConfig;

        @Schema(description = "模型所支持的风格类型")
        private List<SupportStyle> supportStyleList;

        @Schema(description = "模型所适用的会员等级，多个以英文逗号分隔")
        private String suitableForMemberLevel;

        @Schema(description = "按张数扣点系数")
        private Integer coefficientByNum;

        @Schema(description = "按像素扣点系数")
        private Integer coefficientByPixel;

        @Schema(description = "付费试用次数，key：会员等级，value：次数")
        private Map<String, Integer> payTrial;

        @Schema(description = "免费试用次数，key：会员等级，value：次数")
        private Map<String, Integer> freeTrial;

        @Schema(description = "已使用付费试用次数")
        private Integer usedPayTrial;

        @Schema(description = "已使用免费试用次数")
        private Integer usedFreeTrial;
    }

    @ToString
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DefaultConfig {

        @Schema(description = "宽")
        private int width;

        @Schema(description = "高")
        private int height;

        @Schema(description = "种子（一串随机数）")
        private Long seed;

        @Schema(description = "迭代步数")
        private int steps;

        @Schema(description = "提示词引导系数")
        private double cfg;

        @Schema(description = "采样器名称")
        private String samplerName;

        @Schema(description = "调度器")
        private String scheduler;

        @Schema(description = "降噪幅度")
        private double denoise;

        @Schema(description = "正向提示器")
        private String positivePrompt;

        @Schema(description = "反向提示词")
        private String negativePrompt;

        @Schema(description = "高清修复降噪幅度")
        private double hiresFixDenoise;

        @Schema(description = "高清修复倍数")
        private double hiresScale;

        @Schema(description = "模型扩展能力")
        private ModelAbility modelAbility;
    }

    @ToString
    @Data
    public static class ModelAbility {

        @Schema(description = "0.0 - 1.0 之间，默认 0 不生效")
        private double animeStyleControl;

    }

    @ToString
    @Data
    public static class SupportStyle {

        @Schema(description = "风格标签")
        private String label;

        @Schema(description = "风格标签头像")
        private String avatar;

        @Schema(description = "风格标签值")
        private String value;

        @Schema(description = "排序")
        private Integer sort;

        @Schema(description = "默认权重")
        private Double weight;
    }

}
