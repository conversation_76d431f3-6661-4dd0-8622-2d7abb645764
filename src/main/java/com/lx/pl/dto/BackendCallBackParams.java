package com.lx.pl.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class BackendCallBackParams {

    @Schema(description = "任务成功信息")
    private BackendCallBackParams.Result result;

    @ToString
    @Data
    public static class Result {

        @Schema(description = "生图prompt_id")
        private String prompt_id;

        @Schema(description = "当前生图任务执行状态")
        private String prompt_status;

        @Schema(description = "生图成功后地址")
        private List<BackendCallBackParams.ImgMessage> imgMessageList;

    }

    @ToString
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ImgMessage {

        @Schema(description = "原始图片生成后腾讯云地址")
        private String img_url;

        @Schema(description = "图片大小")
        private Long size;

        @Schema(description = "缩略图生成后腾讯云地址")
        private String thumbnail_url;

        @Schema(description = "高清缩略图生成后腾讯云地址")
        private String high_thumbnail_url;

        @Schema(description = "mini图")
        private String miniThumbnailUrl;

        @Schema(description = "30% 高清图")
        private String highMiniUrl;

        @Schema(description = "敏感信息")
        private String sensitive;

        @Schema(description = "原始图片真实宽")
        private int width;

        @Schema(description = "原始图片高")
        private int height;

    }
}
