package com.lx.pl.dto;

import com.lx.pl.exception.LogicException;
import lombok.Data;

import static com.lx.pl.enums.LogicErrorCode.NOT_ENOUGH_LUMENS_SUPPORT;

/**
 * <AUTHOR>
 * @date 2025/7/16
 * @description 用户模型权益校验结果DTO
 */
@Data
public class ModelRightsVerifyResult {
    /**
     * 是否通过验证
     */
    private boolean passVerify;

    /**
     * 是否使用免费试用
     */
    private boolean useFreeTrial;

    /**
     * 是否使用付费试用
     */
    private boolean usePayTrial;

    /**
     * 需要消耗的Lumen
     */
    private int costLumen;

    /**
     * 会员等级
     */
    private String vipType;

    /**
     * 使用的模型信息
     */
    private ModelInformation.ModelAbout useModel;


    public void checkPassVerify() {
        if (!passVerify) {
            throw new LogicException(NOT_ENOUGH_LUMENS_SUPPORT);
        }
    }
}
