package com.lx.pl.enums;

public enum FeaturesType {

    ttp("ttp", "文生图"),
    upscale("upscale", "超分"),

    enlargeUpscale("enlargeUpscale", "上传图片超分"),
    inpaint("inpaint", "局部重绘"),
    expand("expand", "扩图"),
    ptpContent("ptpContent", "图生图（内容）"),

    ptpFluxContent("ptpFluxContent", "flux 图生图（内容）"),
    ptpCharacter("ptpCharacter", "图生图（角色）"),
    ptpFluxCharacter("ptpFluxCharacter", "flux 图生图（角色）"),
    ptpStyle("ptpStyle", "图生图（风格）"),
    ptpFluxPro("ptpFluxPro", "图生图"),

    openposeControl("openposeControl", "pose生图"),
    removeBg("removeBg", "去背景"),
    lineRecolor("lineRecolor", "线稿上色"),
    genfast("genfast", "快速生图"),
    vary("vary", "图片微变"),
    multiRefer("multiRefer", "多图生图"),
    edit("edit", "通过提示词编辑图像");

    private String value;

    private String label;

    //如果枚举值中还有数据则必须要创建一个构造函数
    FeaturesType(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }
}
