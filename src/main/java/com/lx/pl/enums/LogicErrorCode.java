package com.lx.pl.enums;

/**
 * @Description: logic处理异常枚举，描述不返回前端，用于细分错误码，message 描述前端显示错误
 * @Author: senlin_he
 * @Date: 2024/12/27
 */
public enum LogicErrorCode {
    ILLEGAL_PROMPT("4001", "Sorry!  The AI detected that this prompt might break out community rules. Please try a different one.", "提示词涉及儿童色情词汇"),

    EXCEED_CONCURRENT_JOBS("4002", "Exceed  Concurrent Jobs ", "超过并发任务限制"),

    EXCEED_TASK_QUEUE("4003", "Exceed_TaskQueue ", "超过预载队列现在"),
    NOT_VIP("4005", "Please recharge VIP membership first ", "用户非vip"),

    NOT_ENOUGH_LUMENS_SUPPORT("4006", "Please recharge VIP membership and buy lumens first ", "用户不够dev权限"),

    NOT_TASK_EXIST("4007", "Task does not exist ", "任务已经被删除"),

    NOT_MODEL_SUPPORT("4008", "Model required ", "用户没有传modelId"),

    PIXELS_EXCEED_LIMIT("4009", "Pixels exceed limit", "像素超过限制"),

    CONTINUE_CREATE("4011", "Continue Create", "忽略告警，继续生图"),

    RELAX_CREATE_LIMIT("4012", "Relax Create Limit", "非会员，闲时生图达到上线"),

    UNKNOWN_ERROR("4999", "A generic error occurred during logic processing.", "处理逻辑时发生未知异常"),

    USER_PUBLIC_ACTIVITY_LIMIT("4013", "Your image has reached this activity limit.", "用户活动投稿超过次活动限制"),

    USER_ACTIVITY_TIME_PASSED("4014", "The deadline for submitting entries has passed.", "活动投稿时间已过"),

    USER_PUBLIC_RESUBMIT("4015", "Please do not resubmit.", "重复提交"),

    NOT_DELETE_WIN_IMAGE("4016", "This image has won an award and cannot be deleted.", "获奖图片不能删除"),
    BATCH_RMBG_TASK_LIMITED("4017", "A small system hiccup. Please try again later.", "批量去背景任务数已达上限"),

    BATCH_RMBG_BATCH_ID_EMPTY_ERROR("4018", "A small system hiccup. Please try again later.", "批量去背景batchId为空"),

    // Midjourney相关错误码
    MIDJOURNEY_API_ERROR("4019", "This image processing is failed. Please try again later", "Midjourney API调用失败"),

    COUPON_MAX_REDEMPTIONS("8001", "Coupon has reached the maximum number of redemptions.", "优惠券已达到最大兑换次数"),
    COUPON_EXPIRED("8002", "Coupon has expired.", "优惠券已过期"),
    COUPON_NOT_START("8004", "Coupon is not start.", "优惠券未开始"),
    COUPON_NOT_FOUND("8003", "Coupon not found.", "优惠券不存在"),
    COUPON_NOT_SUPPORT("8005", "Coupon not support.", "优惠券不支持"),
    MIDJOURNEY_PROMPT_CHECK_FAILED("4020", "Prompt check failed", "Prompt检查失败"),

    MIDJOURNEY_GENERATION_FAILED("4021", "This image processing is failed. Please try again later", "图像生成失败"),

    ILLEGAL_REQUEST("4022", "This image processing is failed. Please try again later", "非法请求"),

    MODEL_NOT_SUPPORT("4023", "This image processing is failed. Please try again later", "模型不支持"),

    MIDJOURNEY_ACTION_FAILED("4024", "This image processing is failed. Please try again later", "Midjourney操作失败"),

    TTAPI_EXCEED_CONCURRENT_JOBS("4025", "The model is currently experiencing high traffic. Please try again later.", "ttapi超过并发任务限制"),

    // Flux相关错误码
    FLUX_API_ERROR("4026", "This image processing is failed. Please try again later", "Flux API调用失败"),

    FLUX_EXCEED_CONCURRENT_JOBS("4027", "The model is currently experiencing high traffic. Please try again later.", "Flux超过并发任务限制"),

    FLUX_PROMPT_REQUIRED("4028", "prompt is required", "prompt不能为空"),
    NOT_FOUND_COMM_FILE("4030", "Not found comm file", "社区图片不存在"),

    HAS_RENEWAL_SUBSCRIBER("4031", "cannot delete the current renewal subscriber", "不能删除当前有续订订阅用户"),

    EMAIL_FORMAT_ERROR("4032", "please use the correct email format", "邮箱格式不对"),

    USER_ACCOUNT_DELETED_OR_NOT_EXIST("4033", "this user does not exist or has been deleted", "账户已被删除"),
    // 已经参加过活动
    HAS_PARTICIPATED("1001", "You have already participated in the activity", "已经参加过活动"),
    // 折扣资源已用完
    DISCOUNT_RESOURCE_USED_UP("1002", "Discount resource used up", "折扣资源已用完"),
    //    活动未开始或已结束
    ACTIVITY_NOT_STARTED_OR_ENDED("1003", "Activity not started or ended", "活动未开始或已结束"),

    IMAGE_NOT_SUPPORT("4060", "Image not support !", "不支持的图片");


    private final String code;
    private final String message;
    private final String description;

    LogicErrorCode(String code, String message, String description) {
        this.code = code;
        this.message = message;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public String getDescription() {
        return description;
    }
}
