package com.lx.pl.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

@Getter
public enum ClientType {

    ios("ios", "苹果App"),
    android("android", "安卓app"),
    web("web", "浏览器端");

    private final String value;

    private final String label;

    public final static Set<String> allValues = Set.of(Arrays.stream(values()).map(ClientType::getValue).toArray(String[]::new));

    //如果枚举值中还有数据则必须要创建一个构造函数
    ClientType(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public static ClientType getByValue(String value) {
        if (StringUtils.isBlank(value)) {
            return web;
        }
        return Arrays.stream(values()).filter(type -> type.value.equalsIgnoreCase(value)).findFirst().orElse(web);
    }

    public static boolean mobilePlatform(String platform) {
        if (StringUtils.isBlank(platform)) {
            return false;
        }
        return ios.getValue().equalsIgnoreCase(platform) || android.getValue().equalsIgnoreCase(platform);
    }

    public static boolean isAllPlatformSelected(String platforms) {
        if (StringUtils.isBlank(platforms)) {
            return false;
        }

        // 先拆分，去除空白，转换小写，放到Set去重
        Set<String> inputSet = Arrays.stream(platforms.split(","))
                .map(String::trim)
                .map(String::toLowerCase)
                .collect(Collectors.toSet());

        // allValues 已经是全部 platformName 的集合，这里转小写方便比较
        Set<String> allPlatformNames = allValues.stream()
                .map(String::toLowerCase)
                .collect(Collectors.toSet());

        // 判断传入的集合是否包含全部平台
        return inputSet.containsAll(allPlatformNames);
    }
}
