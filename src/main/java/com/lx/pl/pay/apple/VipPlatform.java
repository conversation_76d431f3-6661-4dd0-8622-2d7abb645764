package com.lx.pl.pay.apple;

public enum VipPlatform {
    STRIPE("stripe"),  // Stripe 平台
    IOS("ios"),         // iOS 平台
    PAYPAL("paypal"),         // paypal 平台
    GOOGLE("google"),         // iOS 平台
    ANDROID("android"), // Android 平台
    GIFT("gift"); // gift

    private final String platformName;

    // 构造方法
    VipPlatform(String platformName) {
        this.platformName = platformName;
    }

    // 获取平台名称的方法
    public String getPlatformName() {
        return platformName;
    }

    // 根据平台名称获取对应的枚举
    public static VipPlatform fromString(String platformName) {
        for (VipPlatform platform : VipPlatform.values()) {
            if (platform.platformName.equalsIgnoreCase(platformName)) {
                return platform;
            }
        }
        throw new IllegalArgumentException("Unknown platform: " + platformName);
    }
}
