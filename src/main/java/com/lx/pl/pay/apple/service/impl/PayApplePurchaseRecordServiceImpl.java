package com.lx.pl.pay.apple.service.impl;

import com.apple.itunes.storekit.model.JWSTransactionDecodedPayload;
import com.apple.itunes.storekit.model.Type;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lx.pl.db.mysql.gen.entity.PayAppleProduct;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.pay.apple.VipPlatform;
import com.lx.pl.pay.apple.domain.PayApplePurchaseRecord;
import com.lx.pl.pay.apple.mapper.PayApplePurchaseRecordMapper;
import com.lx.pl.pay.apple.service.PayAppleProductService;
import com.lx.pl.pay.apple.service.PayApplePurchaseRecordService;
import com.lx.pl.pay.common.service.PayLumenRecordService;
import com.lx.pl.pay.common.service.UserPayRecordService;
import com.lx.pl.pay.common.dto.SavePaymentRecordRequest;
import com.lx.pl.pay.common.enums.PaymentPlatform;
import com.lx.pl.pay.common.enums.PaymentSourceEnum;
import com.lx.pl.pay.common.enums.PaymentDetailEnum;

import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class PayApplePurchaseRecordServiceImpl extends ServiceImpl<PayApplePurchaseRecordMapper, PayApplePurchaseRecord> implements PayApplePurchaseRecordService {

    Logger log = LoggerFactory.getLogger("apple-pay-msg");

    @Resource
    private PayAppleProductService payAppleProductService;
    @Resource
    private PayLumenRecordService payLumenRecordService;
    @Resource
    private UserPayRecordService userPayRecordService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveAppleLogicPurchaseRecord(JWSTransactionDecodedPayload payload, User user, Type type) {
        log.info("start saveAppleLogicPurchaseRecord userId:{}, transactionId:{}", user.getId(), payload.getTransactionId());

        // 检查记录是否已存在
        long existRecord = this.lambdaQuery()
                .eq(PayApplePurchaseRecord::getTransactionId, payload.getTransactionId()).count();
        if (existRecord > 0) {
            log.info("payLogicPurchaseRecord already exists userId:{}, transactionId:{}", user.getId(), payload.getTransactionId());
            return false;
        }

        // 获取对应的Apple产品信息
        PayAppleProduct product = payAppleProductService.lambdaQuery()
                .eq(PayAppleProduct::getAppleProductId, payload.getProductId()).one();
        if (product == null) {
            throw new RuntimeException("Apple product not found: " + payload.getProductId());
        }

        // 构建购买记录
        PayApplePurchaseRecord record = buildPayApplePurchaseRecord(payload, user, product);
        // 保存记录
        this.save(record);

        if (Type.AUTO_RENEWABLE_SUBSCRIPTION.getValue().equals(type.getValue())) {
            payLumenRecordService.saveLumenRecordForApple(record, product);
        }
        boolean hasPurchasedLumen = payLumenRecordService.hasPurchasedLumen(record.getUserId());
        if (Type.CONSUMABLE.getValue().equals(type.getValue())) {
            if (!hasPurchasedLumen) {
                PayApplePurchaseRecord record2 = new PayApplePurchaseRecord();
                BeanUtils.copyProperties(record, record2, "id");
                record.setVipPlatForm(VipPlatform.GIFT.getPlatformName());
                record.setLumenQty(product.getInitialLumen());
                record.setCount(1);
                this.save(record2);
                payLumenRecordService.saveOneTimeLumenRecordForApple(record2);
            }
            payLumenRecordService.saveOneTimeLumenRecordForApple(record);
        }

        // 保存用户支付记录
        saveAppleUserPayRecord(payload, user, product, type, hasPurchasedLumen);

        log.info("end saveAppleLogicPurchaseRecord userId:{}, transactionId:{}", user.getId(), payload.getTransactionId());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String recallLumenByTransactionId(String lastTransactionId) {
        PayApplePurchaseRecord one = this.lambdaQuery().eq(PayApplePurchaseRecord::getTransactionId, lastTransactionId)
                .one();
        if (one == null) {
            log.info("未找到购买记录: {}", lastTransactionId);
            return lastTransactionId;
        }
        payLumenRecordService.recallLumen(one.getId(), "apple upgrade");
        this.lambdaUpdate().eq(PayApplePurchaseRecord::getTransactionId, lastTransactionId)
                .set(PayApplePurchaseRecord::getCancel, true)
                .set(PayApplePurchaseRecord::getUpdateTime, LocalDateTime.now())
                .update();
        log.info("end recallLumenByTransactionId {}", lastTransactionId);
        return one.getProductId();

    }

    @NotNull
    private static PayApplePurchaseRecord buildPayApplePurchaseRecord(JWSTransactionDecodedPayload payload, User user, PayAppleProduct product) {
        if (payload == null || user == null || product == null) {
            throw new IllegalArgumentException("Required parameters cannot be null: payload=" + payload + ", user=" + user + ", product=" + product);
        }

        PayApplePurchaseRecord record = new PayApplePurchaseRecord();
        record.setCreateTime(LocalDateTime.now());
        record.setUserId(user.getId());
        record.setLoginName(user.getLoginName());
        record.setLumenQty(product.getLumen());
        record.setVipPlatForm(VipPlatform.IOS.getPlatformName());

        // 安全处理 PriceInterval
        String priceInterval = product.getPriceInterval();
        if (priceInterval != null && priceInterval.toLowerCase().contains("year")) {
            record.setCount(12);
        } else {
            record.setCount(1);
        }

        // 安全处理可能为空的字段
        record.setPrice(payload.getPrice());
        record.setExpiresDate(payload.getExpiresDate() != null ? payload.getExpiresDate() / 1000 : null);
        record.setOriginalTransactionId(payload.getOriginalTransactionId());
        record.setTransactionId(payload.getTransactionId());
        record.setPurchaseDate(payload.getPurchaseDate() != null ? payload.getPurchaseDate() / 1000 : null);
        record.setOriginalPurchaseDate(payload.getOriginalPurchaseDate() != null ? payload.getOriginalPurchaseDate() / 1000 : null);

        if (payload.getRevocationDate() != null) {
            record.setRevocationDate(payload.getRevocationDate() / 1000);
        }

        record.setCancel(false);

        // 安全处理 AppAccountToken
        if (payload.getAppAccountToken() != null) {
            record.setAppAccountToken(payload.getAppAccountToken().toString());
        }

        // 安全处理时间戳
        record.setLogicPeriodStart(payload.getPurchaseDate());
        record.setLogicPeriodEnd(payload.getExpiresDate());
        record.setProductId(payload.getProductId());

        return record;
    }

    /**
     * 保存苹果支付的用户支付记录
     *
     * @param payload           苹果交易数据
     * @param user              用户信息
     * @param product           苹果产品信息
     * @param type              产品类型
     * @param hasPurchasedLumen
     */
    private void saveAppleUserPayRecord(JWSTransactionDecodedPayload payload, User user, PayAppleProduct product, Type type, boolean hasPurchasedLumen) {
        try {
            // 检查幂等性

            if (userPayRecordService.checkAppleIdempotency(payload.getOriginalTransactionId(), payload.getTransactionId()) != null) {
                log.info("Apple user pay record already exists for originalTransactionId: {}, transactionId: {}",
                        payload.getOriginalTransactionId(), payload.getTransactionId());
                return;
            }

            SavePaymentRecordRequest request = new SavePaymentRecordRequest();
            request.setLoginName(user.getLoginName());
            request.setUserId(user.getId());
            request.setPlatform(PaymentPlatform.APPLE.getCode());
            request.setExternalTransactionId(payload.getOriginalTransactionId());
            request.setExternalOrderId(payload.getTransactionId());
            request.setPaymentStatus("completed");

            // 设置金额信息（苹果价格通常以分为单位）
            if (payload.getPrice() != null) {
                request.setAmount(payload.getPrice());
                request.setAfterDiscountAmount(payload.getPrice());
                request.setAmountExcludingTax(payload.getPrice());
            }

            // 设置货币
            if (payload.getCurrency() != null) {
                request.setCurrency(payload.getCurrency());
            }

            // 创建支付记录项
            List<SavePaymentRecordRequest.PaymentRecordItemRequest> items = new ArrayList<>();
            SavePaymentRecordRequest.PaymentRecordItemRequest item = new SavePaymentRecordRequest.PaymentRecordItemRequest();

            item.setProductType(Type.AUTO_RENEWABLE_SUBSCRIPTION.getValue().equals(type.getValue()) ? "plan" : "one");
            item.setPlanLevel(product.getPlanLevel());
            item.setPriceInterval(product.getPriceInterval());
            item.setQty(1);

            if (payload.getPrice() != null) {
                item.setUnitAmount(payload.getPrice());
                item.setTotalAmount(payload.getPrice());
//                item.setTotalAmountExcludingTax(payload.getPrice());
            }

            if (payload.getCurrency() != null) {
                item.setCurrency(payload.getCurrency());
            }

            if (product.getLumen() != null) {
                item.setUnitLumen(product.getLumen());
                item.setTotalLumen(product.getLumen());
            }
            item.setGiftLumen(0);
            if (product.getInitialLumen() != null && !hasPurchasedLumen) {
                item.setGiftLumen(product.getInitialLumen());
            }

            items.add(item);
            request.setItems(items);
            // 设置总Lumen数量
            if (product.getLumen() != null) {
                request.setTotalLumen(product.getLumen().longValue() + item.getGiftLumen());
            }
            // 根据产品类型设置来源和详情
            String source;
            String detail;
            PaymentDetailEnum paymentDetail = null;

            if (Type.AUTO_RENEWABLE_SUBSCRIPTION.getValue().equals(type.getValue())) {
                source = PaymentSourceEnum.SUBSCRIBE.getName();
                paymentDetail = getPaymentDetailByPlanAndInterval(product.getPlanLevel(), product.getPriceInterval());
                if (paymentDetail != null) {
                    detail = paymentDetail.getName();
                } else {
                    detail = String.format("%s %s Subscription",
                            product.getPlanLevel() != null ? product.getPlanLevel() : "Standard",
                            product.getPriceInterval() != null ? product.getPriceInterval() : "Monthly");
                }
            } else if (Type.CONSUMABLE.getValue().equals(type.getValue())) {
                source = PaymentSourceEnum.PURCHASE_LUMENS.getName();
                paymentDetail = PaymentDetailEnum.LUMENS_PURCHASE;
                detail = String.format(paymentDetail.getName(), request.getTotalLumen() != null ? request.getTotalLumen() : 0);
            } else {
                source = PaymentSourceEnum.PLATFORM.getName();
                detail = "Apple Purchase";
            }

            request.setSource(source);
            request.setDetail(detail);
            // 保存支付记录
            userPayRecordService.savePaymentRecord(request);
            log.info("Successfully saved Apple user pay record for userId: {}, originalTransactionId: {}, transactionId: {}",
                    user.getId(), payload.getOriginalTransactionId(), payload.getTransactionId());

        } catch (Exception e) {
            log.error("Failed to save Apple user pay record for userId: {}, originalTransactionId: {}, transactionId: {}",
                    user.getId(), payload.getOriginalTransactionId(), payload.getTransactionId(), e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 根据计划等级和价格间隔获取支付详情枚举
     */
    private PaymentDetailEnum getPaymentDetailByPlanAndInterval(String planLevel, String priceInterval) {
        if (planLevel == null || priceInterval == null) {
            return null;
        }
        return PaymentDetailEnum.getByPlanLevelAndPriceInterval(planLevel, priceInterval);
    }
}