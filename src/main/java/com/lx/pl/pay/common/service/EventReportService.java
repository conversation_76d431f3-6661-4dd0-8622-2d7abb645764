package com.lx.pl.pay.common.service;


import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.xiaoymin.knife4j.core.util.CollectionUtils;
import com.lx.pl.pay.apple.VipPlatform;
import com.lx.pl.pay.apple.config.PayBeanConfig;
import com.lx.pl.pay.common.domain.AiUserDeviceInfoPO;
import com.lx.pl.pay.common.dto.appdevice.AndroidDeviceInfo;
import com.lx.pl.pay.common.dto.appdevice.IosDeviceInfo;
import com.lx.pl.pay.common.mapper.AiUserDeviceInfoMapper;
import com.lx.pl.util.Md5Utils;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;


/**
 * <a href="https://help.solar-engine.com/cn/docs/Koumhl">solarEngine</a>
 */
@Service
@Slf4j
public class EventReportService {


    @Autowired
    PayBeanConfig payBeanConfig;
    @Autowired
    private AiUserDeviceInfoMapper aiUserDeviceInfoDao;

    @Autowired
    private ThreadPoolTaskExecutor purchaseEventReportExecutor;
    @Resource(name = "logicObjectMapper")
    private ObjectMapper objectMapper;

    @Value("${solar.engine.report-url}")
    private String reportUrl;
    @Value("${solar.engine.android-app-key:}")
    private String androidAppKey;
    @Value("${solar.engine.ios-app-key}")
    private String iosAppKey;

    /**
     * 应用内购买上报
     * <p>
     * apple 支付 数据来源 ； pay_apple_jws_transaction
     * google 支付 数据来源 : pay_google_subscription_history
     *
     * @param userId
     * @param platform
     * @param orderNo
     * @param amount
     * @param currencyType
     * @param payType
     * @param timestamp
     */
    public void purchaseEventReport(Long userId,
                                    VipPlatform platform, String orderNo,
                                    Double amount,
                                    String currencyType,
                                    String transactionReason,
                                    String payType,
                                    Long timestamp) {
        try {
            purchaseEventReportExecutor.submit(() -> {
                AiUserDeviceInfoPO aiUserDeviceInfoPO = aiUserDeviceInfoDao.selectOne(new LambdaQueryWrapper<AiUserDeviceInfoPO>()
                        .eq(AiUserDeviceInfoPO::getUserId, userId)
                        .eq(AiUserDeviceInfoPO::getPlatform, platform.getPlatformName()));
                String idfa = null;
                String idfv = null;
                String version = null;
                String androidId = null;
                if (aiUserDeviceInfoPO != null) {
                    if (platform == VipPlatform.IOS) {
                        // iOS 平台需要传递 IDFA 和 IDFV
                        IosDeviceInfo info = null;
                        try {
                            info = objectMapper.readValue(aiUserDeviceInfoPO.getDeviceInfo(), IosDeviceInfo.class);
                        } catch (JsonProcessingException e) {
                            throw new RuntimeException(e);
                        }
                        idfa = info.get_idfa();
                        idfv = info.get_idfv();
                        version = info.get_app_version();
                    }

                    if (platform == VipPlatform.ANDROID) {
                        // Android 平台需要传递 AndroidId
                        AndroidDeviceInfo info = null;
                        try {
                            info = objectMapper.readValue(aiUserDeviceInfoPO.getDeviceInfo(), AndroidDeviceInfo.class);
                        } catch (JsonProcessingException e) {
                            throw new RuntimeException(e);
                        }
                        androidId = info.get_android_id();
                    }

                }
                orderEventReport(buildOrderEventReportDTO(userId, "127.0.0.1", platform, orderNo, amount, currencyType, payType, timestamp, idfa, idfv, version, androidId, transactionReason));
            });
        } catch (Exception e) {
            log.error("purchaseEventReport error", e);
        }
    }


    public void orderEventReport(OrderEventReportDTO eventReportDTO) {
        if (null == eventReportDTO || eventReportDTO.getProperties() == null) {
            return;
        }
        Long timestamp = eventReportDTO.get_ts();

        // 根据平台类型选择对应的签名方法
        String sign;
        Integer platform = eventReportDTO.getProperties().get_platform();
        if (platform != null && platform == 1) {
            // Android平台
            sign = getAndroidAppKey(timestamp);
        } else if (platform != null && platform == 2) {
            // iOS平台
            sign = getIosAppKeyParam(timestamp);
        } else {
            log.error("Unsupported platform: {}", platform);
            return;
        }

        String url = reportUrl
                + "?" + "timestamp=" + timestamp
                + "&sign=" + sign;

        try {
            // 设置请求头
            Headers headers = Headers.of("Content-Type", "application/json");
            // 将DTO对象转换为JSON字符串
            ObjectMapper objectMapper = new ObjectMapper();
            String requestBody = objectMapper.writeValueAsString(CollectionUtils.newArrayList(eventReportDTO));
            // 发送POST请求
//            String result = OkHttpClientUtil.post(url, headers, requestBody, String.class);
            try (HttpResponse execute = HttpUtil.createPost(url)
                    .header("Content-Type", "application/json")
                    .body(requestBody).execute()) {
                log.info("Event report success, result: {} requestBody: {}", execute.body(), requestBody);
            }

        } catch (Exception e) {
            log.error("Event report failed", e);
        }
    }


    private String getAndroidAppKey(Long timestamp) {
        if (androidAppKey == null) {
            log.error("androidAppKey is null");
            return null;
        }
        return Md5Utils.hash(androidAppKey + timestamp);
    }


    private String getIosAppKeyParam(Long timestamp) {
        if (iosAppKey == null) {
            log.error("iosAppKey is null");
            return null;
        }
        return Md5Utils.hash(iosAppKey + timestamp);
    }


    public OrderEventReportDTO buildOrderEventReportDTO(Long userId, String ip,
                                                        VipPlatform platform,
                                                        String orderNo,
                                                        Double amount,
                                                        String currencyType,
                                                        String payType, Long timestamp,
                                                        String _idfa,
                                                        String _idfv,
                                                        String appVersion,
                                                        String androidId,
                                                        String transactionReason) {
        // 试订阅 苹果支付 实际支付金额为 0
        if (null == amount || Objects.equals(amount, 0.0D)) {
            return null;
        }

        OrderEventReportDTO dto = new OrderEventReportDTO();

        // 设置主要字段
        dto.set_ts(timestamp);
        dto.set_event_id(transactionReason+"_"+orderNo); // 用于数据排重复，使用订单号
//        dto.setPur_type(transactionReason);

        dto.set_account_id(String.valueOf(userId)); // userId
        dto.set_ip(ip); // 用户ip

        // 根据平台设置相关字段
        if (platform == VipPlatform.ANDROID) {
            dto.set_appkey(androidAppKey);
            dto.set_ua("android"); // 设备UA信息，服务端没有，传android或ios
            dto.set_distinct_id(androidId);
            dto.set_android_id(androidId); // Android 设备的 AndroidId
        } else if (platform == VipPlatform.IOS) {
            dto.set_appkey(iosAppKey);
            dto.set_ua("ios"); // 设备UA信息，服务端没有，传android或ios
            dto.set_distinct_id(_idfv); // 用户设备ID，服务端没有，IOS 要传 _idfv
            dto.set_idfa(_idfa); // iOS 设备的 IDFA
            dto.set_idfv(_idfv); // iOS 设备的 IDFV
        }

        // 设置Properties
        OrderEventReportDTO.Properties properties = new OrderEventReportDTO.Properties();
        properties.set_order_id(orderNo); // 系统生成的订单ID
        properties.set_pay_amount(amount); // 支付金额
        properties.set_currency_type(currencyType); // 货币种类
        properties.set_pay_type(payType); // 支付方式
//        properties.setPur_type(transactionReason); // 支付方式
        // 根据平台设置platform和package_name
        if (platform == VipPlatform.ANDROID) {
            properties.set_platform(1); // android 1
            properties.set_package_name("nullart.ai"); // android包名
        } else if (platform == VipPlatform.IOS) {
            properties.set_platform(2); // ios 2
            properties.set_package_name("com.piclumen"); // ios包名
        }
        properties.set_app_version(appVersion);

        dto.setProperties(properties);

        return dto;
    }


    @Getter
    @Setter
    @ToString
    public static class OrderEventReportDTO {
        /*
            _appkey 16位 android ios 不同
         */
        private String _appkey;
        /**
         * 来源 固定 api
         */
        private String _source_type = "api";
        /**
         * 固定 event
         */
        private String _type = "event";

//        private String pur_type;
        /**
         * 数据存储区域 1 中国大陆 2 非中国大陆  服务器在硅谷。
         */
        private Integer _package_type = 2;

        /**
         * 事件名称 当前只上报移动端应用内购买 固定 _appPur
         */
        private String _event_name = "_appPur";

        /**
         * 用于数据排重复。 这里可以设置购买单据号。
         */
        private String _event_id;

        /**
         * 事件发生的13位时间戳 毫秒
         */
        private Long _ts;

        /**
         * 用户设备ID  服务端没有 传用户 userId
         */
        private String _distinct_id;


        /**
         * iOS 设备的 IDFA
         */
        private String _idfv;

        /**
         * iOS 设备的 IDFV
         */
        private String _idfa;


        /**
         * 设备AndroidId (Android)
         */
        private String _android_id;


        /**
         * userId
         */
        private String _account_id;
        /**
         * 用户ip
         */
        private String _ip;
        /**
         * 设备 UA 信息  服务端没有 传 android 或者 ios
         */
        private String _ua;

        private Properties properties;

        @Getter
        @Setter
        @ToString
        public static class Properties {

            /**
             * 系统生成的订单 ID
             */
            private String _order_id;
            /**
             * 本次购买支付的金额，单位：元
             */
            private Double _pay_amount;
            /**
             * 支付的货币种类，遵循《ISO 4217 国际标准》，如 CNY、USD
             */
            private String _currency_type;

//            private String pur_type;


            /**
             * 平台，枚举值：
             * 0：Other
             * 1：Android
             * 2：iOS
             * 3：Windows
             * 4：Mac
             * 8：devtools
             * 11：HarmonyOS
             * 12：Linux
             * <p>
             * android 1 ios 2
             */
            private Integer _platform;

            /**
             * _pay_status  支付状态 必传 1
             */
            private Integer _pay_status = 1;


            /**
             * App 包名或者进程名  android:  com.yile.ai    ios :  TODO 待定
             */
            private String _package_name;

            /**
             * _pay_type 支付方式：如 alipay、weixin、applepay、paypal 等
             */
            private String _pay_type;

            /**
             * app 版本  服务端没有 传 service
             */
            private String _app_version;
        }
    }


}
