package com.lx.pl.pay.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lx.pl.db.mysql.gen.entity.PayAppleProduct;
import com.lx.pl.db.mysql.gen.entity.StripeProduct;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.pay.apple.domain.PayApplePurchaseRecord;
import com.lx.pl.pay.common.domain.PayLumenRecord;
import com.lx.pl.pay.google.domain.GoogleOneTimePurchases;
import com.lx.pl.pay.google.domain.GooglePlayOrder;
import com.lx.pl.pay.google.domain.GoogleProduct;
import com.lx.pl.pay.google.domain.GoogleSubscriptionHistory;
import com.lx.pl.pay.paypal.model.PaymentCaptureDetails;
import com.lx.pl.pay.paypal.model.domain.PayPalLogicSubscription;
import com.lx.pl.pay.paypal.model.domain.PayPalOrderPaymentRecord;
import com.lx.pl.pay.paypal.model.domain.PayPalProduct;
import com.lx.pl.pay.stripe.domain.PayLogicPurchaseRecord;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Lumen 记录服务接口
 */
public interface PayLumenRecordService extends IService<PayLumenRecord> {

    void saveLumenRecord(PayLogicPurchaseRecord payLogicPurchaseRecord, StripeProduct stripeProduct);

    void recallLumen(Long payLogicPurchaseRecordId, String message);

    void saveOneTimeLumenRecord(List<PayLogicPurchaseRecord> payLogicPurchaseRecordList);

    void saveLumenRecordForApple(PayApplePurchaseRecord record, PayAppleProduct product);

    void saveOneTimeLumenRecordForApple(PayApplePurchaseRecord record);

    void restoreLumenByUserId(String originalTransactionId, Long newUserId, String loginName);

    void saveLumenRecordForPayPal(PayPalLogicSubscription payPalLogicSubscription, PayPalProduct paypalPlan);

    @Transactional(rollbackFor = Exception.class)
    void recallLumenForPaypal(Long orderId, String message);

    void saveOneTimeLumenForPaypal(List<PayPalOrderPaymentRecord> existRecords, PaymentCaptureDetails model);

    List<PayLumenRecord> queryByLogicId(Long logicId);

    void giftLumen(User user, Integer lumen);

    void invalidatedSubscriptionForPaypal(String srcSubscriptionId, Long id);

    void reActivedSubscriptionForPaypal(String subscriptionId, Long palLogicSubscriptionId);

    boolean hasPurchasedLumen(Long userId);

    void saveLumenRecordForGoogle(GoogleProduct googleProduct, GoogleSubscriptionHistory googleSubscriptionHistory);

    void cancelLumen(GoogleSubscriptionHistory history);

    void saveOneTimeLumenRecordForGoogle(GoogleOneTimePurchases googleOneTimePurchases, GooglePlayOrder googlePlayOrder);

    void updateExistRecordExpiretime(String latestOrderId, Long expiryTime);

    List<PayLumenRecord> findByLatestOrderId(String orderId);

    void cancelLumenByLatestOrderId(String orderId);

    // 可以添加一些自定义方法，例如：
    // List<PayLumenRecord> findByUserId(Long userId);
}
