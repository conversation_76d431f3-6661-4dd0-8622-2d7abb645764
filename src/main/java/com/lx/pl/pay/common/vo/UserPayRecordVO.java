package com.lx.pl.pay.common.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.lx.pl.pay.common.vo.serializer.LocalDateTimeToTimestampSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户支付记录展示VO
 */
@Data
@Schema(description = "用户支付记录展示")
public class UserPayRecordVO {

    /**
     * 记录ID
     */
    @Schema(description = "记录ID")
    private Long id;

    /**
     * 来源名称（Source Name）
     */
    @Schema(description = "来源名称，如：Subscribe, Purchase Lumen")
    private String sourceName;

    /**
     * 详情描述（Details）
     */
    @Schema(description = "详情描述，如：Standard Monthly, 500 Lumens")
    private String detail;

//    private List<UserPayRecordItemVo> items;

    /**
     * 费用（Cost）
     */
    @Schema(description = "费用")
    private Long amount;

    private String currency;

    @Schema(description = "折扣总价")
    private Long afterDiscountAmount;

    /**
     * 时间（Time）- UTC时间戳（秒）
     */
    @Schema(description = "时间 - UTC时间戳（秒）")
    @JsonSerialize(using = LocalDateTimeToTimestampSerializer.class)
    private LocalDateTime time;

    /**
     * 平台类型
     */
    @Schema(description = "平台类型：0 backend, 1 stripe, 2 paypal, 3 apple, 4 google")
    private Integer platform;

    /**
     * 平台名称
     */
    @Schema(description = "平台名称")
    private String platformName;

//    /**
//     * 产品类型
//     */
//    @Schema(description = "产品类型：plan 计划, one 购买lumen")
//    private String productType;
//
//    /**
//     * 计划等级
//     */
//    @Schema(description = "计划等级：standard/pro")
//    private String planLevel;
//
//    /**
//     * 价格间隔
//     */
//    @Schema(description = "价格间隔：month/year")
//    private String priceInterval;
//
//    /**
//     * 总Lumen数量
//     */
//    @Schema(description = "总Lumen数量")
//    private Integer totalLumen;
//
//    /**
//     * 购买数量
//     */
//    @Schema(description = "购买数量")
//    private Integer qty;
//
//    /**
//     * 是否有折扣
//     */
//    @Schema(description = "是否有折扣")
//    private Boolean hasDiscount;
//
//    /**
//     * 折扣百分比
//     */
//    @Schema(description = "折扣百分比")
//    private Integer percentOff;
//
//    /**
//     * 优惠码
//     */
//    @Schema(description = "优惠码")
//    private String couponCode;
}
