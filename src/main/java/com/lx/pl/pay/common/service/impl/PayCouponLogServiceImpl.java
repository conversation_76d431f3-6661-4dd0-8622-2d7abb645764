package com.lx.pl.pay.common.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lx.pl.db.mysql.gen.entity.StripeProduct;
import com.lx.pl.pay.common.domain.PayCouponLog;
import com.lx.pl.pay.common.mapper.PayCouponLogMapper;
import com.lx.pl.pay.common.service.PayCouponLogService;
import com.lx.pl.pay.common.util.PayConstant;
import com.lx.pl.pay.paypal.model.PaymentCaptureDetails;
import com.lx.pl.pay.paypal.model.domain.*;
import com.lx.pl.pay.paypal.service.PayPalLogicSubscriptionService;
import com.lx.pl.pay.paypal.service.PayPalOrderPaymentItemService;
import com.lx.pl.pay.paypal.service.PayPalProductService;
import com.lx.pl.pay.stripe.domain.StripeSubscriptionRecord;
import com.lx.pl.pay.stripe.domain.StripeUserCustomer;
import com.lx.pl.pay.stripe.dto.PaymentType;
import com.lx.pl.service.StripeProductService;
import com.paypal.base.rest.APIContext;
import com.stripe.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cn.hutool.core.text.CharSequenceUtil.upperFirst;
import static com.lx.pl.pay.paypal.service.impl.PayPalPayServiceImpl.calcuPriceAfterOff;

@Slf4j
@Service
public class PayCouponLogServiceImpl extends ServiceImpl<PayCouponLogMapper, PayCouponLog> implements PayCouponLogService {

    @Resource
    private StripeProductService stripeProductService;

    @Resource
    private PayPalProductService payPalProductService;

    @Resource
    private PayPalOrderPaymentItemService payPalOrderPaymentItemService;

    @Resource
    private PayPalLogicSubscriptionService payPalLogicSubscriptionService;

    @Resource
    private APIContext apiContext;

    @Override
    public long countByCouponId(String coupon) {
        return this.lambdaQuery().eq(PayCouponLog::getCouponCode, coupon).count();
    }

    @Override
    public void saveLogIfNeed(StripeSubscriptionRecord one, Subscription subscription, StripeProduct product) {
        try {
            Discount discount = subscription.getDiscount();
            Coupon coupon = discount.getCoupon();
            if (coupon == null) {
                log.info("coupon is null");
                return;
            }

            Map<String, String> metadata = subscription.getMetadata();
            String payCouponCode = metadata.get(PayConstant.PAY_COUPON_CODE_META_KEY);

            PayCouponLog coupon1 = this.lambdaQuery().eq(PayCouponLog::getUserId, one.getUserId())
                    .eq(PayCouponLog::getCouponCode, payCouponCode)
                    .eq(PayCouponLog::getType, "coupon")
                    .one();
            if (coupon1 != null) {
                log.info("coupon log already exists");
                return;
            }
            PayCouponLog payCouponLog = new PayCouponLog();
            payCouponLog.setCouponCode(payCouponCode);
            payCouponLog.setUserId(one.getUserId());
            payCouponLog.setLoginName(one.getLoginName());
            payCouponLog.setUsedTime(discount.getStart());
            payCouponLog.setPriceId(one.getPriceId());
            payCouponLog.setPlanId(one.getSubscriptionId());
            String type = "coupon";
            payCouponLog.setProductName(product.getPlanLevel() + " " + product.getPriceInterval());
            payCouponLog.setType(type);
            String price = product.getPrice();
            payCouponLog.setSrcAmount(price);
            BigDecimal percentOff = coupon.getPercentOff();
            payCouponLog.setDiscountAmount(new BigDecimal(price).multiply(percentOff).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP).toString());
            this.save(payCouponLog);
            log.info("end saveLogIfNeed {}", payCouponLog);
        } catch (Exception e) {
            log.error("saveLogIfNeed error", e);
        }
    }

    @Override
    public void saveLogIfNeed(Invoice event, StripeUserCustomer userCustomer) {
        try {
            InvoiceLineItemCollection lines = event.getLines();
            if (lines == null) {
                log.info("lines is null");
                return;
            }
            List<InvoiceLineItem> data = lines.getData();
            if (data == null || data.isEmpty()) {
                log.info("data is null");
                return;
            }
            InvoiceLineItem invoiceLineItem = data.get(0);
            Price price = invoiceLineItem.getPrice();
            if (price == null) {
                log.info("price is null");
                return;
            }
            Discount discount = event.getDiscount();
            if (discount == null) {
                log.info("discount is null");
                return;
            }
            Coupon coupon = discount.getCoupon();
            if (coupon == null) {
                log.info("coupon is null");
                return;
            }

            Map<String, String> metadata = invoiceLineItem.getMetadata();
            if (CollUtil.isEmpty(metadata)) {
                Invoice.SubscriptionDetails subscriptionDetails = event.getSubscriptionDetails();
                if (subscriptionDetails != null) {
                    metadata = subscriptionDetails.getMetadata();
                }
                if (metadata == null) {
                    metadata = new HashMap<>();
                }
            }

            String payCouponCode = metadata.get(PayConstant.PAY_COUPON_CODE_META_KEY);
            String off = metadata.get(PayConstant.OFF_META_KEY);
            String promotionType = metadata.get(PayConstant.PROMOTION_TYPE_META_KEY);

            PayCouponLog coupon1 = this.lambdaQuery()
                    .eq(PayCouponLog::getUserId, userCustomer.getUserId())
                    .eq(PayCouponLog::getRelationId, event.getId())
                    .one();
            if (coupon1 != null) {
                log.info("coupon log already exists");
                return;
            }
//            if (off == null) {
//                log.info("off is null");
//                off = coupon.getPercentOff().toString();
//                return;
//            }
            PayCouponLog payCouponLog = new PayCouponLog();
            payCouponLog.setCouponCode(payCouponCode);
            payCouponLog.setUserId(userCustomer.getUserId());
            payCouponLog.setLoginName(userCustomer.getLoginName());
            payCouponLog.setUsedTime(discount.getStart());
            payCouponLog.setPriceId(price.getId());
            payCouponLog.setPlanId(invoiceLineItem.getSubscription());
            StripeProduct stripeProductByPriceId = stripeProductService.getStripeProductByPriceId(price.getId());
            payCouponLog.setProductType(stripeProductByPriceId.getProductType());
            payCouponLog.setProductName(upperFirst(stripeProductByPriceId.getPlanLevel()) + "_" + upperFirst(stripeProductByPriceId.getPriceInterval()));
            if (invoiceLineItem.getAmount() == 0 || (invoiceLineItem.getDescription() != null && invoiceLineItem.getDescription().toLowerCase().startsWith("trial"))) {
                payCouponLog.setSrcAmount("0");
                payCouponLog.setDiscountAmount("0");
                payCouponLog.setAmount("0");
                log.info("amount is 0");
            } else {
                payCouponLog.setSrcAmount(stripeProductByPriceId.getPrice());
//                BigDecimal percentOff = BigDecimal.valueOf(100 - Integer.parseInt(off)).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
//                BigDecimal bigDecimal = new BigDecimal(stripeProductByPriceId.getPrice()).multiply(percentOff).setScale(2, RoundingMode.HALF_DOWN);
//                payCouponLog.setDiscountAmount(bigDecimal.toString());
                List<InvoiceLineItem.DiscountAmount> discountAmounts = invoiceLineItem.getDiscountAmounts();
                if (discountAmounts != null && !discountAmounts.isEmpty()) {
                    BigDecimal divide = new BigDecimal(discountAmounts.get(0).getAmount()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
                    payCouponLog.setDiscountAmount(divide.toString());
                } else {
                    payCouponLog.setDiscountAmount("0");
                }
                payCouponLog.setAmount(new BigDecimal(stripeProductByPriceId.getPrice()).subtract(new BigDecimal(payCouponLog.getDiscountAmount())).toString());
            }
            payCouponLog.setType(promotionType);

            payCouponLog.setRelationId(event.getId());
            payCouponLog.setOff(off);
            payCouponLog.setQty(1);
            payCouponLog.setCreateTime(LocalDateTime.now());
            log.info("end saveLogIfNeed {}", payCouponLog);
            this.save(payCouponLog);
        } catch (Exception e) {
            log.error("saveLogIfNeed error", e);
        }
    }

    @Override
    public long countByCouponIdAndUserId(String code, Long id, PaymentType paymentType) {
        return this.lambdaQuery().eq(PayCouponLog::getCouponCode, code)
                .eq(PayCouponLog::getType, paymentType.getType())
                .eq(PayCouponLog::getProductType, paymentType.getType())
                .eq(PayCouponLog::getUserId, id)
                .count();
    }

    @Override
    public void saveLumenLogIfNeed(Map<String, Integer> priceQtyMap, Invoice event, StripeUserCustomer userCustomer) {
        try {
            Discount discount = event.getDiscount();
            Coupon coupon = discount.getCoupon();
            if (coupon == null) {
                log.info("coupon is null");
                return;
            }
            long coupon1 = this.lambdaQuery().eq(PayCouponLog::getUserId, userCustomer.getUserId())
                    .eq(PayCouponLog::getRelationId, event.getId())
                    .count();
            if (coupon1 > 0) {
                log.info("coupon log already exists");
                return;
            }

            Map<String, String> metadata = event.getMetadata();
            if (metadata == null) {
                log.info("metadata is null");
                return;
            }


            String payCouponCode = metadata.get(PayConstant.PAY_COUPON_CODE_META_KEY);
            String off = metadata.get(PayConstant.OFF_META_KEY);
            String promotionType = metadata.get(PayConstant.PROMOTION_TYPE_META_KEY);

            for (InvoiceLineItem datum : event.getLines().getData()) {
                String priceId = datum.getPrice().getId();
                Integer quantity = datum.getQuantity().intValue();
                if (payCouponCode != null) {
                    StripeProduct product = stripeProductService.getStripeProductByPriceId(priceId);

                    PayCouponLog payCouponLog = new PayCouponLog();
                    payCouponLog.setOff(off);
                    payCouponLog.setCouponCode(payCouponCode);
                    payCouponLog.setRelationId(event.getId());
                    payCouponLog.setUserId(userCustomer.getUserId());
                    payCouponLog.setLoginName(userCustomer.getLoginName());
                    payCouponLog.setUsedTime(discount.getStart());
                    payCouponLog.setPriceId(priceId);
                    payCouponLog.setPlanId(null);
                    payCouponLog.setProductName(product.getLumen() + " lumen");
                    payCouponLog.setProductType(product.getProductType());
                    payCouponLog.setType(promotionType);
                    String price = product.getPrice();
                    BigDecimal totalSrc = new BigDecimal(price).multiply(new BigDecimal(quantity));
                    payCouponLog.setSrcAmount(totalSrc.toString());
                    List<InvoiceLineItem.DiscountAmount> discountAmounts = datum.getDiscountAmounts();
                    if (discountAmounts != null && !discountAmounts.isEmpty()) {
                        payCouponLog.setDiscountAmount(new BigDecimal(discountAmounts.get(0).getAmount()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP).toString());
                    }
//                    BigDecimal percentOff = coupon.getPercentOff();
//                    payCouponLog.setDiscountAmount(totalSrc.multiply(percentOff.divide(new BigDecimal(100), 2, RoundingMode.HALF_UP)).setScale(2, RoundingMode.HALF_DOWN).toString());
                    payCouponLog.setAmount(totalSrc.subtract(new BigDecimal(payCouponLog.getDiscountAmount())).toString());
                    payCouponLog.setQty(quantity);
                    payCouponLog.setCreateTime(LocalDateTime.now());
                    this.save(payCouponLog);
                    log.info("end saveLumenLogIfNeed {}", payCouponLog);
                } else {
                    StripeProduct product = stripeProductService.getStripeProductByPriceId(priceId);
                    PayCouponLog payCouponLog = new PayCouponLog();
                    payCouponLog.setOff(off);
                    payCouponLog.setRelationId(event.getId());
                    payCouponLog.setUserId(userCustomer.getUserId());
                    payCouponLog.setLoginName(userCustomer.getLoginName());
                    payCouponLog.setUsedTime(discount.getStart());
                    payCouponLog.setPriceId(priceId);
                    payCouponLog.setProductType(product.getProductType());
                    payCouponLog.setPlanId(null);
                    payCouponLog.setProductName(product.getLumen() + " lumen");
                    payCouponLog.setType(promotionType);
                    String price = product.getPrice();
                    BigDecimal totalSrc = new BigDecimal(price).multiply(new BigDecimal(quantity));
                    payCouponLog.setSrcAmount(totalSrc.toString());
                    List<InvoiceLineItem.DiscountAmount> discountAmounts = datum.getDiscountAmounts();
                    if (discountAmounts != null && !discountAmounts.isEmpty()) {
                        payCouponLog.setDiscountAmount(new BigDecimal(discountAmounts.get(0).getAmount()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP).toString());
                    }
//                    BigDecimal percentOff = coupon.getPercentOff();
//                    payCouponLog.setDiscountAmount(totalSrc.multiply(percentOff.divide(new BigDecimal(100), 2, RoundingMode.HALF_UP)).setScale(2, RoundingMode.HALF_DOWN).toString());
                    payCouponLog.setAmount(totalSrc.subtract(new BigDecimal(payCouponLog.getDiscountAmount())).toString());
                    payCouponLog.setQty(quantity);
                    payCouponLog.setCreateTime(LocalDateTime.now());
                    log.info("end saveLumenLogIfNeed {}", payCouponLog);
                    this.save(payCouponLog);
                }
            }
        } catch (Exception e) {
            log.error("saveLumenLogIfNeed error", e);
        }
    }

    @Override
    public void savePaypalLogIfNeed(List<PayPalOrderPaymentRecord> orderRecords, PaymentCaptureDetails model) {
        try {
            if (orderRecords == null || orderRecords.isEmpty()) {
                log.info("payPalOrderPaymentRecords is null or empty");
                return;
            }

            PayPalOrderPaymentRecord firstRecord = orderRecords.get(0);
            String orderId = firstRecord.getOrderId();
            Long userId = firstRecord.getUserId();
            String loginName = firstRecord.getLoginName();

            // 从PayPal API获取原始订单数据以提取优惠券信息
            String customId = model.getCustomId();
            if (customId == null) {
                log.info("customId is null");
                return;
            }
            JSONObject metadata = JSONUtil.parseObj(customId);
            String payCouponCode = metadata.getStr(PayConstant.PAY_COUPON_CODE_META_KEY);
            String off = metadata.getStr(PayConstant.OFF_META_KEY);
            String  promotionType = metadata.getStr(PayConstant.PROMOTION_TYPE_META_KEY);
            log.info("Extracted coupon metadata from PayPal order: couponCode={}, off={}, promotionType={}",
                    payCouponCode, off, promotionType);

            // 如果没有任何优惠信息，则不记录日志
            if (StrUtil.isBlank(promotionType)) {
                log.info("No coupon or discount information found for orderId: {}", orderId);
                return;
            }

            // 检查是否已经记录过优惠券日志
            long existingLogCount = this.lambdaQuery()
                    .eq(PayCouponLog::getUserId, userId)
                    .eq(PayCouponLog::getRelationId, orderId)
                    .count();
            if (existingLogCount > 0) {
                log.info("PayPal coupon log already exists for orderId: {}, userId: {}", orderId, userId);
                return;
            }

            // 获取订单项目信息
            List<PayPalOrderPaymentItem> orderItems = payPalOrderPaymentItemService.findByOrderId(orderId);
            if (orderItems == null || orderItems.isEmpty()) {
                log.info("No order items found for orderId: {}", orderId);
                return;
            }
            // 为每个订单项目创建优惠券日志
            for (PayPalOrderPaymentItem orderItem : orderItems) {
                String productId = orderItem.getProductId();
                Integer qty = orderItem.getQty();

                // 根据产品ID获取产品信息
                PayPalProduct product = payPalProductService.lambdaQuery()
                        .eq(PayPalProduct::getProductId, productId)
                        .eq(PayPalProduct::getStatus, true)
                        .one();

                if (product == null) {
                    log.warn("PayPal product not found for productId: {}", productId);
                    continue;
                }

                PayCouponLog payCouponLog = new PayCouponLog();
                payCouponLog.setCouponCode(payCouponCode);
                payCouponLog.setOff(off);
                payCouponLog.setRelationId(orderId);
                payCouponLog.setUserId(userId);
                payCouponLog.setLoginName(loginName);
                // 当前时间戳（秒）
                payCouponLog.setUsedTime(System.currentTimeMillis() / 1000);
                payCouponLog.setPriceId(productId);
                // PayPal一次性购买没有planId
                payCouponLog.setPlanId(null);
                payCouponLog.setProductName(product.getLumen() + " lumen");
                payCouponLog.setProductType(product.getProductType());
                payCouponLog.setType(promotionType != null ? promotionType : "paypal_discount");

                // 计算原价和折扣金额
                String productPrice = product.getPrice();
                BigDecimal totalSrc = new BigDecimal(productPrice).multiply(new BigDecimal(qty));
                payCouponLog.setSrcAmount(totalSrc.toString());

                // 按比例分配折扣金额
                BigDecimal priceAfterOff = calcuPriceAfterOff(product, off != null ? Integer.parseInt(off) : 0, qty);
                payCouponLog.setDiscountAmount(String.valueOf(totalSrc.subtract(priceAfterOff)));

                // 计算实际支付金额
                payCouponLog.setAmount(String.valueOf(priceAfterOff));

                payCouponLog.setQty(qty);
                payCouponLog.setCreateTime(LocalDateTime.now());

                this.save(payCouponLog);
                log.info("Saved PayPal coupon log: {}", payCouponLog);
            }
        } catch (Exception e) {
            log.error("savePaypalLogIfNeed error", e);
        }
    }

    @Override
    public void savePaypalSubLogIfNeed(PayPalSubPaymentRecord payPalSubPaymentRecord, String customId) {
        try {
            if (payPalSubPaymentRecord == null) {
                log.info("payPalSubPaymentRecord is null");
                return;
            }

            String subscriptionId = payPalSubPaymentRecord.getSubscriptionId();
            String paymentId = payPalSubPaymentRecord.getPaymentId();
            Long userId = payPalSubPaymentRecord.getUserId();
            String loginName = payPalSubPaymentRecord.getLoginName();
// 从PayPal API获取原始订单数据以提取优惠券信息
            if (customId == null) {
                log.info("customId is null");
                return;
            }
            JSONObject metadata = JSONUtil.parseObj(customId);
            String payCouponCode = metadata.getStr(PayConstant.PAY_COUPON_CODE_META_KEY);
            String off = metadata.getStr(PayConstant.OFF_META_KEY);
            String  promotionType = metadata.getStr(PayConstant.PROMOTION_TYPE_META_KEY);
            log.info("Extracted coupon metadata from PayPal subscription: couponCode={}, off={}, promotionType={}",
                    payCouponCode, off, promotionType);

            // 如果没有任何优惠信息，则不记录日志
            if (StrUtil.isBlank(promotionType) && StrUtil.isBlank(payCouponCode)) {
                log.info("No coupon or discount information found for paymentId: {}", paymentId);
                return;
            }
            // 检查是否已经记录过优惠券日志
            long existingLogCount = this.lambdaQuery()
                    .eq(PayCouponLog::getUserId, userId)
                    .eq(PayCouponLog::getRelationId, paymentId)
                    .count();
            if (existingLogCount > 0) {
                log.info("PayPal subscription coupon log already exists for paymentId: {}, userId: {}", paymentId, userId);
                return;
            }

            // 获取订阅逻辑信息
            PayPalLogicSubscription logicSubscription = payPalLogicSubscriptionService.queryBySubscriptionId(subscriptionId);
            if (logicSubscription == null) {
                log.info("PayPal logic subscription not found for subscriptionId: {}", subscriptionId);
                return;
            }

            // 获取产品信息
            PayPalProduct product = payPalProductService.getPaypalProductByPlanId(logicSubscription.getPlanId());
            if (product == null) {
                log.warn("PayPal product not found for planId: {}", logicSubscription.getPlanId());
                return;
            }

            // 检查是否有折扣（这里需要根据实际业务逻辑判断是否有优惠）
//            String subtotal = payPalSubPaymentRecord.getSubtotal();
            String total = payPalSubPaymentRecord.getTotal();

            BigDecimal srcAmount = new BigDecimal(product.getPrice());
            BigDecimal totalAmount = new BigDecimal(total);
            BigDecimal discountAmount = srcAmount.subtract(totalAmount);

            if (discountAmount.compareTo(BigDecimal.ZERO) <= 0) {
                log.info("No discount found for subscription payment: {}", paymentId);
                return;
            }

            // 创建优惠券日志
            PayCouponLog payCouponLog = new PayCouponLog();
            payCouponLog.setCouponCode(payCouponCode);
            payCouponLog.setOff(off);
            payCouponLog.setRelationId(paymentId);
            payCouponLog.setUserId(userId);
            payCouponLog.setLoginName(loginName);
            payCouponLog.setUsedTime(System.currentTimeMillis() / 1000);
            payCouponLog.setPriceId(product.getPaypalPlanId());
            payCouponLog.setPlanId(subscriptionId);
            payCouponLog.setProductName(upperFirst(product.getPlanLevel()) + "_" + upperFirst(product.getPriceInterval()));
            payCouponLog.setProductType(product.getProductType());
            payCouponLog.setType(promotionType != null ? promotionType : "paypal_subscription_discount");
            payCouponLog.setSrcAmount(String.valueOf(srcAmount));
            payCouponLog.setDiscountAmount(String.valueOf(discountAmount));
            payCouponLog.setAmount(total);
            payCouponLog.setQty(1);
            payCouponLog.setCreateTime(LocalDateTime.now());

            this.save(payCouponLog);
            log.info("Saved PayPal subscription coupon log: {}", payCouponLog);
        } catch (Exception e) {
            log.error("savePaypalSubLogIfNeed error", e);
        }
    }
}