package com.lx.pl.pay.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 支付详情枚举
 */
@Getter
@AllArgsConstructor
public enum LumenRecordDetailEnum {
    /**
     * 专业年度订阅
     */
    PURCHASE_LUMENS("Lumens package", " 购买Lumens", PaymentSourceEnum.SUBSCRIBE),

    /**
     * 会员订阅每月赠送的Lumens
     */
    SUBSCRIPTION_MONTHLY_LUMENS("%s subscription month lumens", "会员订阅每月赠送的Lumens", PaymentSourceEnum.SUBSCRIBE),


    /**
     * 会员订阅每月回收的Lumens
     */
    SUBSCRIPTION_MONTHLY_LUMENS_RECYCLE("%s subscription month lumens recycle", "会员订阅回收的Lumens", PaymentSourceEnum.SUBSCRIBE),
    LUMENS_RESTORE("Restore lumens", "会员订阅回收的Lumens", PaymentSourceEnum.SUBSCRIBE);
    /**
     * 详情名称（英文）
     */
    private final String name;

    /**
     * 详情描述（中文）
     */
    private final String description;

    /**
     * 所属来源
     */
    private final PaymentSourceEnum source;

    /**
     * 根据名称获取枚举
     *
     * @param name 详情名称
     * @return 枚举对象，如果不存在返回null
     */
    public static LumenRecordDetailEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        for (LumenRecordDetailEnum detail : values()) {
            if (detail.getName().equalsIgnoreCase(name)) {
                return detail;
            }
        }
        return null;
    }

    /**
     * 根据来源获取详情列表
     *
     * @param source 来源枚举
     * @return 该来源下的所有详情枚举数组
     */
    public static LumenRecordDetailEnum[] getBySource(PaymentSourceEnum source) {
        if (source == null) {
            return new LumenRecordDetailEnum[0];
        }
        return java.util.Arrays.stream(values()).filter(detail -> detail.getSource() == source).toArray(LumenRecordDetailEnum[]::new);
    }

    /**
     * 检查名称是否有效
     *
     * @param name 详情名称
     * @return 是否有效
     */
    public static boolean isValidName(String name) {
        return getByName(name) != null;
    }
}