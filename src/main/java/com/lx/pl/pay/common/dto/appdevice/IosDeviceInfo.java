package com.lx.pl.pay.common.dto.appdevice;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IosDeviceInfo {
    /**



     _appkey	SE平台分配的appKey，开发者初始化传入
     _distinct_id	SE生成的设备ID(IDFV)
     _account_id	开发者通过login接口传入的accountID
     _visitor_id	开发者通过setVisitorID接口传入的visitorID
     _session_id	SE内部每次冷启动生成的sessionID
     _idfa	设备IDFA
     _idfv	设备的IDFV
     _ua	设备的UA
     _language	设备的系统设置的语言
     _time_zone	设备的时区
     _manufacturer	设备生成厂商
     _platform	SDK平台，固定字段2
     _os_version	设备系统版本
     _screen_height	屏幕高
     _screen_width	屏幕宽
     _device_model	设备型号，
     _device_type	设备类型，3：iphone，4：ipad，0：其它
     _app_version	应用版本号
     _app_version_code	应用build号
     _package_name	应用包名
     _app_name	应用名称
     _channel	固定字段：AppStore
     _lib	固定字段：2
     _lib_version	SDK版本号



     */


    /**
     * SE平台分配的appKey，开发者初始化传入
     */
    private String _appkey;

    /**
     * SE生成的设备ID(IDFV)
     */
    private String _distinct_id;

    /**
     * 开发者通过login接口传入的accountID
     */
    private String _account_id;

    /**
     * 开发者通过setVisitorID接口传入的visitorID
     */
    private String _visitor_id;

    /**
     * SE内部每次冷启动生成的sessionID
     */
    private String _session_id;

    /**
     * 设备IDFA
     */
    private String _idfa;

    /**
     * 设备的IDFV
     */
    private String _idfv;

    /**
     * 设备的UA
     */
    private String _ua;

    /**
     * 设备的系统设置的语言
     */
    private String _language;

    /**
     * 设备的时区
     */
    private String _time_zone;

    /**
     * 设备生成厂商
     */
    private String _manufacturer;

    /**
     * SDK平台，固定字段2
     */
    private Integer _platform;

    /**
     * 设备系统版本
     */
    private String _os_version;


    /**
     * 应用版本号
     */
    private String _app_version;

    /**
     * 应用版本code
     */
    private String _app_version_code;


}
