package com.lx.pl.pay.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lx.pl.dto.CommPageInfo;
import com.lx.pl.pay.common.domain.UserPayRecord;
import com.lx.pl.pay.common.dto.SavePaymentRecordRequest;
import com.lx.pl.pay.common.vo.UserPayRecordVO;

import java.util.List;

/**
 * 用户支付记录服务接口
 */
public interface UserPayRecordService extends IService<UserPayRecord> {

    /**
     * 根据用户ID查询支付记录
     *
     * @param userId 用户ID
     * @return 支付记录列表
     */
    List<UserPayRecord> getByUserId(Long userId);

    /**
     * 根据登录名查询支付记录
     *
     * @param loginName 登录名
     * @return 支付记录列表
     */
    List<UserPayRecord> getByLoginName(String loginName);

    /**
     * 根据平台类型查询支付记录
     *
     * @param platform 平台类型
     * @return 支付记录列表
     */
    List<UserPayRecord> getByPlatform(Integer platform);

    /**
     * 根据用户ID和平台类型查询支付记录
     *
     * @param userId   用户ID
     * @param platform 平台类型
     * @return 支付记录列表
     */
    List<UserPayRecord> getByUserIdAndPlatform(Long userId, Integer platform);

    /**
     * 根据用户ID分页查询支付记录（游标分页）
     *
     * @param userId   用户ID
     * @param lastId   上一页最后一条记录的ID（游标）
     * @param pageSize 每页大小
     * @param platform 平台类型（可选）
     * @return 分页结果
     */
    CommPageInfo<UserPayRecord> getByUserIdWithCursor(Long userId, Long lastId, Integer pageSize, Integer platform);

    /**
     * 根据用户ID分页查询支付记录VO（游标分页）
     *
     * @param userId   用户ID
     * @param lastId   上一页最后一条记录的ID（游标）
     * @param pageSize 每页大小
     * @param platform 平台类型（可选）
     * @return 分页结果VO
     */
    CommPageInfo<UserPayRecordVO> getByUserIdWithCursorVO(Long userId, Long lastId, Integer pageSize, Integer platform);

    /**
     * 保存支付记录（包含幂等校验）
     *
     * @param request 支付记录请求
     * @return 保存的支付记录（如果已存在则返回现有记录）
     */
    UserPayRecord savePaymentRecord(SavePaymentRecordRequest request);

    /**
     * 检查支付记录是否已存在（幂等校验）
     *
     * @param request 支付记录请求
     * @return 如果已存在返回现有记录，否则返回null
     */
    UserPayRecord checkIdempotency(SavePaymentRecordRequest request);

    /**
     * 根据平台和外部交易ID检查是否已存在
     *
     * @param platform              平台类型
     * @param externalTransactionId 外部交易ID
     * @return 如果已存在返回现有记录，否则返回null
     */
    UserPayRecord checkByExternalTransactionId(Integer platform, String externalTransactionId);

    /**
     * 根据平台、用户ID和外部交易ID检查是否已存在
     *
     * @param platform              平台类型
     * @param userId                用户ID
     * @param externalTransactionId 外部交易ID
     * @return 如果已存在返回现有记录，否则返回null
     */
    UserPayRecord checkByUserAndExternalTransactionId(Integer platform, Long userId, String externalTransactionId);

    /**
     * 根据平台、外部交易ID和外部订单号检查是否已存在（双重校验）
     *
     * @param platform              平台类型
     * @param externalTransactionId 外部交易ID
     * @param externalOrderId       外部订单号
     * @return 如果已存在返回现有记录，否则返回null
     */
    UserPayRecord checkByExternalIds(Integer platform, String externalTransactionId, String externalOrderId);

    /**
     * Stripe平台专用幂等校验
     * 基于payment_intent_id或invoice_id
     *
     * @param invoiceId Stripe发票ID
     * @return 如果已存在返回现有记录，否则返回null
     */
    UserPayRecord checkStripeIdempotency(String invoiceId);

    /**
     * PayPal平台专用幂等校验
     * 基于order_id或subscription_id
     *
     * @param orderId        PayPal订单ID
     * @param subscriptionId PayPal订阅ID
     * @param d              PayPal付款人ID
     * @return 如果已存在返回现有记录，否则返回null
     */
    UserPayRecord checkPayPalIdempotency(String subscriptionId, String orderId);

    /**
     * Apple平台专用幂等校验
     * 基于original_transaction_id
     *
     * @param originalTransactionId Apple原始交易ID
     * @param transactionId         Apple交易ID
     * @return 如果已存在返回现有记录，否则返回null
     */
    UserPayRecord checkAppleIdempotency(String originalTransactionId, String transactionId);

    /**
     * Google平台专用幂等校验
     * 基于purchase_token
     *
     * @param purchaseToken Google购买令牌
     * @param orderId       Google订单ID
     * @return 如果已存在返回现有记录，否则返回null
     */
    UserPayRecord checkGoogleIdempotency(String purchaseToken, String orderId);

}
