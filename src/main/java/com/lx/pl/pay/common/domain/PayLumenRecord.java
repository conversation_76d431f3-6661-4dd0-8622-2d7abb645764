package com.lx.pl.pay.common.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lx.pl.db.mysql.gen.entity.MyBaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * Lumen 记录实体类
 */
@Data
@TableName("pay_lumen_record")
public class PayLumenRecord extends MyBaseEntity {

    /**
     * 主键 ID
     */
    @Schema(description = "主键 ID")
    @TableId(value = "id")
    private Long id;

    /**
     * 用户 ID
     */
    @Schema(description = "用户 ID")
    private Long userId;

    /**
     * 用户账号
     */
    @Schema(description = "用户账号")
    private String loginName;

    /**
     * Stripe 客户 ID
     */
    @Schema(description = "Stripe 客户 ID")
    private String customerId;

    private String originalTransactionId;

    private String transactionId;


    private Long payLogicPurchaseRecordId;

    private String orderId;


    @Schema(description = "Google 订单 ID")
    private String latestOrderId;

    /**
     * Lumen 到期时间
     */
    @Schema(description = "Lumen 到期时间")
    private Long currentPeriodEnd;

    /**
     * Lumen 获得时间
     */
    @Schema(description = "Lumen 获得时间")
    private Long currentPeriodStart;

    /**
     * 记录类型：1: 一次性、2: VIP、3: 礼品
     */
    @Schema(description = "记录类型：1: 一次性、2: VIP、3: 礼品")
    private Integer type;

    /**
     * 逻辑订阅每周期结束时间
     */
    @Schema(description = "逻辑订阅每周期结束时间")
    private Long logicPeriodEnd;

    /**
     * 逻辑订阅每周期开始时间
     */
    @Schema(description = "逻辑订阅每周期开始时间")
    private Long logicPeriodStart;

    /**
     * Lumen 原有数量
     */
    @Schema(description = "Lumen 原有数量")
    private Integer lumenQty;

    /**
     * Lumen 剩余数量
     */
    @Schema(description = "Lumen 剩余数量")
    private Integer lumenLeftQty;

    /**
     * 是否为混合
     */
    @Schema(description = "是否为混合")
    private Boolean mixed;

    /**
     * 是否无效，存在取消的情况
     */
    @Schema(description = "是否无效，存在取消的情况")
    private Boolean invalid;

    @Schema(description = "取消的情况说明")
    private String invalidMessage;

    private String vipPlatForm;
}
