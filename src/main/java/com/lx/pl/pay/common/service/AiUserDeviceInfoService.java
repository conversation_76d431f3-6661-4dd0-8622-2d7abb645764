package com.lx.pl.pay.common.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.dto.UserDeviceInfoReportReq;
import com.lx.pl.pay.common.domain.AiUserDeviceInfoPO;
import com.lx.pl.pay.common.mapper.AiUserDeviceInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service
@Slf4j
public class AiUserDeviceInfoService {

    @Autowired
    private AiUserDeviceInfoMapper aiUserDeviceInfoDao;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 上报移动端用户设备信息
     *
     * @param req      用户设备信息上报请求
     * @param user
     * @param platform
     */
    public void deviceInfoReport(UserDeviceInfoReportReq req, User user, String platform) {
        Long userId = user.getId();
        // 1. 先查询是否存在该设备信息
        LambdaQueryWrapper<AiUserDeviceInfoPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiUserDeviceInfoPO::getUserId, userId)
                .eq(AiUserDeviceInfoPO::getPlatform, platform)
                .last("limit 1");

        AiUserDeviceInfoPO existingDeviceInfo = aiUserDeviceInfoDao.selectOne(queryWrapper);
        String deviceInfoJson;
        try {
            deviceInfoJson = objectMapper.writeValueAsString(req);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        if (existingDeviceInfo != null) {
            // 2. 如果存在则更新设备信息
            existingDeviceInfo.setDeviceInfo(deviceInfoJson);
            existingDeviceInfo.setUpdateTime(LocalDateTime.now());
            aiUserDeviceInfoDao.updateById(existingDeviceInfo);
            log.info("Updated device info for userId: {}, platform: {}", userId, platform);
        } else {
            // 3. 如果不存在则插入新的设备信息
            AiUserDeviceInfoPO newDeviceInfo = new AiUserDeviceInfoPO();
            newDeviceInfo.setUserId(userId);
            newDeviceInfo.setPlatform(platform);
            newDeviceInfo.setDeviceInfo(deviceInfoJson);
            newDeviceInfo.setCreateBy(user.getLoginName());
            newDeviceInfo.setUpdateBy(user.getLoginName());
            newDeviceInfo.setCreateTime(LocalDateTime.now());
            newDeviceInfo.setUpdateTime(LocalDateTime.now());

            try {
                aiUserDeviceInfoDao.insert(newDeviceInfo);
            } catch (Exception e) {
                // 唯一约束 + 并发提交 导致的插入异常  忽略
                log.error("Error inserting device info for userId: {}, platform: {}, error: {}", userId, platform, e.getMessage());
            }
            log.info("Inserted new device info for userId: {}, platform: {}", userId, platform);
        }
    }
}
