package com.lx.pl.pay.common.service.impl;

import com.apple.itunes.storekit.model.JWSTransactionDecodedPayload;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lx.pl.db.mysql.gen.entity.PayAppleProduct;
import com.lx.pl.db.mysql.gen.entity.StripeProduct;
import com.lx.pl.enums.VipType;
import com.lx.pl.exception.PayAppleException;
import com.lx.pl.pay.apple.VipPlatform;
import com.lx.pl.pay.apple.service.PayAppleProductService;
import com.lx.pl.pay.common.domain.SubscriptionCurrent;
import com.lx.pl.pay.common.mapper.SubscriptionCurrentMapper;
import com.lx.pl.pay.common.service.SubscriptionCurrentService;
import com.lx.pl.pay.google.domain.GoogleSubscriptions;
import com.lx.pl.pay.stripe.domain.StripeSubscriptionRecord;
import com.lx.pl.service.CommMessageService;
import com.lx.pl.service.RedisService;
import com.stripe.model.Coupon;
import com.stripe.model.Discount;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.lx.pl.enums.AppleErrorCode.PRODUCT_NOT_FOUND;

/**
 * SubscriptionCurrent服务实现类
 */
@Service
public class SubscriptionCurrentServiceImpl extends ServiceImpl<SubscriptionCurrentMapper, SubscriptionCurrent> implements SubscriptionCurrentService {
    Logger log = LoggerFactory.getLogger("stripe-pay-msg");
    protected Logger logApple = LoggerFactory.getLogger("apple-pay-msg");
    protected Logger logGoogle = LoggerFactory.getLogger("google-pay-msg");

    @Resource
    private SubscriptionCurrentMapper subscriptionCurrentMapper;
    @Autowired
    private RedisService redisService;
    @Autowired
    private PayAppleProductService payAppleProductService;
    @Resource(name = "logicObjectMapper")
    private ObjectMapper logicObjectMapper;


    // 缓存前缀
    public static final String CACHE_KEY_PREFIX = "user:subscriptions:";
    // 缓存前缀
    private static final String CACHE_REAL_SUBSCRIPTIONS_PREFIX = "user:realSubscriptions:";

    @Resource
    private CommMessageService commMessageService;

    @Override
    public List<SubscriptionCurrent> getLogicValidSubscriptionsFromDb(Long userId) {
        long currentTime = System.currentTimeMillis() / 1000; // 当前时间（秒级时间戳）

        // 构建 Lambda 查询条件
        LambdaQueryWrapper<SubscriptionCurrent> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SubscriptionCurrent::getUserId, userId) // user_id = userId
                .eq(SubscriptionCurrent::getInvalid, 0)
                .le(SubscriptionCurrent::getVipBeginTime, currentTime) // vip_begin_time <= currentTime
                .ge(SubscriptionCurrent::getVipEndTime, currentTime); // vip_end_time >= currentTime

        // 执行查询
        return subscriptionCurrentMapper.selectList(queryWrapper);
    }

    @Override
    public String updateAutoRenewStatus(String subscriptionId, Integer autoRenewStatus) {
        // 构建更新条件
        LambdaUpdateWrapper<SubscriptionCurrent> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(SubscriptionCurrent::getSubscriptionId, subscriptionId)
                .set(SubscriptionCurrent::getAutoRenewStatus, autoRenewStatus);

        // 执行更新操作
        boolean updateSuccess = subscriptionCurrentMapper.update(null, updateWrapper) > 0;
        return null;
    }

    @Override
    public SubscriptionCurrent getLogicValidHighSubscriptionsFromDb(Long userId) {
        // 构建缓存键
        String cacheKey = CACHE_KEY_PREFIX + userId;
        long currentTime = System.currentTimeMillis() / 1000; // 当前时间（秒级时间戳）

        // 1. 尝试从 Redis 获取缓存
        String cachedData = redisService.stringGet(cacheKey);
        if (cachedData != null) {
            try {
                SubscriptionCurrent cachedSubscription = logicObjectMapper.readValue(cachedData, SubscriptionCurrent.class);

                // 判断缓存数据是否过期
                if (cachedSubscription.getVipEndTime() >= currentTime) {
                    return cachedSubscription; // 缓存有效，直接返回
                }
                // 如果缓存中的数据已经过期，则删除缓存
                redisService.delete(cacheKey);
            } catch (JsonProcessingException e) {
                // 处理反序列化异常
                throw new RuntimeException("Failed to deserialize subscription data from Redis.", e);
            }
        }

        // 2. 如果缓存中没有数据或数据已过期，则从数据库查询
        List<SubscriptionCurrent> subscriptionCurrents = getLogicValidSubscriptionsFromDb(userId);

        SubscriptionCurrent highestVipSubscription = getHighestVipSubscription(subscriptionCurrents);

        // 3. 将查询结果缓存到 Redis，设置缓存过期时间（例如：5分钟）
        try {
            if (!VipType.basic.getValue().equalsIgnoreCase(highestVipSubscription.getPlanLevel())) {
                redisService.stringSet(cacheKey, logicObjectMapper.writeValueAsString(highestVipSubscription), 60, TimeUnit.MINUTES);
            }
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Failed to serialize subscription data to Redis.", e);
        }

        // 返回最高级别的订阅
        return highestVipSubscription;
    }

    @Override
    public SubscriptionCurrent getValidHighSubscriptionsFromDb(Long userId, String vipPlatform) {

        List<SubscriptionCurrent> subscriptionCurrents = getValidSubscriptionsFromDb(userId, vipPlatform);

        SubscriptionCurrent highestVipSubscription = getHighestVipSubscription(subscriptionCurrents);

        // 返回最高级别的订阅
        return highestVipSubscription;
    }

    // clear cache
    @Override
    public void clearUserCache(Long userId) {
        String cacheKey = CACHE_KEY_PREFIX + userId;
        redisService.delete(cacheKey);
    }

    @Override
    public Boolean canPay(Long userId, String vipPlatform) {
        List<SubscriptionCurrent> subscriptionCurrents = getValidSubscriptionsAllInfoFromDb(userId);

        // 如果用户没有任何有效订阅，直接返回 true（即用户可以购买）
        if (subscriptionCurrents.isEmpty()) {
            return true;
        }
        //如果subscriptionCurrents中含有其它vipPlatform并且没有自己对应的vipPlatform，则返回false
        // 检查是否有其他平台的订阅
        boolean hasCurrentPlatformSubscription = false;
        boolean hasOtherPlatformSubscription = false;

        for (SubscriptionCurrent subscription : subscriptionCurrents) {
            // 排除 gift 类型的订阅
            if (VipPlatform.GIFT.getPlatformName().equalsIgnoreCase(subscription.getVipPlatform())) {
                continue;
            }

            // 判断是否有当前平台（vipPlatform）的有效订阅
            if (subscription.getVipPlatform().equalsIgnoreCase(vipPlatform)) {
                hasCurrentPlatformSubscription = true;
            } else {
                // 判断是否有其它平台的有效订阅
                hasOtherPlatformSubscription = true;
            }

            // 如果同时有其它平台的订阅并且没有当前平台的订阅，返回 false
            if (hasOtherPlatformSubscription && !hasCurrentPlatformSubscription) {
                return false;
            }
        }

        // 如果没有其它平台的订阅，或者当前平台已有有效订阅，则可以购买
        return true;

    }

    @Override
    public List<SubscriptionCurrent> getValidSubscriptionsFromDb(Long userId) {
        long currentTime = System.currentTimeMillis() / 1000; // 当前时间（秒级时间戳）

        // 构建 Lambda 查询条件
        LambdaQueryWrapper<SubscriptionCurrent> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SubscriptionCurrent::getUserId, userId)
                .eq(SubscriptionCurrent::getInvalid, 0)
                .and(wrapper -> wrapper.eq(SubscriptionCurrent::getVipPlatform, VipPlatform.GIFT.getPlatformName())
                        .le(SubscriptionCurrent::getVipBeginTime, currentTime)
                        .ge(SubscriptionCurrent::getVipEndTime, currentTime)
                        .or()
                        .le(SubscriptionCurrent::getCurrentPeriodStart, currentTime)
                        .ge(SubscriptionCurrent::getCurrentPeriodEnd, currentTime))
                .select(SubscriptionCurrent::getCurrentPeriodStart,
                        SubscriptionCurrent::getCurrentPeriodEnd,
                        SubscriptionCurrent::getPlanLevel,
                        SubscriptionCurrent::getPriceInterval,
                        SubscriptionCurrent::getVipBeginTime,
                        SubscriptionCurrent::getVipEndTime,
                        SubscriptionCurrent::getVipPlatform,
                        SubscriptionCurrent::getAutoRenewStatus,
                        SubscriptionCurrent::getMark,
                        SubscriptionCurrent::getRenewPrice,
                        SubscriptionCurrent::getTrial);

        // 执行查询
        List<SubscriptionCurrent> subscriptionCurrents = subscriptionCurrentMapper.selectList(queryWrapper);

        return subscriptionCurrents;
    }

    private List<SubscriptionCurrent> getValidSubscriptionsFromDb(Long userId, String vipPlatform) {
        long currentTime = System.currentTimeMillis() / 1000; // 当前时间（秒级时间戳）

        // 构建 Lambda 查询条件
        LambdaQueryWrapper<SubscriptionCurrent> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SubscriptionCurrent::getUserId, userId).eq(SubscriptionCurrent::getVipPlatform, vipPlatform)
                .eq(SubscriptionCurrent::getInvalid, 0)
                .le(SubscriptionCurrent::getCurrentPeriodStart, currentTime)
                .ge(SubscriptionCurrent::getCurrentPeriodEnd, currentTime)
                .select(SubscriptionCurrent::getCurrentPeriodStart, SubscriptionCurrent::getCurrentPeriodEnd,
                        SubscriptionCurrent::getPlanLevel,
                        SubscriptionCurrent::getPriceInterval,
                        SubscriptionCurrent::getVipBeginTime,
                        SubscriptionCurrent::getVipEndTime,
                        SubscriptionCurrent::getVipPlatform,
                        SubscriptionCurrent::getAutoRenewStatus,
                        SubscriptionCurrent::getSubscriptionId
                );

        // 执行查询
        List<SubscriptionCurrent> subscriptionCurrents = subscriptionCurrentMapper.selectList(queryWrapper);

        return subscriptionCurrents;
    }

    public List<SubscriptionCurrent> getValidSubscriptionsAllInfoFromDb(Long userId) {
        long currentTime = System.currentTimeMillis() / 1000; // 当前时间（秒级时间戳）

        // 构建 Lambda 查询条件
        LambdaQueryWrapper<SubscriptionCurrent> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SubscriptionCurrent::getUserId, userId)
                .eq(SubscriptionCurrent::getInvalid, 0)
                .le(SubscriptionCurrent::getCurrentPeriodStart, currentTime)
                .ge(SubscriptionCurrent::getCurrentPeriodEnd, currentTime);

        // 执行查询
        return subscriptionCurrentMapper.selectList(queryWrapper);
    }

    /**
     * 获取最高级别的有效订阅
     *
     * @param subscriptions 订阅记录列表
     * @return 最高级别的订阅
     */
    public static SubscriptionCurrent getHighestVipSubscription(List<SubscriptionCurrent> subscriptions) {
        if (subscriptions.isEmpty()) {
            // 如果没有有效订阅，则返回一个默认的订阅对象
            SubscriptionCurrent defaultSubscription = new SubscriptionCurrent();
            defaultSubscription.setPlanLevel("basic");
            return defaultSubscription;
        }

        // 按 vipType 和 priceInterval 排序，获取最高级别的订阅
        return subscriptions.stream().sorted(Comparator.comparing((SubscriptionCurrent sub) -> {
                    // 设置 vipType 的优先级，转换为整数
                    switch (sub.getPlanLevel()) {
                        case "pro":
                            return 3;    // pro 为最高
                        case "standard":
                            return 2;    // standard 为中等
                        case "basic":
                            return 1;    // basic 为最低
                        default:
                            return 1;    // 未知类型
                    }
                }).thenComparing(sub -> {
                    // 设置 priceInterval 的优先级，年付 > 月付
                    return "year".equals(sub.getPriceInterval()) ? 1 : 0;
                }).reversed()) // reversed 确保 vipType 和 priceInterval 按优先级从高到低排序
                .findFirst() // 获取排序后的第一个元素
                .orElseGet(() -> {
                    // 如果没有订阅，则返回一个默认的订阅对象
                    SubscriptionCurrent defaultSubscription = new SubscriptionCurrent();
                    defaultSubscription.setPlanLevel("basic");
                    return defaultSubscription;
                });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateStripeSubscriptionCurrent(SubscriptionCurrent subscriptionCurrent) {
        log.info("start saveOrUpdateStripeSubscriptionCurrent {} {}", subscriptionCurrent.getUserId(), subscriptionCurrent.getSubscriptionId());
        SubscriptionCurrent one = this.lambdaQuery().eq(SubscriptionCurrent::getUserId, subscriptionCurrent.getUserId())
                .eq(SubscriptionCurrent::getVipPlatform, subscriptionCurrent.getVipPlatform())
                .eq(SubscriptionCurrent::getSubscriptionId, subscriptionCurrent.getSubscriptionId())
                .eq(SubscriptionCurrent::getInvalid, false)
                .one();
        if (one != null) {
            long nowEpochSecond = System.currentTimeMillis() / 1000;
            subscriptionCurrent.setId(one.getId());
            subscriptionCurrent.setUpdateTime(LocalDateTime.now());
            Long vipEndTime = one.getVipEndTime();
            if (vipEndTime != null) {
                long diffEpochSecond = subscriptionCurrent.getCurrentPeriodEnd() - subscriptionCurrent.getCurrentPeriodStart();
                log.info("{} {} vipEndTime:{} diff: {}", one.getUserId(), one.getSubscriptionId(), vipEndTime, diffEpochSecond);
                subscriptionCurrent.setVipEndTime(nowEpochSecond > vipEndTime ? nowEpochSecond + diffEpochSecond : vipEndTime + diffEpochSecond);
            } else {
                log.info("{} {} vipEndTime is null", one.getUserId(), one.getSubscriptionId());
                subscriptionCurrent.setVipBeginTime(subscriptionCurrent.getCurrentPeriodStart());
                subscriptionCurrent.setVipEndTime(subscriptionCurrent.getCurrentPeriodEnd());
            }
            subscriptionCurrent.setUpdateTime(LocalDateTime.now());
            this.updateById(subscriptionCurrent);
        } else {
            log.info("{} {} is new", subscriptionCurrent.getUserId(), subscriptionCurrent.getSubscriptionId());
            subscriptionCurrent.setVipBeginTime(subscriptionCurrent.getCurrentPeriodStart());
            subscriptionCurrent.setVipEndTime(subscriptionCurrent.getCurrentPeriodEnd());
            subscriptionCurrent.setCreateTime(LocalDateTime.now());
            this.save(subscriptionCurrent);
        }
        this.clearUserCache(subscriptionCurrent.getUserId());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recallStripeVipTime(StripeSubscriptionRecord subscription) {
        log.info("start recallStripeVipTime {} {}", subscription.getLoginName(), subscription.getSubscriptionId());
        long diff = subscription.getCurrentPeriodEnd() - subscription.getCurrentPeriodStart();
        log.info("diff: {}", diff);
        this.lambdaUpdate().eq(SubscriptionCurrent::getUserId, subscription.getUserId())
                .eq(SubscriptionCurrent::getVipPlatform, VipPlatform.STRIPE.getPlatformName())
                .eq(SubscriptionCurrent::getSubscriptionId, subscription.getSubscriptionId())
                .eq(SubscriptionCurrent::getInvalid, false)
                .set(SubscriptionCurrent::getUpdateTime, subscription.getUpdateTime())
                .setSql(" vip_end_time = vip_end_time - " + diff + " ,current_period_end = current_period_end - " + diff)
                .update();
        log.info("end recallStripeVipTime {} {}", subscription.getLoginName(), subscription.getSubscriptionId());
    }

    @Override
    public void saveOrUpdateAppleSubscriptionCurrent(SubscriptionCurrent subscriptionCurrent) {
        logApple.info("start saveAppleSubscriptionCurrent transactionId={} {} {} {}", subscriptionCurrent.getTransactionId(), subscriptionCurrent.getUserId(), subscriptionCurrent.getPlanLevel(), subscriptionCurrent.getPriceInterval());
        SubscriptionCurrent one = this.lambdaQuery().eq(SubscriptionCurrent::getUserId, subscriptionCurrent.getUserId())
                .eq(SubscriptionCurrent::getVipPlatform, subscriptionCurrent.getVipPlatform())
                .eq(SubscriptionCurrent::getOriginalTransactionId, subscriptionCurrent.getOriginalTransactionId())
                .eq(SubscriptionCurrent::getInvalid, false)
                .one();

        if (one != null) {
            // 如果subscriptionCurrent的purchaseDate 的时间 >now+60 则插入新的记录 并且更新原记录originalTransactionId = null
            if (subscriptionCurrent.getCurrentPeriodStart() > (System.currentTimeMillis() / 1000 + 60)) {
                subscriptionCurrent.setVipBeginTime(subscriptionCurrent.getCurrentPeriodStart());
                subscriptionCurrent.setVipEndTime(subscriptionCurrent.getCurrentPeriodEnd());
                subscriptionCurrent.setCreateTime(LocalDateTime.now());
                this.save(subscriptionCurrent);
                log.info("{} {} {} is diff 60s", subscriptionCurrent.getLoginName(), subscriptionCurrent.getSubscriptionId(), subscriptionCurrent.getCurrentPeriodStart() - System.currentTimeMillis());
                this.lambdaUpdate().eq(SubscriptionCurrent::getId, one.getId())
                        .set(SubscriptionCurrent::getOriginalTransactionId, null)
                        .set(SubscriptionCurrent::getInvalid, false)
                        .update();
            } else {
                // 如果subscriptionCurrent的purchaseDate 的时间 < now + 60 则直接更新原记录
                subscriptionCurrent.setId(one.getId());
                subscriptionCurrent.setUpdateTime(LocalDateTime.now());
                subscriptionCurrent.setTransactionId(subscriptionCurrent.getTransactionId());
                subscriptionCurrent.setCurrentPeriodStart(subscriptionCurrent.getCurrentPeriodStart());
                subscriptionCurrent.setCurrentPeriodEnd(subscriptionCurrent.getCurrentPeriodEnd());
                subscriptionCurrent.setOriginalTransactionId(subscriptionCurrent.getOriginalTransactionId());
                subscriptionCurrent.setPlanLevel(subscriptionCurrent.getPlanLevel());
                subscriptionCurrent.setPriceInterval(subscriptionCurrent.getPriceInterval());
                subscriptionCurrent.setAutoRenewStatus(subscriptionCurrent.getAutoRenewStatus());

                logApple.info("{} {} vipEndTime is null", one.getLoginName(), one.getTransactionId());
                // 开始时间前前移 60 webhook可能会提前到
                subscriptionCurrent.setVipBeginTime(subscriptionCurrent.getCurrentPeriodStart() - 60);
                subscriptionCurrent.setVipEndTime(subscriptionCurrent.getCurrentPeriodEnd());
                this.updateById(subscriptionCurrent);
            }
        } else {
            logApple.info("{} {} is new", subscriptionCurrent.getLoginName(), subscriptionCurrent.getTransactionId());
            subscriptionCurrent.setVipBeginTime(subscriptionCurrent.getCurrentPeriodStart());
            subscriptionCurrent.setVipEndTime(subscriptionCurrent.getCurrentPeriodEnd());
            subscriptionCurrent.setCreateTime(LocalDateTime.now());
            this.save(subscriptionCurrent);
        }
        this.clearUserCache(subscriptionCurrent.getUserId());

        // IOS 发送升级站内信
        commMessageService.addUserUpgradeVipMessage(subscriptionCurrent.getPlanLevel(), subscriptionCurrent.getCurrentPeriodEnd(), subscriptionCurrent.getPriceInterval(), subscriptionCurrent.getUserId());

        logApple.info("end saveAppleSubscriptionCurrent transactionId={} {} {} {}", subscriptionCurrent.getTransactionId(), subscriptionCurrent.getLoginName(), subscriptionCurrent.getPlanLevel(), subscriptionCurrent.getPriceInterval());
    }

    @Override
    public SubscriptionCurrent queryByOriginTransactionId(String originalTransactionId) {
        SubscriptionCurrent currentSubscription = this.lambdaQuery()
                .eq(SubscriptionCurrent::getOriginalTransactionId, originalTransactionId)
                .eq(SubscriptionCurrent::getInvalid, false)
                .one();
        return currentSubscription;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void restoreSubscriptionRecord(String originTransactionId, Long srcUserId, Long newUserId, String loginName) {
        List<SubscriptionCurrent> list = this.lambdaQuery()
                .eq(SubscriptionCurrent::getVipPlatform, VipPlatform.IOS.getPlatformName())
                .eq(SubscriptionCurrent::getUserId, srcUserId)
                .eq(SubscriptionCurrent::getInvalid, false)
                .list();
        if (list.isEmpty()) {
            log.info("no subscription to restore {} {}", srcUserId, loginName);
            return;
        }
        List<SubscriptionCurrent> saveNew = new ArrayList<>();
        List<Long> ids = new ArrayList<>();
        for (SubscriptionCurrent currentSubscription : list) {
            SubscriptionCurrent subscriptionCurrent = this.copyToNew(currentSubscription);
            subscriptionCurrent.setUserId(newUserId);
            subscriptionCurrent.setLoginName(loginName);
            currentSubscription.setUpdateTime(LocalDateTime.now());
            saveNew.add(subscriptionCurrent);
            ids.add(currentSubscription.getId());
        }
        this.lambdaUpdate().in(SubscriptionCurrent::getId, ids)
                .set(SubscriptionCurrent::getInvalid, true)
                .update();
        this.saveBatch(saveNew);

        this.clearUserCache(srcUserId);
        this.clearUserCache(newUserId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateSubscriptionPlan(SubscriptionCurrent subscription, String newProductId, JWSTransactionDecodedPayload transaction) {
        PayAppleProduct newProduct = payAppleProductService.lambdaQuery()
                .eq(PayAppleProduct::getAppleProductId, newProductId).one();
        if (newProduct == null) {
            logApple.error("新产品不存在: productId={}", newProductId);
            throw new PayAppleException(PRODUCT_NOT_FOUND);
        }
        SubscriptionCurrent newSubs = this.copyToNew(subscription);
        newSubs.setPlanLevel(newProduct.getPlanLevel());
        newSubs.setPriceInterval(newProduct.getPriceInterval());
        newSubs.setVipBeginTime(subscription.getCurrentPeriodStart());
        newSubs.setVipEndTime(subscription.getCurrentPeriodEnd());
        newSubs.setUpdateTime(LocalDateTime.now());
        this.save(newSubs);

        this.lambdaUpdate().eq(SubscriptionCurrent::getId, subscription.getId())
                .set(SubscriptionCurrent::getUpdateTime, LocalDateTime.now())
                .set(SubscriptionCurrent::getInvalid, true)
                .update();
//        logApple.info("更新订阅计划信息: subscriptionId={},srcPlanLever={} newPlanLevel={} srcVipEndTime={}, diff={}, nowEndVipTime={}", subscription.getSubscriptionId(), newProduct.getPlanLevel(), subscription.getPlanLevel(), subscription.getVipEndTime(), diff, subscription.getVipEndTime() - diff);
        this.clearUserCache(subscription.getUserId());
        logApple.info("更新订阅计划信息: subscriptionId={},srcPlanLever={} newPlanLevel={} srcVipEndTime={} srcId={}, newId={}"
                , subscription.getSubscriptionId(), newProduct.getPlanLevel(), subscription.getPlanLevel(), subscription.getVipEndTime(), subscription.getId(), newSubs.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCurrentSub(StripeProduct stripeProduct, StripeSubscriptionRecord record, Discount discount) {
        log.info("updateCurrentSub {} {} {}", record.getSubscriptionId(), record.getLoginName(), stripeProduct);
        SubscriptionCurrent current = this.queryBySubscriptionId(record.getSubscriptionId());
        if (current != null) {
            SubscriptionCurrent subscriptionCurrent = this.copyToNew(current);
            subscriptionCurrent.setPlanLevel(stripeProduct.getPlanLevel());
            subscriptionCurrent.setPriceInterval(stripeProduct.getPriceInterval());
            subscriptionCurrent.setVipBeginTime(record.getCurrentPeriodStart());
            subscriptionCurrent.setVipEndTime(record.getCurrentPeriodEnd());
            subscriptionCurrent.setCurrentPeriodStart(record.getCurrentPeriodStart());
            subscriptionCurrent.setCurrentPeriodEnd(record.getCurrentPeriodEnd());
            subscriptionCurrent.setCreateTime(LocalDateTime.now());
            subscriptionCurrent.setInvalid(false);
            subscriptionCurrent.setTrial(false);
            if (stripeProduct.getMark() == null) {
                subscriptionCurrent.setMark("v1");
            } else {
                subscriptionCurrent.setMark(stripeProduct.getMark());
            }
            subscriptionCurrent.setAutoRenewStatus(record.getCancelledAt() == null ? 1 : 0);
            buildRenewPrice(stripeProduct, discount, subscriptionCurrent);
            this.save(subscriptionCurrent);
            this.lambdaUpdate().eq(SubscriptionCurrent::getId, current.getId())
                    .set(SubscriptionCurrent::getInvalid, true)
                    .set(SubscriptionCurrent::getUpdateTime, LocalDateTime.now())
                    .update();
            this.clearUserCache(record.getUserId());
        }
    }

    @Override
    public boolean hasBoughtVipAndHasExpire(Long userId, int daysBefore) {
        List<SubscriptionCurrent> logicValidSubscriptionsFromDb = this.getLogicValidSubscriptionsFromDb(userId);
        if (!logicValidSubscriptionsFromDb.isEmpty()) {
            logicValidSubscriptionsFromDb = logicValidSubscriptionsFromDb
                    .stream()
                    .filter(subscriptionCurrent -> !VipPlatform.GIFT.getPlatformName().equalsIgnoreCase(subscriptionCurrent.getVipPlatform()))
                    .collect(Collectors.toList());
        }
        if (!logicValidSubscriptionsFromDb.isEmpty()) {
            log.info("hasBoughtVipAndHasExpire: {} has valid subscription", userId);
            return false;
        }
        long currentTime = System.currentTimeMillis() / 1000;
        long timeThreshold = currentTime + 20 - (long) daysBefore * 24 * 60 * 60;

        LambdaQueryWrapper<SubscriptionCurrent> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SubscriptionCurrent::getUserId, userId)
                .ne(SubscriptionCurrent::getVipPlatform, VipPlatform.GIFT.getPlatformName())
                .and(e -> e.le(SubscriptionCurrent::getVipEndTime, timeThreshold).or().eq(SubscriptionCurrent::getInvalid, true))
                .select(SubscriptionCurrent::getVipEndTime);

        List<SubscriptionCurrent> subscriptionCurrents = subscriptionCurrentMapper.selectList(queryWrapper);
        if (!subscriptionCurrents.isEmpty()) {
            log.info("hasBoughtVipAndHasExpire: {} has expired subscription", userId);
            return true;
        }
        return false;
    }


    public static void buildRenewPrice(StripeProduct stripeProduct, Discount discount, SubscriptionCurrent subscriptionCurrent) {
        String price = stripeProduct.getPrice();
        if (discount != null) {
            Coupon coupon = discount.getCoupon();
            if (coupon != null) {
                BigDecimal percentOff = coupon.getPercentOff();
                BigDecimal discountPercent = new BigDecimal(100).subtract(percentOff).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
                price = new BigDecimal(price).multiply(discountPercent).setScale(2, RoundingMode.HALF_DOWN).toString();
            }
        }
        subscriptionCurrent.setRenewPrice(price);
    }

    @Override
    public void invalidatedSubscriptionForPaypal(String srcSubscriptionId) {
        this.lambdaUpdate().eq(SubscriptionCurrent::getSubscriptionId, srcSubscriptionId)
                .eq(SubscriptionCurrent::getInvalid, false)
                .set(SubscriptionCurrent::getInvalid, true)
                .set(SubscriptionCurrent::getUpdateTime, LocalDateTime.now())
                .update();
    }

    @Override
    public void reActivedSubscriptionForPaypal(String subscriptionId) {
        this.lambdaUpdate().eq(SubscriptionCurrent::getSubscriptionId, subscriptionId)
                .eq(SubscriptionCurrent::getInvalid, true)
                .set(SubscriptionCurrent::getInvalid, false)
                .set(SubscriptionCurrent::getUpdateTime, LocalDateTime.now())
                .update();
    }

    @Override
    public void updateRenewPrice(String total, String subscriptionId) {
        log.info("start updateRenewPrice {} {}", subscriptionId, total);
        this.lambdaUpdate().eq(SubscriptionCurrent::getSubscriptionId, subscriptionId)
                .eq(SubscriptionCurrent::getInvalid, false)
                .orderByDesc(SubscriptionCurrent::getId)
                .last("limit 1")
                .set(SubscriptionCurrent::getUpdateTime, LocalDateTime.now())
                .set(SubscriptionCurrent::getRenewPrice, total)
                .update();
    }

    @Override
    public void saveOrUpdateGoogleSubscriptionCurrent(SubscriptionCurrent googleSubscriptions) {
        logGoogle.info("saveOrUpdateGoogleSubscriptionCurrent: {} {}", googleSubscriptions.getPurchaseToken(), googleSubscriptions.getLoginName());
        SubscriptionCurrent one = this.lambdaQuery().eq(SubscriptionCurrent::getUserId, googleSubscriptions.getUserId())
                .eq(SubscriptionCurrent::getVipPlatform, googleSubscriptions.getVipPlatform())
                .eq(SubscriptionCurrent::getPurchaseToken, googleSubscriptions.getPurchaseToken())
                .eq(SubscriptionCurrent::getLatestOrderId, googleSubscriptions.getLatestOrderId())
                .eq(SubscriptionCurrent::getInvalid, false)
                .one();
        if (one == null) {
            log.info("saveOrUpdateGoogleSubscriptionCurrent: {} {}", googleSubscriptions.getPurchaseToken(), googleSubscriptions.getLoginName());
            this.save(googleSubscriptions);
        } else {
            log.info("updateGoogleSubscriptionCurrent: {} {}", googleSubscriptions.getPurchaseToken(), googleSubscriptions.getLoginName());
            this.invalidSubscription(one);
            this.save(googleSubscriptions);
        }
        this.clearUserCache(googleSubscriptions.getUserId());
    }

    @Override
    public void saveNew(SubscriptionCurrent subscriptionCurrent) {
        this.save(subscriptionCurrent);
        this.clearUserCache(subscriptionCurrent.getUserId());
    }

    @Override
    public void updateExpiryTime(GoogleSubscriptions googleSubscriptions) {
        logGoogle.info("updateExpiryTime: {}", googleSubscriptions);
        this.lambdaUpdate().eq(SubscriptionCurrent::getPurchaseToken, googleSubscriptions.getPurchaseToken())
                .eq(SubscriptionCurrent::getLatestOrderId, googleSubscriptions.getLatestOrderId())
                .set(SubscriptionCurrent::getInvalid, false)
                .set(SubscriptionCurrent::getAutoRenewStatus, googleSubscriptions.getAutoRenewing())
                .set(SubscriptionCurrent::getVipEndTime, googleSubscriptions.getExpiryTime())
                .set(SubscriptionCurrent::getCurrentPeriodEnd, googleSubscriptions.getExpiryTime())
                .set(SubscriptionCurrent::getUpdateTime, LocalDateTime.now())
                .update();
    }

    private void invalidSubscription(SubscriptionCurrent one) {
        this.lambdaUpdate().eq(SubscriptionCurrent::getId, one.getId())
                .set(SubscriptionCurrent::getInvalid, true)
                .set(SubscriptionCurrent::getUpdateTime, LocalDateTime.now())
                .update();
    }

    private SubscriptionCurrent queryBySubscriptionId(String subscriptionId) {
        return this.lambdaQuery()
                .eq(SubscriptionCurrent::getSubscriptionId, subscriptionId)
                .eq(SubscriptionCurrent::getInvalid, false)
                .one();
    }

    private SubscriptionCurrent copyToNew(SubscriptionCurrent src) {
        SubscriptionCurrent newSub = new SubscriptionCurrent();
        BeanUtils.copyProperties(src, newSub, "id", "createTime", "updateTime", "invalid", "createBy", "updateBy");
        return newSub;
    }

    @Override
    public Boolean hasRenewSubscription(Long userId) {

        Long currentTime = System.currentTimeMillis() / 1000; // 当前时间（秒级时间戳）

        LambdaQueryWrapper<SubscriptionCurrent> scw = new LambdaQueryWrapper<>();
        scw.eq(SubscriptionCurrent::getUserId, userId);
        scw.eq(SubscriptionCurrent::getInvalid, Boolean.FALSE);
        scw.eq(SubscriptionCurrent::getAutoRenewStatus, 1);
        scw.ne(SubscriptionCurrent::getPlanLevel, VipPlatform.GIFT.getPlatformName());
        scw.le(SubscriptionCurrent::getCurrentPeriodStart, currentTime);
        scw.ge(SubscriptionCurrent::getCurrentPeriodEnd, currentTime);

        return this.count(scw) > 0;

    }
}
