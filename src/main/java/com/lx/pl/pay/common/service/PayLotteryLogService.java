

package com.lx.pl.pay.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.pay.common.domain.PayLotteryLog;
import com.lx.pl.pay.common.dto.GoogleLotteryVo;

import java.util.List;

public interface PayLotteryLogService extends IService<PayLotteryLog> {
    boolean hasParticipated(Long userId, String platform, String relationCode);

    void saveLog(User user, String draw, String platform, String relationCode, List<GoogleLotteryVo> lotteryVos);
}

