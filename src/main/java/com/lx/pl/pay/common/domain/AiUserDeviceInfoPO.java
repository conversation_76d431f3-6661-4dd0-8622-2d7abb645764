package com.lx.pl.pay.common.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lx.pl.db.mysql.gen.entity.MyBaseEntity;
import lombok.Data;

@Data
@TableName("user_device_info")
public class AiUserDeviceInfoPO extends MyBaseEntity {


    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;


    private Long userId;


    /**
     * 平台  android  IOS  PlatformTypeEnum
     */
    private String platform;


    /**
     * 设备信息 参考 AndroidDeviceInfo.java 或 IOSDeviceInfo.java
     */
    private String deviceInfo;


}