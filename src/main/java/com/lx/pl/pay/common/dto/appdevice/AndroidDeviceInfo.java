package com.lx.pl.pay.common.dto.appdevice;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AndroidDeviceInfo {

    /**
     * SE平台分配的appKey，开发者初始化传入
     */
    private String _appkey;

    /**
     * SE生成的设备ID
     */
    private String _distinct_id;

    /**
     * 开发者通过login接口传入的accountID
     */
    private String _account_id;

    /**
     * 开发者通过setVisitorID接口传入的visitorID
     */
    private String _visitor_id;

    /**
     * SE内部每次冷启动生成的sessionID
     */
    private String _session_id;

    /**
     * SE安装时产生的唯一UUID
     */
    private String _uuid;

    /**
     * 设备IMEI
     */
    private String _imei;

    /**
     * 设备IMEI2
     */
    private String _imei2;

    /**
     * 设备gaid
     */
    private String _gaid;

    /**
     * 设备oaid
     */
    private String _oaid;

    /**
     * 设备AndroidId
     */
    private String _android_id;

    /**
     * 设备的UA
     */
    private String _ua;

    /**
     * 设备的系统设置的语言
     */
    private String _language;

    /**
     * 设备的时区
     */
    private String _time_zone;

    /**
     * 设备生成厂商，如：vivo
     */
    private String _manufacturer;

    /**
     * SDK平台，固定字段1
     */
    private Integer _platform;

    /**
     * 设备系统版本
     */
    private String _os_version;

    /**
     * 屏幕高
     */
    private Integer _screen_height;

    /**
     * 屏幕宽
     */
    private Integer _screen_width;

    /**
     * 屏幕密度
     */
    private Float _density;

    /**
     * 设备型号，如：vivoV2057A
     */
    private String _device_model;

    /**
     * 设备类型，1：Android_phone，2：Android_pad，0：其它
     */
    private Integer _device_type;

    /**
     * 应用版本号
     */
    private String _app_version;

    /**
     * 应用版本code
     */
    private String _app_version_code;

    /**
     * 应用包名
     */
    private String _package_name;

    /**
     * 应用名称
     */
    private String _app_name;

    /**
     * 渠道名称
     */
    private String _channel;

    /**
     * 固定字段：1
     */
    private Integer _lib;

    /**
     * SDK版本号
     */
    private String _lib_version;
}
