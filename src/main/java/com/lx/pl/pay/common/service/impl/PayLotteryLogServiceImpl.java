

package com.lx.pl.pay.common.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.pay.common.domain.PayLotteryLog;
import com.lx.pl.pay.common.dto.GoogleLotteryVo;
import com.lx.pl.pay.common.mapper.PayLotteryLogMapper;
import com.lx.pl.pay.common.service.PayLotteryLogService;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class PayLotteryLogServiceImpl extends ServiceImpl<PayLotteryLogMapper, PayLotteryLog> implements PayLotteryLogService {
    @Override
    public boolean hasParticipated(Long userId, String platform, String relationCode) {
        if (this.lambdaQuery().eq(PayLotteryLog::getUserId, userId)
                .eq(PayLotteryLog::getRelationCode, relationCode)
                .eq(platform != null, PayLotteryLog::getPlatform, platform)
                .isNull(platform == null, PayLotteryLog::getPlatform).exists()) {
            return true;
        }
        return false;
    }

    @Override
    public void saveLog(User user, String draw, String platform, String relationCode, List<GoogleLotteryVo> lotteryVos) {
        PayLotteryLog payLotteryLog = new PayLotteryLog();
        payLotteryLog.setUserId(user.getId());
        payLotteryLog.setLoginName(user.getLoginName());
        payLotteryLog.setPlatform(platform);
        payLotteryLog.setAward(draw);
        payLotteryLog.setRelationCode(relationCode);
        payLotteryLog.setCreateTime(LocalDateTime.now());
        this.save(payLotteryLog);
    }
}

