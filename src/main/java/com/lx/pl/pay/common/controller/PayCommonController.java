package com.lx.pl.pay.common.controller;

import com.google.common.collect.Lists;
import com.lx.pl.annotation.Authorization;
import com.lx.pl.annotation.CurrentUser;
import com.lx.pl.db.mysql.gen.entity.StripeProduct;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.dto.UserDeviceInfoReportReq;
import com.lx.pl.dto.generic.R;
import com.lx.pl.enums.VipType;
import com.lx.pl.pay.apple.dto.PayAppleProductDto;
import com.lx.pl.pay.apple.service.ApplePayService;
import com.lx.pl.pay.common.domain.PromotionConfigItem;
import com.lx.pl.pay.common.domain.SubscriptionCurrent;
import com.lx.pl.pay.common.dto.PromotionConfigVo;
import com.lx.pl.pay.common.dto.SubscriptionCurrentDto;
import com.lx.pl.pay.common.dto.UserPromotionStatusDto;
import com.lx.pl.pay.common.service.*;
import com.lx.pl.service.StripeProductService;
import com.lx.pl.service.VipService;
import com.lx.pl.service.message.DingTalkAlert;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@Tag(name = "vip共通相关接口")
@Slf4j
@RequestMapping("/api/pay/common")
public class PayCommonController {

    @Autowired
    VipService vipService;
    @Autowired
    SubscriptionCurrentService subscriptionCurrentService;
    @Autowired
    PromotionConfigItemService promotionConfigItemService;
    @Autowired
    StripeProductService stripeProductService;
    @Autowired
    ApplePayService applePayService;
    @Autowired
    PayLumenRecordService payLumenRecordService;
    @Autowired
    AiUserDeviceInfoService aiUserDeviceInfoService;
    @Autowired
    private DingTalkAlert dingTalkAlert;


    @Operation(summary = "查询用户vip信息详情for pay")
    @Authorization
    @GetMapping(path = "/vip-info-for-pay")
    public R<Map<String, Object>> vipInfo(@Parameter(hidden = true) @CurrentUser User user, String vipPlatform) {
        return R.success(vipService.getRealUserVipInfo(user.getId(), vipPlatform));
    }

    @Operation(summary = "查询支付用所有激活vip信息详情")
    @Authorization
    @GetMapping(path = "/all-pay-vip-info")
    public R<List<SubscriptionCurrentDto>> getAllPayVipInfo(@Parameter(hidden = true) @CurrentUser User user) {
        List<SubscriptionCurrent> validSubscriptionsFromDb = subscriptionCurrentService.getValidSubscriptionsFromDb(user.getId());
        List<SubscriptionCurrentDto> subscriptionCurrentDtos = validSubscriptionsFromDb.stream().map(sub -> {
            SubscriptionCurrentDto dto = new SubscriptionCurrentDto();
            dto.setCurrentPeriodStart(sub.getCurrentPeriodStart());
            dto.setCurrentPeriodEnd(sub.getCurrentPeriodEnd());
            dto.setPlanLevel(sub.getPlanLevel());
            dto.setPriceInterval(sub.getPriceInterval());
            dto.setVipBeginTime(sub.getVipBeginTime());
            dto.setVipEndTime(sub.getVipEndTime());
            dto.setVipPlatform(sub.getVipPlatform());
            dto.setAutoRenewStatus(sub.getAutoRenewStatus());
            dto.setMark(sub.getMark());
            dto.setRenewPrice(sub.getRenewPrice());
            dto.setTrial(sub.getTrial());
            return dto;
        }).collect(Collectors.toList());
        // 按条件分组，保留每组中 vipEndTime 最大的数据
        Map<List<String>, Optional<SubscriptionCurrentDto>> groupedMap = subscriptionCurrentDtos.stream()
                .collect(Collectors.groupingBy(dto -> Arrays.asList(dto.getVipPlatform(), dto.getPlanLevel(), dto.getPriceInterval()), Collectors.maxBy(Comparator.comparing(SubscriptionCurrentDto::getVipEndTime))));

        List<SubscriptionCurrentDto> filteredList = groupedMap.values().stream().filter(Optional::isPresent) // 过滤掉空值
                .map(Optional::get)          // 提取实际值
                .collect(Collectors.toList());
        return R.success(filteredList.stream().sorted(Comparator.comparing((SubscriptionCurrentDto sub) -> {
            // 设置 vipType 的优先级，转换为整数
            switch (sub.getPlanLevel()) {
                case "pro":
                    return 3;    // pro 为最高
                case "standard":
                    return 2;    // standard 为中等
                case "basic":
                    return 1;    // basic 为最低
                default:
                    return 1;    // 未知类型
            }
        }).thenComparing(sub -> {
            // 设置 priceInterval 的优先级，年付 > 月付
            return "year".equals(sub.getPriceInterval()) ? 1 : 0;
        }).reversed()).collect(Collectors.toList()));
    }

    @Operation(summary = "查询用户优惠状态和折扣价格列表")
    @Authorization
    @GetMapping(path = "/user-promotion-status")
    public R<UserPromotionStatusDto> getUserPromotionStatus(@Parameter(hidden = true) @CurrentUser User user, @RequestParam(value = "platform", defaultValue = "stripe", required = false) String platform) {

        UserPromotionStatusDto result = new UserPromotionStatusDto();

//        // 1. 检查用户是否能试用
        Boolean canTrial = vipService.canTrail(user.getId());
        SubscriptionCurrent logicValidHighSubscriptionsFromDb = subscriptionCurrentService.getLogicValidHighSubscriptionsFromDb(user.getId());
        if (logicValidHighSubscriptionsFromDb != null) {
            result.setPlanLevel(logicValidHighSubscriptionsFromDb.getPlanLevel());
            result.setPriceInterval(logicValidHighSubscriptionsFromDb.getPriceInterval());
        }
//        // 5. 获取产品价格列表并计算折扣
        UserPromotionStatusDto.PromotionInfo planPromotion = new UserPromotionStatusDto.PromotionInfo();
        UserPromotionStatusDto.PromotionInfo lumenPromotion = new UserPromotionStatusDto.PromotionInfo();
        List<UserPromotionStatusDto.ProductPriceInfo> discountedPrices = calculateDiscountedPrices(user, platform, canTrial, planPromotion, lumenPromotion);
        result.setDiscountedPrices(discountedPrices);
        result.setPlanPromotion(Lists.newArrayList(planPromotion));
        result.setLumenPromotion(lumenPromotion);
        result.setFirstBuyLumen(!payLumenRecordService.hasPurchasedLumen(user.getId()));
        result.setNewUser(canTrial);
        return R.success(result);
    }

    /**
     * 计算折扣后的价格列表
     */
    private List<UserPromotionStatusDto.ProductPriceInfo> calculateDiscountedPrices(User user, String platform, Boolean canTrial, UserPromotionStatusDto.PromotionInfo planPromotion, UserPromotionStatusDto.PromotionInfo lumenPromotion) {

        List<UserPromotionStatusDto.ProductPriceInfo> priceInfoList = new ArrayList<>();

        if ("stripe".equalsIgnoreCase(platform)) {
            // 获取 Stripe 产品列表
            List<StripeProduct> stripeProducts = stripeProductService.list().stream().filter(product -> product.getStatus() != null && product.getStatus() == 1).collect(Collectors.toList());
            Map<String, Pair<String, PromotionConfigVo>> productTypeDiscountMap = new HashMap<>();
            Map<Long, List<PromotionConfigItem>> itemMap = new HashMap<>();
            for (StripeProduct product : stripeProducts) {
                UserPromotionStatusDto.ProductPriceInfo priceInfo = new UserPromotionStatusDto.ProductPriceInfo();
//                priceInfo.setProductId(product.getStripeProductId());
                priceInfo.setMark(product.getMark());
                priceInfo.setPlanLevel(product.getPlanLevel());
                priceInfo.setProductType(product.getProductType());
                priceInfo.setPriceInterval(product.getPriceInterval());
                priceInfo.setLumen(product.getLumen());
                priceInfo.setInitialLumen(product.getInitialLumen());
                priceInfo.setTrialDay(product.getTrialDay());
                priceInfo.setMark(product.getMark());

                String originalPrice = product.getPrice();
                priceInfo.setOriginalPrice(originalPrice);
                String planLevelAndPriceInterval = "plan".equalsIgnoreCase(product.getProductType()) ? product.getPlanLevel() + "." + product.getPriceInterval() : null;
                Pair<String, PromotionConfigVo> stringIntegerPair = productTypeDiscountMap.get(planLevelAndPriceInterval);
                if (stringIntegerPair == null) {
                    stringIntegerPair = vipService.getUserMaxDiscount(user.getId(), product.getProductType(), planLevelAndPriceInterval);
                    if (stringIntegerPair != null) {
                        productTypeDiscountMap.put(planLevelAndPriceInterval, stringIntegerPair);
                        if ("plan".equals(product.getProductType()) && stringIntegerPair.getLeft() != null) {
                            planPromotion.setType(stringIntegerPair.getLeft());
                            planPromotion.setOff(stringIntegerPair.getRight().getOff());
                            planPromotion.setRedeemBy(stringIntegerPair.getRight().getRedeemBy());
                        } else {
                            if (stringIntegerPair.getLeft() != null) lumenPromotion.setType(stringIntegerPair.getLeft());
                            if (stringIntegerPair.getRight() != null && stringIntegerPair.getRight().getUsedPromotionConfigItem() != null)
                                lumenPromotion.setOff(stringIntegerPair.getRight().getUsedPromotionConfigItem().getOff());
                        }
                    }
                }

                // 计算折扣
                calculateDiscount(originalPrice, productTypeDiscountMap.get(planLevelAndPriceInterval), priceInfo);
                priceInfoList.add(priceInfo);
            }
        } else if ("apple".equalsIgnoreCase(platform)) {
            // 获取 Apple 产品列表
            List<PayAppleProductDto> appleProducts = applePayService.getProduct();

            for (PayAppleProductDto product : appleProducts) {
                UserPromotionStatusDto.ProductPriceInfo priceInfo = new UserPromotionStatusDto.ProductPriceInfo();
                priceInfo.setMark(product.getMark());
                priceInfo.setPlanLevel(product.getPlanLevel());
                priceInfo.setProductType(product.getProductType());
                priceInfo.setPriceInterval(product.getPriceInterval());
                priceInfo.setLumen(product.getLumen());
                priceInfo.setInitialLumen(product.getInitialLumen());
                priceInfo.setMark(product.getMark());

                // Apple 产品价格由客户端提供，这里暂时不计算折扣
                priceInfo.setOriginalPrice("0");
                priceInfo.setDiscountedPrice("0");
                priceInfo.setSavedAmount("0");
                priceInfo.setAppliedPromotionType("none");

                priceInfoList.add(priceInfo);
            }
        }

        return priceInfoList;
    }

    /**
     * 计算单个产品的折扣价格
     */
    private void calculateDiscount(String originalPrice, Pair<String, PromotionConfigVo> stringIntegerPair, UserPromotionStatusDto.ProductPriceInfo priceInfo) {
        if (originalPrice == null) {
            return;
        }
        try {
//            Pair<String, PromotionConfigVo> stringIntegerPair = productTypeDiscountMap.getOrDefault(product.getPlanLevel() + "." + product.getPriceInterval(), Pair.of("none", null));
            BigDecimal price = new BigDecimal(originalPrice);
            int maxDiscount = 0;
            int off = 0;
            String appliedPromotionType = null;
            if (stringIntegerPair != null) {
                appliedPromotionType  = stringIntegerPair.getLeft();
                 off = stringIntegerPair.getRight() != null && stringIntegerPair.getRight().getUsedPromotionConfigItem() != null ? stringIntegerPair.getRight().getUsedPromotionConfigItem()
                        .getOff() : 0;

                if (stringIntegerPair.getRight() != null && stringIntegerPair.getRight().getUsedPromotionConfigItem() != null) {
                    priceInfo.setFrontOff(stringIntegerPair.getRight().getUsedPromotionConfigItem().getFrontOff());
                    priceInfo.setDiscountedPrice(stringIntegerPair.getRight().getUsedPromotionConfigItem().getFrontMonthPrice());
                    off = stringIntegerPair.getRight().getUsedPromotionConfigItem().getOff();
                }
            }

            // 应用最大折扣
            if (off > 0) {
                BigDecimal discountPercent = new BigDecimal(100 - off).divide(new BigDecimal(100), 8, RoundingMode.HALF_DOWN);
                price = price.multiply(discountPercent);
                priceInfo.setDiscountedPriceNotMonth(price.setScale(2, RoundingMode.HALF_DOWN).toString());
            }
            priceInfo.setOff(off);
            // 计算节省金额
            try {
                if (priceInfo.getDiscountedPrice() != null) {
                    BigDecimal original = new BigDecimal(originalPrice);
                    BigDecimal discounted = new BigDecimal(priceInfo.getDiscountedPriceNotMonth());
                    BigDecimal saved = original.subtract(discounted);
                    priceInfo.setSavedAmount(saved.toString());
                } else {
                    priceInfo.setDiscountedPrice(priceInfo.getOriginalPrice());
                }
            } catch (Exception e) {
                log.error("calculateDiscount error", e);
                priceInfo.setSavedAmount("0");
            }
            priceInfo.setAppliedPromotionType(appliedPromotionType);

        } catch (NumberFormatException e) {
            log.error("calculateDiscount error", e);
        }

        if (VipType.standard.getValue().equalsIgnoreCase(priceInfo.getPlanLevel())) {
            String priceInterval = priceInfo.getPriceInterval();
            if ("year".equalsIgnoreCase(priceInterval)) {
                priceInfo.setOriginalPriceMonth("11.99");
                if (priceInfo.getOff() == 0) {
                    priceInfo.setDiscountedPrice(new BigDecimal(priceInfo.getOriginalPrice()).divide(new BigDecimal(12), 2, RoundingMode.HALF_DOWN).toString());
                    priceInfo.setFrontOff(25);
                }
            } else {
                priceInfo.setOriginalPriceMonth(priceInfo.getOriginalPrice());
            }
        }
        if (VipType.pro.getValue().equalsIgnoreCase(priceInfo.getPlanLevel())) {
            String priceInterval = priceInfo.getPriceInterval();
            if ("year".equalsIgnoreCase(priceInterval)) {
                priceInfo.setOriginalPriceMonth("28.99");
                if (priceInfo.getOff() == 0) {
                    priceInfo.setDiscountedPrice(new BigDecimal(priceInfo.getOriginalPrice()).divide(new BigDecimal(12), 2, RoundingMode.HALF_DOWN).toString());
                    priceInfo.setFrontOff(24);
                }
            } else {
                priceInfo.setOriginalPriceMonth(priceInfo.getOriginalPrice());
            }
        }

    }


    @PostMapping("deviceInfoReport")
    @Operation(summary = "上报移动端用户设备信息")
    @Authorization
    public R<Void> deviceInfoReport(@RequestBody UserDeviceInfoReportReq req, @Parameter(hidden = true) @CurrentUser User user, HttpServletRequest request) {
        try {
            String platform = request.getHeader("Platform").toLowerCase();
            aiUserDeviceInfoService.deviceInfoReport(req, user, platform);
        } catch (Exception e) {
            dingTalkAlert.send("用户设备信息上报失败, 用户设备信息上报失败，异常信息：" + e.getMessage());
        }
        return R.success();
    }
}
