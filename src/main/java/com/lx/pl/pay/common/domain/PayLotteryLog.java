

package com.lx.pl.pay.common.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lx.pl.db.mysql.gen.entity.MyBaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@TableName("pay_lottery_log")
@Schema(description = "抽奖日志")
public class PayLotteryLog extends MyBaseEntity {
    @TableId(value = "id")
    @Schema(description = "ID")
    private Long id;
    @Schema(description = "uid抽奖ID")
    private String uid;
    @Schema(description = "用户ID")
    private Long userId;
    @Schema(description = "用户登录名称")
    private String loginName;
    @Schema(description = "平台")
    private String platform;
    private String award;
    @Schema(description = "关联ID")
    private Long relationId;
    @Schema(description = "关联CODE")
    private String relationCode;
    @Schema(description = "产品ID")
    private String productId;
    @Schema(description = "折扣链接")
    private String url;
    @Schema(description = "折扣码")
    private String code;
}

