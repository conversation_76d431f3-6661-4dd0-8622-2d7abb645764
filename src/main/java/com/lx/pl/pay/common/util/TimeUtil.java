package com.lx.pl.pay.common.util;

import java.time.*;
import java.time.format.DateTimeFormatter;

public class TimeUtil {
    // 获取零时区的最后一秒
    public static long convertToUtcLastSecond(long timestamp) {
        Instant instant = Instant.ofEpochSecond(timestamp);
        // 将 Instant 转换为 UTC 时间的 LocalDate
        LocalDate currentDate = instant.atZone(ZoneOffset.UTC).toLocalDate();
        // 将当前日期转换为 UTC 时间的午夜 23:59:59
        ZonedDateTime endOfDayUtc = currentDate.atTime(23, 59, 59).atZone(ZoneOffset.UTC);
        // 返回时间戳
        return endOfDayUtc.toEpochSecond();
//        return utc2.toEpochSecond();
    }

    public static long convertToUtcFirstSecond(long timestamp) {
        // 将传入的时间戳转换为 Instant
        Instant instant = Instant.ofEpochSecond(timestamp);
        // 将 Instant 转换为 UTC 时间的 LocalDate
        LocalDate currentDate = instant.atZone(ZoneOffset.UTC).toLocalDate();
        // 将当前日期转换为 UTC 时间的午夜 00:00:00
        ZonedDateTime startOfDayUtc = currentDate.atStartOfDay(ZoneOffset.UTC);
        // 返回时间戳
        return startOfDayUtc.toEpochSecond();
    }

    public static void main(String[] args) {
        // 时间戳转UTC 和 本地时间

        long x = convertToUtcFirstSecond(1774421700L);
        System.out.println(x);
        long x1 = convertToUtcLastSecond(1777100100);
        System.out.println(x1);

        convertTimestampToUtcAndLocal(1774421700L);
        convertTimestampToUtcAndLocal(1777100100);
        convertTimestampToUtcAndLocal(x);
        convertTimestampToUtcAndLocal(x1);


    }


    public static void convertTimestampToUtcAndLocal(long timestamp) {
        // 将时间戳转换为 Instant
        Instant instant = Instant.ofEpochSecond(timestamp);

        // 转换为 UTC 时间
        ZonedDateTime utcTime = instant.atZone(ZoneOffset.UTC);

        // 转换为本地时间
        ZonedDateTime localTime = instant.atZone(ZoneId.systemDefault());

        // 格式化时间
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 输出结果
        System.out.println("UTC Time: " + utcTime.format(formatter));
        System.out.println("Local Time: " + localTime.format(formatter));
    }

    public static Long convertToUtcTimestamp(String timeStrUtc) {
        DateTimeFormatter formatter = DateTimeFormatter.ISO_INSTANT;
        Instant instant = Instant.from(formatter.parse(timeStrUtc));
        long timestamp = instant.getEpochSecond();
        return timestamp;
    }
}
