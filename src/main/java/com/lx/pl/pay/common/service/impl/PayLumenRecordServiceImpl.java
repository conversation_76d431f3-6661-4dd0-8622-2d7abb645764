package com.lx.pl.pay.common.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.lx.pl.db.mysql.gen.entity.PayAppleProduct;
import com.lx.pl.db.mysql.gen.entity.StripeProduct;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.enums.LumenChangeSourceEnum;
import com.lx.pl.enums.LumenChangeTypeEnum;
import com.lx.pl.enums.LumenType;
import com.lx.pl.pay.apple.VipPlatform;
import com.lx.pl.pay.apple.domain.PayApplePurchaseRecord;
import com.lx.pl.pay.apple.service.impl.PayAppleProductServiceImpl;
import com.lx.pl.pay.apple.service.impl.PayApplePurchaseRecordServiceImpl;
import com.lx.pl.pay.common.domain.PayLumenRecord;
import com.lx.pl.pay.common.domain.SubscriptionCurrent;
import com.lx.pl.pay.common.dto.SavePaymentRecordRequest;
import com.lx.pl.pay.common.enums.LumenRecordDetailEnum;
import com.lx.pl.pay.common.enums.PaymentDetailEnum;
import com.lx.pl.pay.common.enums.PaymentPlatform;
import com.lx.pl.pay.common.enums.PaymentSourceEnum;
import com.lx.pl.pay.common.mapper.PayLumenRecordMapper;
import com.lx.pl.pay.common.service.PayLumenRecordService;
import com.lx.pl.pay.common.service.SubscriptionCurrentService;
import com.lx.pl.pay.common.service.UserPayRecordService;
import com.lx.pl.pay.common.util.PayConstant;
import com.lx.pl.pay.google.domain.GoogleOneTimePurchases;
import com.lx.pl.pay.google.domain.GooglePlayOrder;
import com.lx.pl.pay.google.domain.GoogleProduct;
import com.lx.pl.pay.google.domain.GoogleSubscriptionHistory;
import com.lx.pl.pay.google.service.GoogleProductService;
import com.lx.pl.pay.google.service.impl.GoogleSubscriptionHistoryServiceImpl;
import com.lx.pl.pay.paypal.model.PaymentCaptureDetails;
import com.lx.pl.pay.paypal.model.domain.PayPalLogicSubscription;
import com.lx.pl.pay.paypal.model.domain.PayPalOrderPaymentItem;
import com.lx.pl.pay.paypal.model.domain.PayPalOrderPaymentRecord;
import com.lx.pl.pay.paypal.model.domain.PayPalProduct;
import com.lx.pl.pay.paypal.service.PayPalOrderPaymentItemService;
import com.lx.pl.pay.paypal.service.PayPalProductService;
import com.lx.pl.pay.paypal.service.impl.PayPalOrderPaymentItemServiceImpl;
import com.lx.pl.pay.paypal.service.impl.PayPalProductServiceImpl;
import com.lx.pl.pay.stripe.domain.PayLogicPurchaseRecord;
import com.lx.pl.pay.stripe.service.impl.PayLogicPurchaseRecordServiceImpl;
import com.lx.pl.service.*;
import com.lx.pl.util.DateUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.*;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.lx.pl.pay.apple.VipPlatform.PAYPAL;

/**
 * Lumen 记录服务实现类
 *
 * <AUTHOR>
 */
@Service
public class PayLumenRecordServiceImpl extends ServiceImpl<PayLumenRecordMapper, PayLumenRecord> implements PayLumenRecordService, ApplicationContextAware {

    static Logger log = LoggerFactory.getLogger("stripe-pay-msg");
    static Logger logApple = LoggerFactory.getLogger("apple-pay-msg");
    static Logger logGoogle = LoggerFactory.getLogger("google-pay-msg");
    static Logger logPaypal = LoggerFactory.getLogger("paypal-pay-msg");

    @Autowired
    RedisService redisService;
    @Resource
    private LumenChangeRecordService lumenChangeRecordService;
    @Resource
    private StripeProductService stripeProductService;
    private ApplicationContext applicationContext;

    @Autowired
    private PayPalProductService productService;
    @Autowired
    private GoogleProductService googleProductService;
    @Autowired
    private PayPalOrderPaymentItemService payPalOrderPaymentItemService;
    @Autowired
    private UserPayRecordService userPayRecordService;


    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @Lazy
    private VipService vipService;
    @Autowired
    private SubscriptionCurrentService subscriptionCurrentService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveLumenRecord(PayLogicPurchaseRecord payLogicPurchaseRecord, StripeProduct stripeProduct) {
        List<PayLumenRecord> payLogicPurchaseRecords = generatePeriods(payLogicPurchaseRecord);
        if (payLogicPurchaseRecords != null && !payLogicPurchaseRecords.isEmpty()) {
            log.info("saveLumenRecord: {}", payLogicPurchaseRecords);
            this.saveBatch(payLogicPurchaseRecords);
            String detail = StrUtil.upperFirst(stripeProduct.getPlanLevel());
            long now = System.currentTimeMillis() / 1000;
            for (PayLumenRecord lumenRecord : payLogicPurchaseRecords) {
                lumenChangeRecordService.saveOrUpdateLumenChangeRecord(lumenRecord.getUserId(), lumenRecord.getLoginName(), lumenRecord.getLumenQty(), LumenChangeTypeEnum.ADD.getValue(), LumenChangeSourceEnum.SUBSCRIBE.getValue(), String.format(LumenRecordDetailEnum.SUBSCRIPTION_MONTHLY_LUMENS.getName(), detail), lumenRecord.getId(), lumenRecord.getLogicPeriodStart() < now ? now : lumenRecord.getLogicPeriodStart());
            }

        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recallLumen(Long payLogicPurchaseRecordId, String message) {
        log.info("recallLumen: {}", payLogicPurchaseRecordId);
        boolean update = this.lambdaUpdate().eq(PayLumenRecord::getPayLogicPurchaseRecordId, payLogicPurchaseRecordId).set(PayLumenRecord::getInvalid, true)
                .set(PayLumenRecord::getInvalidMessage, message).set(PayLumenRecord::getUpdateTime, LocalDateTime.now()).update();


        String detail = null;
        List<PayLumenRecord> list = this.lambdaQuery().eq(PayLumenRecord::getPayLogicPurchaseRecordId, payLogicPurchaseRecordId).list();
        long now = System.currentTimeMillis() / 1000;
        for (PayLumenRecord lumenRecord : list) {
            // 开始时间未来 直接删除
            if (lumenRecord.getLogicPeriodStart() > System.currentTimeMillis() / 1000) {
                lumenChangeRecordService.removeLumenChangeRecordByRelationId(lumenRecord.getId());
            } else if (lumenRecord.getLogicPeriodEnd() > System.currentTimeMillis() / 1000) {
                // 结束时间已过 直接删除
                if (lumenRecord.getLumenLeftQty() > 0) {
                    if (detail == null) {
                        detail = buildDetailPrefix(payLogicPurchaseRecordId, lumenRecord);
                    }
                    lumenChangeRecordService.saveOrUpdateLumenChangeRecord(lumenRecord.getUserId(), lumenRecord.getLoginName(), lumenRecord.getLumenLeftQty(), LumenChangeTypeEnum.DEDUCT.getValue(), LumenChangeSourceEnum.SUBSCRIBE.getValue(), String.format(LumenRecordDetailEnum.SUBSCRIPTION_MONTHLY_LUMENS.getName(), detail), lumenRecord.getId(), lumenRecord.getLogicPeriodStart() < now ? now : lumenRecord.getLogicPeriodStart());
                }
            }
        }
        log.info("end recallLumen: {} {}", payLogicPurchaseRecordId, update);
    }

    private String buildDetailPrefix(Long payLogicPurchaseRecordId, PayLumenRecord lumenRecord) {
        try {
            if (lumenRecord.getVipPlatForm().equals(VipPlatform.STRIPE.getPlatformName())) {
                PayLogicPurchaseRecord byId = applicationContext.getBean(PayLogicPurchaseRecordServiceImpl.class).getById(payLogicPurchaseRecordId);
                if (byId == null) {
                    log.info("recallLumen not found payLogicPurchaseRecord: {}", payLogicPurchaseRecordId);
                    return null;
                }
                String priceId = byId.getPriceId();
                StripeProduct stripeProductByPriceId = stripeProductService.getStripeProductByPriceId(priceId);
                if (stripeProductByPriceId == null) {
                    log.info("recallLumen not found stripeProduct: {}", priceId);
                    return null;
                }
                return StrUtil.upperFirst(stripeProductByPriceId.getPlanLevel());
            }
            if (lumenRecord.getVipPlatForm().equals(VipPlatform.IOS.getPlatformName())) {
                PayApplePurchaseRecord byId = applicationContext.getBean(PayApplePurchaseRecordServiceImpl.class).getById(payLogicPurchaseRecordId);
                if (byId == null) {
                    log.info("recallLumen not found payApplePurchaseRecord: {}", payLogicPurchaseRecordId);
                    return null;
                }
                PayAppleProduct payAppleProduct = applicationContext.getBean(PayAppleProductServiceImpl.class).getById(byId.getProductId());
                if (payAppleProduct == null) {
                    log.info("recallLumen not found payAppleProduct: {}", byId.getProductId());
                    return null;
                }
                return StrUtil.upperFirst(payAppleProduct.getPlanLevel());
            }
            if (lumenRecord.getVipPlatForm().equals(VipPlatform.PAYPAL.getPlatformName())) {
                PayPalOrderPaymentItem byId = applicationContext.getBean(PayPalOrderPaymentItemServiceImpl.class).getById(payLogicPurchaseRecordId);
                if (byId == null) {
                    log.info("recallLumen not found payPalOrderPaymentItem: {}", payLogicPurchaseRecordId);
                    return null;
                }
                PayPalProduct payPalProduct = applicationContext.getBean(PayPalProductServiceImpl.class).getById(byId.getProductId());
                if (payPalProduct == null) {
                    log.info("recallLumen not found payPalProduct: {}", byId.getProductId());
                    return null;
                }
                return StrUtil.upperFirst(payPalProduct.getPlanLevel());
            }

            if (lumenRecord.getVipPlatForm().equals(VipPlatform.GOOGLE.getPlatformName())) {
                GoogleSubscriptionHistory byId = applicationContext.getBean(GoogleSubscriptionHistoryServiceImpl.class).getById(payLogicPurchaseRecordId);
                if (byId == null) {
                    log.info("recallLumen not found payPalOrderPaymentItem: {}", payLogicPurchaseRecordId);
                    return null;
                }
                PayPalProduct payPalProduct = applicationContext.getBean(PayPalProductServiceImpl.class).getById(byId.getProductId());
                if (payPalProduct == null) {
                    log.info("recallLumen not found payPalProduct: {}", byId.getProductId());
                    return null;
                }
                return StrUtil.upperFirst(payPalProduct.getPlanLevel());
            }
        } catch (Exception e) {
            log.error("buildDetailPrefix error: {}", e.getMessage(), e);
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOneTimeLumenRecord(List<PayLogicPurchaseRecord> payLogicPurchaseRecordList) {
        log.info("start saveOneTimeLumen for {} ", payLogicPurchaseRecordList.size());
        List<PayLumenRecord> payLumenRecords = new ArrayList<>();
        long nowSec = System.currentTimeMillis() / 1000;
        for (PayLogicPurchaseRecord payLogicPurchaseRecord : payLogicPurchaseRecordList) {
            log.info("start saveOneTimeLumenRecord: {}", payLogicPurchaseRecord);
            PayLumenRecord record = new PayLumenRecord();
            record.setPayLogicPurchaseRecordId(payLogicPurchaseRecord.getId());
            record.setUserId(payLogicPurchaseRecord.getUserId());
            record.setLoginName(payLogicPurchaseRecord.getLoginName());
            record.setCustomerId(payLogicPurchaseRecord.getCustomerId());
            record.setType(LumenType.recharge.getValue());
            ZonedDateTime now = ZonedDateTime.now(ZoneOffset.UTC);
            record.setCurrentPeriodStart(now.toEpochSecond());
            // end day 为
            ZonedDateTime zonedDateTime = now.plusYears(1);
            ZonedDateTime endOfDay = zonedDateTime.toLocalDate().atTime(23, 59, 59, 999999999).atZone(now.getZone());
            record.setCurrentPeriodEnd(endOfDay.toEpochSecond());
            record.setVipPlatForm(payLogicPurchaseRecord.getVipPlatForm());
            record.setLogicPeriodStart(record.getCurrentPeriodStart());
            record.setLogicPeriodEnd(record.getCurrentPeriodEnd());

            record.setLumenQty(payLogicPurchaseRecord.getLumenQty() * payLogicPurchaseRecord.getCount());
            record.setLumenLeftQty(payLogicPurchaseRecord.getLumenQty() * payLogicPurchaseRecord.getCount());
            record.setMixed(false);
            record.setInvalid(false);
            record.setCreateTime(LocalDateTime.now());
            payLumenRecords.add(record);
            log.info("end saveOneTimeLumenRecord: {}", record);
            lumenChangeRecordService.saveOrUpdateLumenChangeRecord(record.getUserId(), record.getLoginName(), record.getLumenQty(), LumenChangeTypeEnum.ADD.getValue(), LumenChangeSourceEnum.PURCHASE.getValue(), LumenRecordDetailEnum.PURCHASE_LUMENS.getName(), record.getId(), record.getLogicPeriodStart() > nowSec ? nowSec : record.getLogicPeriodStart());
        }
        this.saveBatch(payLumenRecords);
        log.info("end saveOneTimeLumenRecord, total save {} price", payLumenRecords.size());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveLumenRecordForApple(PayApplePurchaseRecord record, PayAppleProduct product) {
        logApple.info("start saveLumenRecordForApple {} {}", record.getUserId(), record.getTransactionId());
        String detail = StrUtil.upperFirst(product.getPlanLevel());
        List<PayLumenRecord> payLumenRecords = generatePeriodsApple(record);
        if (!payLumenRecords.isEmpty()) {
            this.saveBatch(payLumenRecords);
            long now = System.currentTimeMillis() / 1000;
            for (PayLumenRecord lumenRecord : payLumenRecords) {
                lumenChangeRecordService.saveOrUpdateLumenChangeRecord(lumenRecord.getUserId(), lumenRecord.getLoginName(), lumenRecord.getLumenQty(), LumenChangeTypeEnum.ADD.getValue(), LumenChangeSourceEnum.SUBSCRIBE.getValue(), String.format(LumenRecordDetailEnum.SUBSCRIPTION_MONTHLY_LUMENS.getName(), detail), lumenRecord.getId(), lumenRecord.getLogicPeriodStart() < now ? now : lumenRecord.getLogicPeriodStart());
            }
        }
        logApple.info("end saveLumenRecordForApple: {}", payLumenRecords);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOneTimeLumenRecordForApple(PayApplePurchaseRecord record) {
        PayLumenRecord saveRecord = new PayLumenRecord();
        saveRecord.setPayLogicPurchaseRecordId(record.getId());

        saveRecord.setUserId(record.getUserId());
        saveRecord.setLoginName(record.getLoginName());
        saveRecord.setType(LumenType.recharge.getValue());
        ZonedDateTime now = ZonedDateTime.now(ZoneOffset.UTC);
        ZonedDateTime zonedDateTime = now.plusYears(1);
        ZonedDateTime endOfDay = zonedDateTime.toLocalDate().atTime(23, 59, 59, 999999999).atZone(now.getZone());
        saveRecord.setCurrentPeriodStart(record.getPurchaseDate());
        saveRecord.setCurrentPeriodEnd(endOfDay.toEpochSecond());
        saveRecord.setLogicPeriodStart(now.toEpochSecond());
        saveRecord.setLogicPeriodEnd(endOfDay.toEpochSecond());

        saveRecord.setVipPlatForm(record.getVipPlatForm());

        record.setOriginalTransactionId(record.getOriginalTransactionId());
        record.setTransactionId(record.getTransactionId());

        saveRecord.setLumenQty(record.getLumenQty() * record.getCount());
        saveRecord.setInvalid(false);
        saveRecord.setLumenLeftQty(record.getLumenQty() * record.getCount());
        saveRecord.setMixed(false);
        saveRecord.setCreateTime(LocalDateTime.now());
        this.save(saveRecord);

        long nowSec = System.currentTimeMillis() / 1000;
        lumenChangeRecordService.saveOrUpdateLumenChangeRecord(saveRecord.getUserId(), saveRecord.getLoginName(), saveRecord.getLumenQty(), LumenChangeTypeEnum.ADD.getValue(), LumenChangeSourceEnum.PURCHASE.getValue(), LumenRecordDetailEnum.PURCHASE_LUMENS.getName(), saveRecord.getId(), saveRecord.getLogicPeriodStart() > nowSec ? nowSec : saveRecord.getLogicPeriodStart());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveLumenRecordForPayPal(PayPalLogicSubscription payPalLogicSubscription, PayPalProduct payPalPlan) {
        logPaypal.info("start saveLumenRecordForPayPal {} {} {}", payPalLogicSubscription.getUserId(), payPalLogicSubscription.getSubscriptionId(), payPalLogicSubscription.getSubStartSec());
        List<PayLumenRecord> payLumenRecords = generatePeriodsPaypal(payPalLogicSubscription, payPalPlan);
        if (!payLumenRecords.isEmpty()) {
            this.saveBatch(payLumenRecords);
            String detail = StrUtil.upperFirst(payPalPlan.getPlanLevel());
            long now = System.currentTimeMillis() / 1000;
            for (PayLumenRecord lumenRecord : payLumenRecords) {
                lumenChangeRecordService.saveOrUpdateLumenChangeRecord(lumenRecord.getUserId(), lumenRecord.getLoginName(), lumenRecord.getLumenQty(), LumenChangeTypeEnum.ADD.getValue(), LumenChangeSourceEnum.SUBSCRIBE.getValue(), String.format(LumenRecordDetailEnum.SUBSCRIPTION_MONTHLY_LUMENS.getName(), detail), lumenRecord.getId(), lumenRecord.getLogicPeriodStart() < now ? now : lumenRecord.getLogicPeriodStart());
            }
            logPaypal.info("end saveLumenRecordForPayPal: {}", payLumenRecords);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void recallLumenForPaypal(Long orderId, String message) {
        log.info("recallLumen: {}", orderId);
        List<PayLumenRecord> list = this.lambdaQuery().eq(PayLumenRecord::getOrderId, orderId).list();
        boolean update = this.lambdaUpdate().eq(PayLumenRecord::getOrderId, orderId).set(PayLumenRecord::getInvalid, true).set(PayLumenRecord::getInvalidMessage, message)
                .set(PayLumenRecord::getUpdateTime, LocalDateTime.now()).update();

        String detail = null;
        long now = System.currentTimeMillis() / 1000;
        for (PayLumenRecord lumenRecord : list) {
            // 开始时间未来 直接删除
            if (lumenRecord.getLogicPeriodStart() > System.currentTimeMillis() / 1000) {
                lumenChangeRecordService.removeLumenChangeRecordByRelationId(lumenRecord.getId());
            } else if (lumenRecord.getLogicPeriodEnd() > System.currentTimeMillis() / 1000) {
                // 结束时间已过 直接删除
                if (lumenRecord.getLumenLeftQty() > 0) {
                    if (detail == null) {
                        detail = buildDetailPrefix(lumenRecord.getPayLogicPurchaseRecordId(), lumenRecord);
                    }
                    lumenChangeRecordService.saveOrUpdateLumenChangeRecord(lumenRecord.getUserId(), lumenRecord.getLoginName(), lumenRecord.getLumenLeftQty(), LumenChangeTypeEnum.DEDUCT.getValue(), LumenChangeSourceEnum.SUBSCRIBE.getValue(), String.format(LumenRecordDetailEnum.SUBSCRIPTION_MONTHLY_LUMENS_RECYCLE.getName(), detail), lumenRecord.getId(), lumenRecord.getLogicPeriodStart() < now ? now : lumenRecord.getLogicPeriodStart());
                }
            }
        }
        log.info("end recallLumen: {} {}", orderId, update);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOneTimeLumenForPaypal(List<PayPalOrderPaymentRecord> existRecords, PaymentCaptureDetails model) {

        User userById = applicationContext.getBean(UserService.class).getUserById(existRecords.get(0).getUserId());
        boolean hasPurchasedLumen = this.hasPurchasedLumen(userById.getId());
        List<PayLumenRecord> recordList = new ArrayList<>();
        for (PayPalOrderPaymentRecord existRecord1 : existRecords) {
            String orderId = existRecord1.getOrderId();
            if (!this.queryByOrderId(orderId).isEmpty()) {
                log.info("order already exists: {}", orderId);
                continue;
            }
            log.info("start saveOneTimeLumen {} {} {}", existRecord1.getUserId(), existRecord1.getLoginName(), existRecord1.getAmount());

            List<PayPalOrderPaymentItem> byPaymentId = payPalOrderPaymentItemService.findByOrderId(orderId);
            for (PayPalOrderPaymentItem payPalOrderPaymentItem : byPaymentId) {
                log.info("start saveOneTimeLumen {} {}", payPalOrderPaymentItem.getUserId(), payPalOrderPaymentItem.getProductId());
                PayLumenRecord record = new PayLumenRecord();
                record.setPayLogicPurchaseRecordId(payPalOrderPaymentItem.getId());
                record.setOrderId(orderId);

                record.setUserId(payPalOrderPaymentItem.getUserId());
                record.setLoginName(payPalOrderPaymentItem.getLoginName());
                record.setCustomerId(payPalOrderPaymentItem.getUserId().toString());
                record.setType(LumenType.recharge.getValue());
                ZonedDateTime now = ZonedDateTime.now(ZoneOffset.UTC);
                record.setCurrentPeriodStart(now.toEpochSecond());
                // end day 为
                ZonedDateTime zonedDateTime = now.plusYears(1);
                ZonedDateTime endOfDay = zonedDateTime.toLocalDate().atTime(23, 59, 59, 999999999).atZone(now.getZone());
                record.setCurrentPeriodEnd(endOfDay.toEpochSecond());
                record.setVipPlatForm(PAYPAL.getPlatformName());
                record.setLogicPeriodStart(record.getCurrentPeriodStart());
                record.setLogicPeriodEnd(record.getCurrentPeriodEnd());
                String referenceId = payPalOrderPaymentItem.getProductId();
                PayPalProduct paypalProductByPlanId = productService.getPaypalProductByPlanId(referenceId);
                if (paypalProductByPlanId != null) {
                    record.setLumenQty(paypalProductByPlanId.getLumen() * ((payPalOrderPaymentItem.getQty() == null || payPalOrderPaymentItem.getQty() <= 0) ? 1 : payPalOrderPaymentItem.getQty()));
                } else {
                    record.setLumenQty(500);
                    log.info("paypalProductByPlanId is null");
                }
                record.setLumenLeftQty(record.getLumenQty());
                record.setMixed(false);
                record.setInvalid(false);
                record.setCreateTime(LocalDateTime.now());
                recordList.add(record);
                if (!hasPurchasedLumen && paypalProductByPlanId != null && paypalProductByPlanId.getInitialLumen() > 0) {
                    PayLumenRecord firstChargeRecord = new PayLumenRecord();
                    BeanUtils.copyProperties(record, firstChargeRecord);
                    firstChargeRecord.setLumenQty(paypalProductByPlanId.getInitialLumen());
                    firstChargeRecord.setLumenLeftQty(firstChargeRecord.getLumenQty());
                    firstChargeRecord.setType(LumenType.recharge.getValue());
                    recordList.add(firstChargeRecord);
                }

                log.info("end saveOneTimeLumen {} {}", payPalOrderPaymentItem.getUserId(), payPalOrderPaymentItem.getProductId());
            }
            savePayPalCaptureUserPayRecord(model, existRecord1, byPaymentId, hasPurchasedLumen);
        }
        this.saveBatch(recordList);
        long now = System.currentTimeMillis() / 1000;
        recordList.forEach(record -> {
            lumenChangeRecordService.saveOrUpdateLumenChangeRecord(record.getUserId(), record.getLoginName(), record.getLumenQty(), LumenChangeTypeEnum.ADD.getValue(), LumenChangeSourceEnum.PURCHASE.getValue(), LumenRecordDetailEnum.PURCHASE_LUMENS.getName(), record.getId(), record.getLogicPeriodStart() < now ? now : record.getLogicPeriodStart());
        });
        log.info("end saveOneTimeLumenRecord: {}", recordList);
    }

    /**
     * 保存PayPal支付捕获的用户支付记录
     *
     * @param captureModel      PayPal支付捕获详情
     * @param paymentRecord     PayPal订单支付记录
     * @param byPaymentId
     * @param hasPurchasedLumen
     */
    private void savePayPalCaptureUserPayRecord(PaymentCaptureDetails captureModel, PayPalOrderPaymentRecord paymentRecord, List<PayPalOrderPaymentItem> byPaymentId, boolean hasPurchasedLumen) {
        try {
            // 获取订单ID
            String orderId = null;
            if (captureModel.getSupplementaryData() != null && captureModel.getSupplementaryData().getRelatedIds() != null) {
                orderId = captureModel.getSupplementaryData().getRelatedIds().getOrderId();
            }

            if (orderId == null) {
                log.warn("Cannot find orderId from capture model: {}", captureModel.getId());
                return;
            }

            // 检查幂等性
            if (userPayRecordService.checkPayPalIdempotency(null, orderId) != null) {
                log.info("PayPal capture user pay record already exists for orderId: {}, captureId: {}", orderId, captureModel.getId());
                return;
            }

            SavePaymentRecordRequest request = new SavePaymentRecordRequest();
            request.setLoginName(paymentRecord.getLoginName());
            request.setUserId(paymentRecord.getUserId());
            request.setPlatform(PaymentPlatform.PAYPAL.getCode());
            request.setExternalTransactionId(captureModel.getId());
            request.setExternalOrderId(orderId);
            request.setPaymentStatus("completed");

            // 设置金额信息
            if (captureModel.getAmount() != null && captureModel.getAmount().getValue() != null) {
                try {
                    double amount = Double.parseDouble(captureModel.getAmount().getValue());
                    Long amountInCents = Math.round(amount * 1000);
                    request.setAfterDiscountAmount(amountInCents);
                    PaymentCaptureDetails.SellerReceivableBreakdown sellerReceivableBreakdown = captureModel.getSellerReceivableBreakdown();
                    if (sellerReceivableBreakdown != null && sellerReceivableBreakdown.getNetAmount() != null) {
                        Double netAmount = Double.parseDouble(sellerReceivableBreakdown.getNetAmount().getValue());
                        request.setAmountExcludingTax((long) (netAmount * 1000));
                    } else {
                        request.setAmountExcludingTax(request.getAmount() * 1000);
                    }

                } catch (NumberFormatException e) {
                    log.warn("Failed to parse capture amount: {}", captureModel.getAmount().getValue());
                }
            }
            String customId = captureModel.getCustomId();
            Integer off = 0;
            if (customId != null) {
                JSONObject customData = JSONUtil.parseObj(customId);
                off = customData.getInt(PayConstant.OFF_META_KEY);
            }
            request.setPercentOff(off);

            BigDecimal percent = (new BigDecimal(100).subtract(new BigDecimal(off))).divide(new BigDecimal(100), 2, RoundingMode.HALF_DOWN);

            // 设置货币
            if (captureModel.getAmount() != null && captureModel.getAmount().getCurrencyCode() != null) {
                request.setCurrency(captureModel.getAmount().getCurrencyCode());
            }

            // 设置来源和详情（捕获支付通常是一次性支付）
            String source = PaymentSourceEnum.PURCHASE_LUMENS.getName();
            PaymentDetailEnum paymentDetail = PaymentDetailEnum.LUMENS_PURCHASE;

            request.setSource(source);

            // 创建支付记录项
            List<SavePaymentRecordRequest.PaymentRecordItemRequest> items = new ArrayList<>();
            Integer totalRequestLumen = 0;
            Long totalAmount = 0L;

            for (PayPalOrderPaymentItem payPalOrderPaymentItem : byPaymentId) {
                SavePaymentRecordRequest.PaymentRecordItemRequest item = new SavePaymentRecordRequest.PaymentRecordItemRequest();
                item.setProductType("one");
                item.setQty(payPalOrderPaymentItem.getQty());
                item.setPercentOff(off);

                // 查询产品信息
                PayPalProduct paypalProductByPlanId = productService.getPaypalProductByPlanId(payPalOrderPaymentItem.getProductId());
                String price = paypalProductByPlanId.getPrice();
                BigDecimal priceBig = new BigDecimal(price);
//                BigDecimal priceAfterDiscount = priceBig.multiply(BigDecimal.valueOf(item.getQty())).multiply(percent);

                // 设置单位金额
                long longedValue = priceBig.multiply(BigDecimal.valueOf(1000L)).longValue();
                item.setUnitAmount(longedValue);
                totalAmount += longedValue * item.getQty();


                // 设置总金额（从PayPal订单项获取实际支付金额）
                String amount = payPalOrderPaymentItem.getAmount();
                Long totalAmountInCents = Math.round(Double.parseDouble(amount) * 1000);
                item.setTotalAmount(totalAmountInCents);

                // 设置货币
                if (captureModel.getAmount() != null && captureModel.getAmount().getCurrencyCode() != null) {
                    item.setCurrency(captureModel.getAmount().getCurrencyCode());
                }

                // 设置Lumen信息
                item.setUnitLumen(paypalProductByPlanId.getLumen());
                item.setGiftLumen(0);
                if (paypalProductByPlanId.getInitialLumen() != null && !hasPurchasedLumen) {
                    item.setGiftLumen(paypalProductByPlanId.getInitialLumen());
                }
                item.setTotalLumen(item.getUnitLumen() * item.getQty() + item.getGiftLumen());

                // 累计总金额和总Lumen
                totalRequestLumen += item.getTotalLumen();

                items.add(item);
            }
            request.setAmount(totalAmount);
            request.setTotalLumen(totalRequestLumen.longValue());
            request.setDetail(String.format(paymentDetail.getName(), request.getTotalLumen() != null ? request.getTotalLumen() : 0));

            request.setItems(items);

            // 保存支付记录
            userPayRecordService.savePaymentRecord(request);
            log.info("Successfully saved PayPal capture user pay record for userId: {}, captureId: {}", paymentRecord.getUserId(), captureModel.getId());

        } catch (Exception e) {
            log.error("Failed to save PayPal capture user pay record for captureId: {}", captureModel.getId(), e);
            // 不抛出异常，避免影响主流程
        }
    }

    private List<PayLumenRecord> queryByOrderId(String orderId) {
        return this.lambdaQuery().eq(PayLumenRecord::getOrderId, orderId).list();
    }

    @Override
    public List<PayLumenRecord> queryByLogicId(Long logicId) {
        return this.lambdaQuery().eq(PayLumenRecord::getPayLogicPurchaseRecordId, logicId).eq(PayLumenRecord::getInvalid, false).list();
    }

    private List<PayLumenRecord> generatePeriodsPaypal(PayPalLogicSubscription payPalLogicSubscription, PayPalProduct paypalPlan) {
        long periodStart = LocalDateTime.now().toEpochSecond(ZoneOffset.UTC);
        String priceInterval = paypalPlan.getPriceInterval();
        int monthsCount = 1;
        if ("year".equals(priceInterval)) {
            monthsCount = (12);
        }

        PayLumenRecord record = new PayLumenRecord();
        record.setCreateTime(LocalDateTime.now());
        record.setPayLogicPurchaseRecordId(payPalLogicSubscription.getId());
        record.setUserId(payPalLogicSubscription.getUserId());
        record.setLoginName(payPalLogicSubscription.getLoginName());
        record.setType(LumenType.vip.getValue());
        record.setLumenQty(paypalPlan.getLumen());
        record.setLumenLeftQty(paypalPlan.getLumen());
        record.setVipPlatForm(PAYPAL.getPlatformName());

        record.setMixed(false);
        record.setInvalid(false);
        record.setCreateTime(LocalDateTime.now());
        return getPayLumenRecords(record, periodStart, monthsCount);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void restoreLumenByUserId(String originalTransactionId, Long newUserId, String loginName) {
        List<PayLumenRecord> list = this.lambdaQuery().eq(PayLumenRecord::getOriginalTransactionId, originalTransactionId).eq(PayLumenRecord::getInvalid, false)
                .gt(PayLumenRecord::getLogicPeriodEnd, System.currentTimeMillis() / 1000 - 10).list();
        if (!list.isEmpty()) {
            Long now = System.currentTimeMillis() / 1000;
            log.info("restoreLumenByUserId: {}", list);
            for (PayLumenRecord lumenRecord : list) {
                lumenChangeRecordService.saveOrUpdateLumenChangeRecord(lumenRecord.getUserId(), lumenRecord.getLoginName(), lumenRecord.getLumenLeftQty(), LumenChangeTypeEnum.DEDUCT.getValue(), LumenChangeSourceEnum.SUBSCRIBE.getValue(), LumenRecordDetailEnum.LUMENS_RESTORE.getName(), lumenRecord.getId(), lumenRecord.getLogicPeriodStart() < now ? now : lumenRecord.getLogicPeriodStart());
                lumenChangeRecordService.saveOrUpdateLumenChangeRecord(newUserId, loginName, lumenRecord.getLumenLeftQty(), LumenChangeTypeEnum.ADD.getValue(), LumenChangeSourceEnum.SUBSCRIBE.getValue(), LumenRecordDetailEnum.LUMENS_RESTORE.getName(), lumenRecord.getId(), lumenRecord.getLogicPeriodStart() < now ? now : lumenRecord.getLogicPeriodStart());
            }

            this.lambdaUpdate().eq(PayLumenRecord::getOriginalTransactionId, originalTransactionId).eq(PayLumenRecord::getInvalid, false)
                    .gt(PayLumenRecord::getLogicPeriodEnd, System.currentTimeMillis() / 1000 - 10)
                    .set(PayLumenRecord::getLoginName, loginName)
                    .set(PayLumenRecord::getUserId, newUserId)
                    .set(PayLumenRecord::getUpdateTime, LocalDateTime.now())
                    .update();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveLumenRecordForGoogle(GoogleProduct googleProduct, GoogleSubscriptionHistory googleSubscriptionHistory) {
        logApple.info("start saveLumenRecordForGoogle {} {}", googleSubscriptionHistory.getUserId(), googleSubscriptionHistory.getLatestOrderId());
        List<PayLumenRecord> payLumenRecords = generatePeriodsGoogle(googleSubscriptionHistory, googleProduct);
        if (!payLumenRecords.isEmpty()) {
            this.saveBatch(payLumenRecords);
            Long now = System.currentTimeMillis() / 1000;
            String detail = StrUtil.upperFirst(googleProduct.getPlanLevel());
            for (PayLumenRecord payLumenRecord : payLumenRecords) {
                lumenChangeRecordService.saveOrUpdateLumenChangeRecord(payLumenRecord.getUserId(), payLumenRecord.getLoginName(), payLumenRecord.getLumenQty(), LumenChangeTypeEnum.ADD.getValue(), LumenChangeSourceEnum.SUBSCRIBE.getValue(), String.format(LumenRecordDetailEnum.SUBSCRIPTION_MONTHLY_LUMENS.getName(), detail), payLumenRecord.getId(), payLumenRecord.getLogicPeriodStart() < now ? now : payLumenRecord.getLogicPeriodStart());
            }
        }
        logApple.info("end saveLumenRecordForGoogle: {}", payLumenRecords);
    }

    @Override
    public void cancelLumen(GoogleSubscriptionHistory history) {
        logGoogle.info("start cancelLumen {} {}", history.getUserId(), history.getLatestOrderId());

        List<PayLumenRecord> list = this.lambdaQuery().eq(PayLumenRecord::getPayLogicPurchaseRecordId, history.getId()).eq(PayLumenRecord::getVipPlatForm, VipPlatform.GOOGLE.getPlatformName())
                .eq(PayLumenRecord::getInvalid, false).list();
        if (CollUtil.isEmpty(list)) {
            logGoogle.info("no lumen record to cancel {} {}", history.getUserId(), history.getLatestOrderId());
            return;
        }
        this.lambdaUpdate().eq(PayLumenRecord::getPayLogicPurchaseRecordId, history.getId()).eq(PayLumenRecord::getVipPlatForm, VipPlatform.GOOGLE.getPlatformName())
                .eq(PayLumenRecord::getInvalid, false).set(PayLumenRecord::getInvalid, true).set(PayLumenRecord::getUpdateTime, LocalDateTime.now()).update();
        Long now = System.currentTimeMillis() / 1000;
        String detail = null;
        for (PayLumenRecord lumenRecord : list) {
            // 开始时间未来 直接删除
            if (lumenRecord.getLogicPeriodStart() > System.currentTimeMillis() / 1000) {
                lumenChangeRecordService.removeLumenChangeRecordByRelationId(lumenRecord.getId());
            } else if (lumenRecord.getLogicPeriodEnd() > System.currentTimeMillis() / 1000) {
                // 结束时间已过 直接删除
                if (lumenRecord.getLumenLeftQty() > 0) {
                    if (detail == null) {
                        detail = buildDetailPrefix(lumenRecord.getPayLogicPurchaseRecordId(), lumenRecord);
                    }
                    lumenChangeRecordService.saveOrUpdateLumenChangeRecord(lumenRecord.getUserId(), lumenRecord.getLoginName(), lumenRecord.getLumenLeftQty(), LumenChangeTypeEnum.DEDUCT.getValue(), LumenChangeSourceEnum.SUBSCRIBE.getValue(), String.format(LumenRecordDetailEnum.SUBSCRIPTION_MONTHLY_LUMENS_RECYCLE.getName(), detail), lumenRecord.getId(), lumenRecord.getLogicPeriodStart() < now ? now : lumenRecord.getLogicPeriodStart());

                }
            }
        }

    }

    @Override
    public void updateExistRecordExpiretime(String latestOrderId, Long expiryTime) {
        // expiryTime当天的最后1s
        // 将时间戳转换为LocalDateTime
        LocalDateTime dateTime = LocalDateTime.ofInstant(java.time.Instant.ofEpochSecond(expiryTime), ZoneId.of("UTC"));

        // 获取当天的日期
        LocalDate today = dateTime.toLocalDate();
        // 设置时间为当天的23:59:59
        LocalDateTime endOfDay = LocalDateTime.of(today, LocalTime.MAX);
        // 转换为时间戳（毫秒）
        ZonedDateTime zonedDateTime = endOfDay.atZone(ZoneId.of("UTC"));

        this.lambdaUpdate().eq(PayLumenRecord::getLatestOrderId, latestOrderId).eq(PayLumenRecord::getInvalid, false).set(PayLumenRecord::getLogicPeriodEnd, zonedDateTime.toEpochSecond())
                .set(PayLumenRecord::getCurrentPeriodEnd, zonedDateTime.toEpochSecond()).set(PayLumenRecord::getUpdateTime, LocalDateTime.now()).update();
    }

    @Override
    public List<PayLumenRecord> findByLatestOrderId(String orderId) {
        return this.lambdaQuery().eq(PayLumenRecord::getLatestOrderId, orderId).eq(PayLumenRecord::getInvalid, false).list();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelLumenByLatestOrderId(String orderId) {
        log.info("cancelLumenByLatestOrderId: {}", orderId);
        List<PayLumenRecord> list = this.lambdaQuery().eq(PayLumenRecord::getLatestOrderId, orderId).eq(PayLumenRecord::getInvalid, false).list();
        if (list.isEmpty()) {
            log.info("no lumen record to cancel {} {}", orderId);
            return;
        }
        this.lambdaUpdate().eq(PayLumenRecord::getLatestOrderId, orderId).eq(PayLumenRecord::getInvalid, false)
                .set(PayLumenRecord::getInvalid, true)
                .set(PayLumenRecord::getUpdateTime, LocalDateTime.now())
                .update();
        Long now = System.currentTimeMillis() / 1000;
        String detail = null;
        for (PayLumenRecord lumenRecord : list) {
            if (lumenRecord.getLogicPeriodStart() > System.currentTimeMillis() / 1000) {
                lumenChangeRecordService.removeLumenChangeRecordByRelationId(lumenRecord.getId());
            } else if (lumenRecord.getLogicPeriodEnd() > System.currentTimeMillis() / 1000) {
                // 结束时间已过 直接删除
                if (lumenRecord.getLumenLeftQty() > 0) {
                    if (detail == null) {
                        detail = buildDetailPrefix(lumenRecord.getPayLogicPurchaseRecordId(), lumenRecord);
                    }
                    lumenChangeRecordService.saveOrUpdateLumenChangeRecord(lumenRecord.getUserId(), lumenRecord.getLoginName(), lumenRecord.getLumenLeftQty(), LumenChangeTypeEnum.DEDUCT.getValue(), LumenChangeSourceEnum.SUBSCRIBE.getValue(), String.format(LumenRecordDetailEnum.SUBSCRIPTION_MONTHLY_LUMENS_RECYCLE.getName(), detail), lumenRecord.getId(), lumenRecord.getLogicPeriodStart() < now ? now : lumenRecord.getLogicPeriodStart());

                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOneTimeLumenRecordForGoogle(GoogleOneTimePurchases googleOneTimePurchases, GooglePlayOrder googlePlayOrder) {
        boolean hasPurchasedLumen = this.hasPurchasedLumen(googleOneTimePurchases.getUserId());

        List<PayLumenRecord> recordList = new ArrayList<>();
        PayLumenRecord saveRecord = new PayLumenRecord();
        saveRecord.setPayLogicPurchaseRecordId(googleOneTimePurchases.getId());
        saveRecord.setLatestOrderId(googleOneTimePurchases.getOrderId());
        saveRecord.setOrderId(googleOneTimePurchases.getOrderId());

        saveRecord.setUserId(googleOneTimePurchases.getUserId());
        saveRecord.setLoginName(googleOneTimePurchases.getEmail());
        saveRecord.setType(LumenType.recharge.getValue());
        ZonedDateTime now = ZonedDateTime.now(ZoneOffset.UTC);
        ZonedDateTime zonedDateTime = now.plusYears(1);
        ZonedDateTime endOfDay = zonedDateTime.toLocalDate().atTime(23, 59, 59, 999999999).atZone(now.getZone());
        Long purchaseTime = googleOneTimePurchases.getPurchaseTime();
        saveRecord.setCurrentPeriodStart(purchaseTime);
        saveRecord.setCurrentPeriodEnd(endOfDay.toEpochSecond());
        saveRecord.setLogicPeriodStart(now.toEpochSecond());
        saveRecord.setLogicPeriodEnd(endOfDay.toEpochSecond());
        saveRecord.setVipPlatForm(VipPlatform.GOOGLE.getPlatformName());

        saveRecord.setLumenQty(googleOneTimePurchases.getLumenQty() * googleOneTimePurchases.getCount());
        saveRecord.setInvalid(false);
        saveRecord.setLumenLeftQty(googleOneTimePurchases.getLumenQty() * googleOneTimePurchases.getCount());
        saveRecord.setMixed(false);
        saveRecord.setCreateTime(LocalDateTime.now());
        recordList.add(saveRecord);
        GoogleProduct byProductId = googleProductService.findByPlanId(googleOneTimePurchases.getProductId());

        SubscriptionCurrent logicValidHighSubscriptionsFromDb = subscriptionCurrentService.getLogicValidHighSubscriptionsFromDb(googleOneTimePurchases.getUserId());

        if (!hasPurchasedLumen && byProductId != null && byProductId.getInitialLumen() > 0) {
            log.info("saveOneTimeLumenRecordForGoogle:  loginName: {} productId: {} initialLumen: {}", saveRecord.getLoginName(), googleOneTimePurchases.getProductId(), byProductId.getInitialLumen());
            PayLumenRecord firstChargeRecord = new PayLumenRecord();
            BeanUtils.copyProperties(saveRecord, firstChargeRecord);
            firstChargeRecord.setLumenQty(byProductId.getInitialLumen() * googleOneTimePurchases.getCount());
            firstChargeRecord.setLumenLeftQty(byProductId.getInitialLumen() * googleOneTimePurchases.getCount());
            firstChargeRecord.setType(LumenType.recharge.getValue());
            recordList.add(firstChargeRecord);
        }
        if (byProductId != null && byProductId.getRule() != null) {
            addVipGiftLumen(googleOneTimePurchases, logicValidHighSubscriptionsFromDb, saveRecord, byProductId, recordList);
        }
        saveGoogleCaptureUserPayRecord(googleOneTimePurchases, hasPurchasedLumen, googlePlayOrder, byProductId, logicValidHighSubscriptionsFromDb);

        this.saveBatch(recordList);

        Long nowSec = System.currentTimeMillis() / 1000;
        recordList.forEach(record -> {
            lumenChangeRecordService.saveOrUpdateLumenChangeRecord(record.getUserId(), record.getLoginName(), record.getLumenQty(), LumenChangeTypeEnum.ADD.getValue(), LumenChangeSourceEnum.PURCHASE.getValue(), LumenRecordDetailEnum.PURCHASE_LUMENS.getName(), record.getId(), record.getLogicPeriodStart() < nowSec ? nowSec : record.getLogicPeriodStart());
        });
        logGoogle.info("end saveOneTimeLumenRecordForGoogle: {}", recordList);
    }

    private static void addVipGiftLumen(GoogleOneTimePurchases googleOneTimePurchases, SubscriptionCurrent logicValidHighSubscriptionsFromDb, PayLumenRecord saveRecord, GoogleProduct byProductId, List<PayLumenRecord> recordList) {
        try {
            if (logicValidHighSubscriptionsFromDb != null && StrUtil.containsAnyIgnoreCase(logicValidHighSubscriptionsFromDb.getPlanLevel(), "pro", "standard")) {
                log.info("saveOneTimeLumenRecordForGoogle: level: {} loginName: {} productId: {} rule: {}", logicValidHighSubscriptionsFromDb.getPlanLevel(), saveRecord.getLoginName(), googleOneTimePurchases.getProductId(), byProductId.getRule());
                JSONObject rule = JSONUtil.parseObj(byProductId.getRule());
                PayLumenRecord firstChargeRecord = new PayLumenRecord();
                BeanUtils.copyProperties(saveRecord, firstChargeRecord);
                String planLevel = logicValidHighSubscriptionsFromDb.getPlanLevel();
                if ("pro".equalsIgnoreCase(planLevel)) {
                    Integer pro = rule.getInt("pro");
                    if (pro != null && pro > 0) {
                        firstChargeRecord.setLumenQty(pro * googleOneTimePurchases.getCount());
                        firstChargeRecord.setLumenLeftQty(pro * googleOneTimePurchases.getCount());
                        firstChargeRecord.setType(LumenType.recharge.getValue());
                        recordList.add(firstChargeRecord);
                    }
                } else if ("standard".equalsIgnoreCase(planLevel)) {
                    Integer standard = rule.getInt("standard");
                    if (standard != null && standard > 0) {
                        firstChargeRecord.setLumenQty(standard * googleOneTimePurchases.getCount());
                        firstChargeRecord.setLumenLeftQty(standard * googleOneTimePurchases.getCount());
                        firstChargeRecord.setType(LumenType.recharge.getValue());
                        recordList.add(firstChargeRecord);
                    }
                }
            }
        } catch (Exception e) {
            log.error("parse google rule error: {}", e.getMessage(), e);
        }
    }

    private void saveGoogleCaptureUserPayRecord(GoogleOneTimePurchases googleOneTimePurchases, boolean hasPurchasedLumen, GooglePlayOrder googlePlayOrder, GoogleProduct byProductId,
                                                SubscriptionCurrent logicValidHighSubscriptionsFromDb) {

        try {
            // 获取订单ID
            String orderId = googleOneTimePurchases.getOrderId();

            // 检查幂等性
            if (userPayRecordService.checkPayPalIdempotency(null, orderId) != null) {
                log.info("google capture user pay record already exists for orderId: {}, captureId: {}", orderId, googleOneTimePurchases.getId());
                return;
            }

            SavePaymentRecordRequest request = new SavePaymentRecordRequest();
            request.setLoginName(googleOneTimePurchases.getEmail());
            request.setUserId(googleOneTimePurchases.getUserId());
            request.setPlatform(PaymentPlatform.GOOGLE.getCode());
            request.setExternalTransactionId(orderId);
            request.setExternalOrderId(orderId);
            request.setPaymentStatus("completed");
            String total = googlePlayOrder.getTotal();
            String tax = googlePlayOrder.getTax();
            request.setAmountExcludingTax(Long.parseLong(total) /*- Long.parseLong(tax)*/);
            request.setAfterDiscountAmount(Long.parseLong(total));
            request.setAmount(request.getAmountExcludingTax());

//            String customId = captureModel.getCustomId();
//            Integer off = 0;
//            if (customId != null) {
//                JSONObject customData = JSONUtil.parseObj(customId);
//                off = customData.getInt(PayConstant.OFF_META_KEY);
//            }
            request.setPercentOff(0);
            request.setCurrency(googlePlayOrder.getCurrencyCode());
//            BigDecimal percent = (new BigDecimal(100).subtract(new BigDecimal(off))).divide(new BigDecimal(100), 2, RoundingMode.HALF_DOWN);


            // 设置来源和详情（捕获支付通常是一次性支付）
            String source = PaymentSourceEnum.PURCHASE_LUMENS.getName();
            PaymentDetailEnum paymentDetail = PaymentDetailEnum.LUMENS_PURCHASE;

            request.setSource(source);
            // 创建支付记录项
            List<SavePaymentRecordRequest.PaymentRecordItemRequest> items = new ArrayList<>();
            Integer totalRequestLumen = 0;
            Long totalAmount = 0L;

            SavePaymentRecordRequest.PaymentRecordItemRequest item = new SavePaymentRecordRequest.PaymentRecordItemRequest();
            item.setProductType("one");
            item.setQty(googlePlayOrder.getQty());
            item.setPercentOff(0);

            // 设置单位金额
            item.setUnitAmount(Long.valueOf(googlePlayOrder.getListingPrice()));
            // 设置总金额（从PayPal订单项获取实际支付金额）
            item.setTotalAmount(Long.valueOf(googlePlayOrder.getTotal()));
            item.setCurrency(googlePlayOrder.getCurrencyCode());
            // 设置货币
            // 设置Lumen信息
            item.setUnitLumen(byProductId.getLumen());
            item.setGiftLumen(0);
            if (byProductId.getInitialLumen() != null && !hasPurchasedLumen) {
                item.setGiftLumen(byProductId.getInitialLumen());
            }
            if (byProductId.getRule() != null) {
                JSONObject rule = JSONUtil.parseObj(byProductId.getRule());
                String planLevel = logicValidHighSubscriptionsFromDb.getPlanLevel();
                if ("pro".equalsIgnoreCase(planLevel)) {
                    Integer pro = rule.getInt("pro");
                    if (pro != null && pro > 0) {
                        int qty = pro * googleOneTimePurchases.getCount();
                        item.setGiftLumen(item.getGiftLumen() + qty);

                    }
                } else if ("standard".equalsIgnoreCase(planLevel)) {
                    Integer standard = rule.getInt("standard");
                    if (standard != null && standard > 0) {
                        int qty = standard * googleOneTimePurchases.getCount();
                        item.setGiftLumen(item.getGiftLumen() + qty);
                    }
                }
            }

            item.setTotalLumen(item.getUnitLumen() * item.getQty() + item.getGiftLumen());

            // 累计总金额和总Lumen
            totalRequestLumen += item.getTotalLumen();

            items.add(item);

            request.setTotalLumen(totalRequestLumen.longValue());
            request.setDetail(String.format(paymentDetail.getName(), request.getTotalLumen() != null ? request.getTotalLumen() : 0));

            request.setItems(items);

            // 保存支付记录
            userPayRecordService.savePaymentRecord(request);
            log.info("Successfully saved PayPal capture user pay record for userId: {}, captureId: {}", googleOneTimePurchases.getUserId(), googleOneTimePurchases.getId());

        } catch (Exception e) {
            log.error("Failed to save PayPal capture user pay record for captureId: {}", googleOneTimePurchases.getId(), e);
            // 不抛出异常，避免影响主流程
        }
    }

    private List<PayLumenRecord> generatePeriodsGoogle(GoogleSubscriptionHistory googleSubscriptionHistory, GoogleProduct googleProduct) {
        Integer lumen = googleProduct.getLumen();
        String priceInterval = googleProduct.getPriceInterval(); // month/year
        int monthsCount = 1;
        if (priceInterval.equals("year")) {
            monthsCount = 12;
        }

        PayLumenRecord record = new PayLumenRecord();
        record.setCreateTime(LocalDateTime.now());
        record.setPayLogicPurchaseRecordId(googleSubscriptionHistory.getId());

        record.setUserId(googleSubscriptionHistory.getUserId());
        record.setLoginName(googleSubscriptionHistory.getLoginName());
        record.setType(LumenType.vip.getValue());
        record.setLumenQty(lumen);
        record.setLumenLeftQty(lumen);
        record.setVipPlatForm(VipPlatform.GOOGLE.getPlatformName());

        record.setMixed(false);
        record.setInvalid(false);
        record.setCreateTime(LocalDateTime.now());
        List<PayLumenRecord> payLumenRecords = getPayLumenRecords(record, LocalDateTime.now().toEpochSecond(ZoneOffset.UTC), monthsCount);
//        Long expiryTime = googleSubscriptionHistory.getExpiryTime();
//        PayLumenRecord payLumenRecord = payLumenRecords.get(payLumenRecords.size() - 1);
//        // 将时间戳转换为 Instant
//        Instant instant = Instant.ofEpochSecond(expiryTime);
//        // 将 Instant 转换为 ZonedDateTime 并设置为 UTC 时区
//        ZonedDateTime expirationTime =  instant.atZone(ZoneOffset.UTC);
//        payLumenRecord.setLogicPeriodEnd(expiryTime);
//        payLumenRecord.setCurrentPeriodEnd(expiryTime);
        return payLumenRecords;
    }

    public static List<PayLumenRecord> generatePeriodsApple(PayApplePurchaseRecord payLogicPurchaseRecord) {
        long periodStart = payLogicPurchaseRecord.getPurchaseDate();
        Integer monthsCount = payLogicPurchaseRecord.getCount();
        PayLumenRecord record = new PayLumenRecord();
        record.setCreateTime(LocalDateTime.now());
        record.setPayLogicPurchaseRecordId(payLogicPurchaseRecord.getId());

        record.setUserId(payLogicPurchaseRecord.getUserId());
        record.setLoginName(payLogicPurchaseRecord.getLoginName());
        record.setOriginalTransactionId(payLogicPurchaseRecord.getOriginalTransactionId());
        record.setTransactionId(payLogicPurchaseRecord.getTransactionId());
        record.setType(LumenType.vip.getValue());
        record.setLumenQty(payLogicPurchaseRecord.getLumenQty());
        record.setLumenLeftQty(payLogicPurchaseRecord.getLumenQty());
        record.setVipPlatForm(VipPlatform.IOS.getPlatformName());
        record.setOriginalTransactionId(payLogicPurchaseRecord.getOriginalTransactionId());
        record.setTransactionId(payLogicPurchaseRecord.getTransactionId());

        record.setMixed(false);
        record.setInvalid(false);
        record.setCreateTime(LocalDateTime.now());
        return getPayLumenRecords(record, periodStart, monthsCount);
    }

    private static List<PayLumenRecord> generatePeriods(PayLogicPurchaseRecord payLogicPurchaseRecord) {
        long periodStart = payLogicPurchaseRecord.getCurrentPeriodStart();
        Integer monthsCount = payLogicPurchaseRecord.getCount();
        PayLumenRecord record = new PayLumenRecord();
        record.setCreateTime(LocalDateTime.now());
        record.setPayLogicPurchaseRecordId(payLogicPurchaseRecord.getId());

        record.setUserId(payLogicPurchaseRecord.getUserId());
        record.setLoginName(payLogicPurchaseRecord.getLoginName());
        record.setCustomerId(payLogicPurchaseRecord.getCustomerId());
        record.setType(LumenType.vip.getValue());

        record.setLumenQty(payLogicPurchaseRecord.getLumenQty());
        record.setLumenLeftQty(payLogicPurchaseRecord.getLumenQty());
        record.setVipPlatForm(VipPlatform.STRIPE.getPlatformName());
        record.setMixed(false);
        record.setInvalid(false);
        if (payLogicPurchaseRecord.getTrial() == null || !payLogicPurchaseRecord.getTrial()) {
            return getPayLumenRecords(record, periodStart, monthsCount);
        }
        return getPayLumenRecordsTrial(payLogicPurchaseRecord.getTrialDays(), periodStart, record);
    }

    @NotNull
    private static ArrayList<PayLumenRecord> getPayLumenRecordsTrial(Integer trialDays, long periodStart, PayLumenRecord srcLumenRecord) {
        Instant instant = Instant.ofEpochSecond(periodStart);
        // 将 Instant 转换为 ZonedDateTime 并设置为 UTC 时区
        ZonedDateTime startOfMonth = instant.atZone(ZoneOffset.UTC);
        startOfMonth = startOfMonth.toLocalDate().atStartOfDay(startOfMonth.getZone());
        ZonedDateTime endOfMonthTime = startOfMonth.plusDays(trialDays);
        endOfMonthTime = endOfMonthTime.toLocalDate().atTime(23, 59, 59, 0).atZone(endOfMonthTime.getZone());

        PayLumenRecord record = new PayLumenRecord();
        record.setCreateTime(LocalDateTime.now());
        record.setPayLogicPurchaseRecordId(srcLumenRecord.getPayLogicPurchaseRecordId());

        record.setUserId(srcLumenRecord.getUserId());
        record.setLoginName(srcLumenRecord.getLoginName());
        record.setCustomerId(srcLumenRecord.getCustomerId());
        record.setOriginalTransactionId(srcLumenRecord.getOriginalTransactionId());
        record.setTransactionId(srcLumenRecord.getTransactionId());
        record.setType(LumenType.vip.getValue());

        long endSecond = endOfMonthTime.toEpochSecond();
        long periodStartDateTime = startOfMonth.toEpochSecond();

        record.setCurrentPeriodStart(periodStartDateTime);
        record.setCurrentPeriodEnd(endSecond);
        record.setLogicPeriodStart(periodStartDateTime);
        record.setLogicPeriodEnd(endSecond);

        record.setLumenQty(srcLumenRecord.getLumenQty());
        record.setLumenLeftQty(srcLumenRecord.getLumenQty());
        record.setVipPlatForm(srcLumenRecord.getVipPlatForm());
        record.setMixed(false);
        record.setInvalid(false);
        record.setCreateTime(srcLumenRecord.getCreateTime());
        return Lists.newArrayList(record);
    }

    @NotNull
    private static List<PayLumenRecord> getPayLumenRecords(PayLumenRecord srcLumenRecord, long periodStart, Integer monthsCount) {
        List<PayLumenRecord> periods = new ArrayList<>();
        // 将时间戳转换为 Instant
        Instant instant = Instant.ofEpochSecond(periodStart);

        // 将 Instant 转换为 ZonedDateTime 并设置为 UTC 时区
        ZonedDateTime startOfMonth = instant.atZone(ZoneOffset.UTC);
        startOfMonth = startOfMonth.toLocalDate().atStartOfDay(startOfMonth.getZone());
        int dayOfMonth = startOfMonth.getDayOfMonth();

        for (int i = 0; i < monthsCount; i++) {
            // 计算每个周期的开始和结束时间
            ZonedDateTime endOfMonthTime = startOfMonth.plusMonths(1);
            switch (dayOfMonth) {
                case 31:
                    // 如果是 31 天的月份，则结束时间为该月最后一天
                    endOfMonthTime = endOfMonthTime.with(TemporalAdjusters.lastDayOfMonth());
                    break;
                case 30:
                    Month month = endOfMonthTime.getMonth();
                    if (Month.FEBRUARY == month) {
                        endOfMonthTime = endOfMonthTime.with(TemporalAdjusters.lastDayOfMonth());
                    } else {
                        endOfMonthTime = endOfMonthTime.withDayOfMonth(30);
                    }
                    break;
                case 29:
                    if (endOfMonthTime.getMonth() == Month.FEBRUARY) {
                        endOfMonthTime = endOfMonthTime.with(TemporalAdjusters.lastDayOfMonth());
                    }
                    break;
                default:
                    break;
            }
            endOfMonthTime = endOfMonthTime.toLocalDate().atTime(23, 59, 59, 0).atZone(endOfMonthTime.getZone());
            long endSecond = endOfMonthTime.toEpochSecond();
            // 计算开始时间和结束时间的时间戳
            long periodStartDateTime = startOfMonth.toEpochSecond();
            log.info("generatePeriods {}  ", startOfMonth + " ~~ " + endOfMonthTime.toLocalDate().atTime(23, 59, 59, 0).atZone(endOfMonthTime.getZone()));

            startOfMonth = endOfMonthTime;
            PayLumenRecord record = new PayLumenRecord();
            record.setCreateTime(LocalDateTime.now());
            record.setPayLogicPurchaseRecordId(srcLumenRecord.getPayLogicPurchaseRecordId());

            record.setUserId(srcLumenRecord.getUserId());
            record.setLoginName(srcLumenRecord.getLoginName());
            record.setCustomerId(srcLumenRecord.getCustomerId());
            record.setOriginalTransactionId(srcLumenRecord.getOriginalTransactionId());
            record.setTransactionId(srcLumenRecord.getTransactionId());
            record.setType(LumenType.vip.getValue());

            record.setCurrentPeriodStart(periodStartDateTime);
            record.setCurrentPeriodEnd(endSecond);
            record.setLogicPeriodStart(periodStartDateTime);
            record.setLogicPeriodEnd(endSecond);

            record.setLumenQty(srcLumenRecord.getLumenQty());
            record.setLumenLeftQty(srcLumenRecord.getLumenQty());
            record.setVipPlatForm(srcLumenRecord.getVipPlatForm());
            record.setMixed(false);
            record.setInvalid(false);
            record.setCreateTime(srcLumenRecord.getCreateTime());
            periods.add(record);
        }
        return periods;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void giftLumen(User user, Integer lumen) {
        // 赠送lumen
        PayLumenRecord payLumenRecord = new PayLumenRecord();
        payLumenRecord.setVipPlatForm(VipPlatform.GIFT.getPlatformName());
        payLumenRecord.setType(LumenType.gift.getValue());
        payLumenRecord.setLoginName(user.getLoginName());
        payLumenRecord.setLumenQty(lumen);
        payLumenRecord.setLumenLeftQty(lumen);
        ZonedDateTime now = ZonedDateTime.now(ZoneOffset.UTC);
        ZonedDateTime zonedDateTime = now.plusDays(60);
        ZonedDateTime endOfDay = zonedDateTime.toLocalDate().atTime(23, 59, 59, 999999999).atZone(now.getZone());
        payLumenRecord.setCurrentPeriodStart(now.toEpochSecond());
        payLumenRecord.setCurrentPeriodEnd(endOfDay.toEpochSecond());
        payLumenRecord.setLogicPeriodStart(now.toEpochSecond());
        payLumenRecord.setLogicPeriodEnd(endOfDay.toEpochSecond());
        insertPayLumenRecord(payLumenRecord);

        lumenChangeRecordService.saveOrUpdateLumenChangeRecord(user, lumen, LumenChangeTypeEnum.ADD.getValue(), LumenChangeSourceEnum.RESEARCH.getValue(), LumenChangeSourceEnum.RESEARCH.getValue(), null);
    }

    @Override
    public boolean hasPurchasedLumen(Long userId) {
        if (userId == null) {
            return true;
        }
        String hashKey = userId.toString();
        String cacheKey = "user:first:lumen_flag";
        // 先从 Redis 获取
        boolean hasBuy = redisService.hasHashField(cacheKey, hashKey);
        if (!hasBuy) {
            PayLumenRecord one = this.lambdaQuery().eq(PayLumenRecord::getUserId, userId).eq(PayLumenRecord::getType, LumenType.recharge.getValue()).gt(PayLumenRecord::getId, 1928269958249353217L)
                    .last("limit 1").one();
            if (one != null) {
                hasBuy = true;
                redisService.putDataToHash(cacheKey, hashKey, true, 15, TimeUnit.DAYS);
            }
        }
        return hasBuy;
    }

    @Override
    public void invalidatedSubscriptionForPaypal(String srcSubscriptionId, Long payLogicId) {
        log.info("invalidatedSubscriptionForPaypal: {} {}", srcSubscriptionId, payLogicId);
        List<PayLumenRecord> list = this.lambdaQuery().eq(PayLumenRecord::getPayLogicPurchaseRecordId, payLogicId).eq(PayLumenRecord::getInvalid, false).list();
        this.lambdaUpdate().eq(PayLumenRecord::getPayLogicPurchaseRecordId, payLogicId).eq(PayLumenRecord::getInvalid, false).set(PayLumenRecord::getInvalid, true)
                .set(PayLumenRecord::getUpdateTime, LocalDateTime.now()).update();
        subscriptionCurrentService.invalidatedSubscriptionForPaypal(srcSubscriptionId);
        log.info("end invalidatedSubscriptionForPaypal: {} {}", srcSubscriptionId, payLogicId);
        if (!list.isEmpty()) {
            Long now = System.currentTimeMillis() / 1000;
            String detail = null;
            for (PayLumenRecord lumenRecord : list) {
                // 开始时间未来 直接删除
                if (lumenRecord.getLogicPeriodStart() > now) {
                    lumenChangeRecordService.removeLumenChangeRecordByRelationId(lumenRecord.getId());
                } else if (lumenRecord.getLogicPeriodEnd() > now) {
                    // 结束时间已过 直接删除
                    if (lumenRecord.getLumenLeftQty() > 0) {
                        if (detail == null) {
                            detail = buildDetailPrefix(lumenRecord.getPayLogicPurchaseRecordId(), lumenRecord);
                        }
                        lumenChangeRecordService.saveOrUpdateLumenChangeRecord(lumenRecord.getUserId(), lumenRecord.getLoginName(), lumenRecord.getLumenLeftQty(), LumenChangeTypeEnum.DEDUCT.getValue(), LumenChangeSourceEnum.SUBSCRIBE.getValue(), String.format(LumenRecordDetailEnum.SUBSCRIPTION_MONTHLY_LUMENS_RECYCLE.getName(), detail), lumenRecord.getId(), lumenRecord.getLogicPeriodStart() < now ? now : lumenRecord.getLogicPeriodStart());
                    }
                }
            }
        }
    }

    @Override
    public void reActivedSubscriptionForPaypal(String subscriptionId, Long palLogicSubscriptionId) {
        log.info("reActivedSubscriptionForPaypal: {} {}", subscriptionId, palLogicSubscriptionId);
        List<PayLumenRecord> list = this.lambdaQuery().eq(PayLumenRecord::getPayLogicPurchaseRecordId, palLogicSubscriptionId).eq(PayLumenRecord::getInvalid, true).list();
        this.lambdaUpdate().eq(PayLumenRecord::getPayLogicPurchaseRecordId, palLogicSubscriptionId).eq(PayLumenRecord::getInvalid, true).set(PayLumenRecord::getInvalid, false)
                .set(PayLumenRecord::getUpdateTime, LocalDateTime.now()).update();
        subscriptionCurrentService.reActivedSubscriptionForPaypal(subscriptionId);

        if (!list.isEmpty()) {
            Long now = System.currentTimeMillis() / 1000;
            String detail = null;
            for (PayLumenRecord lumenRecord : list) {
                if (detail == null) {
                    detail = buildDetailPrefix(lumenRecord.getPayLogicPurchaseRecordId(), lumenRecord);
                }
                lumenChangeRecordService.saveOrUpdateLumenChangeRecord(lumenRecord.getUserId(), lumenRecord.getLoginName(), lumenRecord.getLumenLeftQty(), LumenChangeTypeEnum.ADD.getValue(), LumenChangeSourceEnum.SUBSCRIBE.getValue(), String.format(LumenRecordDetailEnum.SUBSCRIPTION_MONTHLY_LUMENS.getName(), detail), lumenRecord.getId(), lumenRecord.getLogicPeriodStart() < now ? now : lumenRecord.getLogicPeriodStart());
            }
        }
    }

    public void insertPayLumenRecord(PayLumenRecord payLumenRecord) {
        payLumenRecord.setCreateTime(LocalDateTime.now());
        payLumenRecord.setLumenLeftQty(payLumenRecord.getLumenQty());
        Long currentTimestamp = DateUtils.getTimestamp(LocalDateTime.now());
        // 如果在当前订阅期限内，清空 Redis 缓存
        if (payLumenRecord.getLogicPeriodStart() <= currentTimestamp && currentTimestamp <= payLumenRecord.getLogicPeriodEnd()) {
            log.info("在当前订阅期限内，清空 Redis 缓存");
            VipService vipService = applicationContext.getBean(VipService.class);
            vipService.resettingPersonalLumens(payLumenRecord.getLoginName());
        }
        this.save(payLumenRecord);
    }

}
