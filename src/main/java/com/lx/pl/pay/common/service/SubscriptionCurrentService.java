package com.lx.pl.pay.common.service;

import com.apple.itunes.storekit.model.JWSTransactionDecodedPayload;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lx.pl.db.mysql.gen.entity.StripeProduct;
import com.lx.pl.pay.google.domain.GoogleSubscriptions;
import com.lx.pl.pay.stripe.domain.StripeSubscriptionRecord;
import com.lx.pl.pay.common.domain.SubscriptionCurrent;
import com.lx.pl.pay.stripe.domain.StripeSubscriptionRecord;
import com.stripe.model.Discount;

import java.util.List;

/**
 * SubscriptionCurrent 服务接口
 */
public interface SubscriptionCurrentService extends IService<SubscriptionCurrent> {

    List<SubscriptionCurrent> getLogicValidSubscriptionsFromDb(Long userId);

    String updateAutoRenewStatus(String subscriptionId, Integer autoRenewStatus);

    SubscriptionCurrent getValidHighSubscriptionsFromDb(Long userId, String vipPlatform);

    // clear cache
    void clearUserCache(Long userId);

    List<SubscriptionCurrent> getValidSubscriptionsFromDb(Long userId);

    void saveOrUpdateStripeSubscriptionCurrent(SubscriptionCurrent subscriptionCurrent);

    SubscriptionCurrent getLogicValidHighSubscriptionsFromDb(Long userId);

    Boolean canPay(Long userId, String vipPlatform);


    void recallStripeVipTime(StripeSubscriptionRecord subscription);

    void saveOrUpdateAppleSubscriptionCurrent(SubscriptionCurrent subscriptionCurrent);

    void restoreSubscriptionRecord(String originalTransactionId, Long srcUserId, Long newUserId, String loginName);

    SubscriptionCurrent queryByOriginTransactionId(String originalTransactionId);


    void updateSubscriptionPlan(SubscriptionCurrent subscription, String newProductId, JWSTransactionDecodedPayload transaction);

    void invalidatedSubscriptionForPaypal(String srcSubscriptionId);

    void reActivedSubscriptionForPaypal(String subscriptionId);

    void updateRenewPrice(String total, String subscriptionId);
    void updateCurrentSub(StripeProduct stripeProduct, StripeSubscriptionRecord record, Discount discount);

    boolean hasBoughtVipAndHasExpire(Long userId, int daysBefore);

    Boolean hasRenewSubscription(Long userId);

    void saveOrUpdateGoogleSubscriptionCurrent(SubscriptionCurrent subscriptionCurrent);

    void saveNew(SubscriptionCurrent subscriptionCurrent);

    void updateExpiryTime(GoogleSubscriptions googleSubscriptions);

}
