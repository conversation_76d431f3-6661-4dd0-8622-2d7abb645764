package com.lx.pl.pay.google.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lx.pl.pay.google.domain.GoogleProduct;
import com.lx.pl.pay.google.domain.GoogleSubscriptionHistory;
import com.lx.pl.pay.google.domain.GoogleSubscriptions;

public interface GoogleSubscriptionHistoryService extends IService<GoogleSubscriptionHistory> {
    boolean saveSubscriptionHistoryAndLumen(GoogleSubscriptions googleSubscriptions, GoogleProduct googleProduct, String json, int type);

    boolean saveSubscriptionHistory(GoogleSubscriptions googleSubscriptions, String json, int type);

    GoogleSubscriptionHistory queryByLatestOrderId(String latestOrderId, String purchaseToken);
}
