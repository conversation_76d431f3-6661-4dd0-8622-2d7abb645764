package com.lx.pl.pay.google.service;

import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.enums.LogicErrorCode;
import com.lx.pl.exception.LogicException;
import com.lx.pl.pay.common.dto.GoogleLotteryVo;
import com.lx.pl.pay.common.service.PayLotteryLogService;
import com.lx.pl.pay.common.service.PayLumenRecordService;
import com.lx.pl.pay.google.domain.GoogleActivityConfig;
import com.lx.pl.pay.google.domain.GoogleProductPromotion;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RDeque;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class LotteryServiceGoogle {

    @Autowired
    RedissonClient redisson;
    private final String poolName = "lottery:bag";
    private final String lockName = "lottery:lock";
    private final String statsKey = "lottery:stats";
    private int totalWeight;
    @Value("${pay.lottery.discounts:standard_month:90,pro_month:60,pro_month:20,standard_month:100,lumen:500,none}")
    private String[] prizes;
    @Value("${pay.lottery.weights:5,48,20,2,5,20}")
    private int[] weights;

    public LotteryServiceGoogle() {
    }

    public LotteryServiceGoogle(String[] prizes, int[] weights) {
        this.prizes = prizes;
        this.weights = weights;
        this.totalWeight = Arrays.stream(weights).sum();
    }


    @Autowired
    private GoogleProductPromotionService googleProductPromotionService;
    @Autowired
    PayLumenRecordService payLumenRecordService;
    @Autowired
    PayLotteryLogService payLotteryLogService;
    @Autowired
    private GoogleActivityConfigService googleActivityConfigService;

    List<String> bag = null;

    public String drawLocal() {
        if (totalWeight == 0) {
            this.totalWeight = Arrays.stream(weights).sum();
        }

        int multiRounds = 10;
        if (bag == null) {
            bag = new ArrayList<>(multiRounds * totalWeight);
            for (int r = 0; r < multiRounds; r++) {
                for (int i = 0; i < prizes.length; i++) {
                    for (int j = 0; j < weights[i]; j++) {
                        bag.add(prizes[i]);
                    }
                }
            }
        }

        ThreadLocalRandom current = ThreadLocalRandom.current();
        int i = current.nextInt(bag.size() - 1);
//        bag.add(bag.get(i));
//        // 打乱
//        Collections.shuffle(bag, current);
        return bag.get(i);
    }


    public static void main(String[] args) {
        String[] prizes = {"90", "60", "100", "500_lumen", "none"};
        int[] weights = {5, 68, 2, 5, 20};
        LotteryServiceGoogle lotteryService = new LotteryServiceGoogle(prizes, weights);


        int[] counts = new int[prizes.length];
        for (int i = 0; i < 100000; i++) {
            String s = lotteryService.drawLocal();
            switch (s) {
                case "90":
                    counts[0]++;
                    break;
                case "60":
                    counts[1]++;
                    break;
                case "100":
                    counts[2]++;
                    break;
                case "500_lumen":
                    counts[3]++;
                    break;
                case "none":
                    counts[4]++;
                    break;
            }
        }
        for (int i = 0; i < counts.length; i++) {
            System.out.println("Discount 1000 times for " + prizes[i] + ": " + counts[i] / 1000D * 100 + "%");
        }
    }

    /**
     * 抽奖
     */
    public String draw() {
        RDeque<String> deque = redisson.getDeque(poolName);
        String prize = deque.pollFirst();

        if (prize == null) {
            refill();
            prize = deque.pollFirst();
        }

        if (prize != null) {
            // 更新统计
            redisson.getMap(statsKey).merge(prize, 1, (oldVal, newVal) -> (Integer) oldVal + 1);
        }

        return prize;
    }

    /**
     * 批量 refill（一次性生成 multiRounds * totalWeight 个奖品）
     */
    private void refill() {
        RLock lock = redisson.getLock(lockName);
        if (lock.tryLock()) {
            try {
                RDeque<String> deque = redisson.getDeque(poolName);
                if (deque.isEmpty()) {
                    // 一次生成5轮奖品
                    int multiRounds = 10;
                    List<String> bag = new ArrayList<>(multiRounds * totalWeight);

                    for (int r = 0; r < multiRounds; r++) {
                        for (int i = 0; i < prizes.length; i++) {
                            for (int j = 0; j < weights[i]; j++) {
                                bag.add(prizes[i]);
                            }
                        }
                    }
                    // 打乱
                    Collections.shuffle(bag, ThreadLocalRandom.current());
                    deque.addAll(bag);
                }
            } finally {
                lock.unlock();
            }
        }
    }

    public void printStats() {
        Map<String, Integer> stats = (Map<String, Integer>) (Map) redisson.getMap(statsKey);
        // 打印概率
        int totalCount = 0;
        for (int count : stats.values()) {
            totalCount += count;
        }
        for (Map.Entry<String, Integer> entry : stats.entrySet()) {
            System.out.println("Discount " + entry.getKey() + ": " + entry.getValue() / (double) totalCount * 100 + "%");
        }

        System.out.println("统计结果：" + stats);
    }

    public List<GoogleLotteryVo> getAwardsGoogle(User user, String platform) {
        String draw = this.drawLocal();
        List<GoogleLotteryVo> lotteryVos = new ArrayList<>();
        log.info("user: {} 抽奖结果: {}", user.getLoginName(), draw);
        draw = "standard_month:90";

        switch (draw) {
            case "standard_month:100":
            case "pro_month:60":
            case "pro_month:50":
            case "standard_month:90":
                String[] split = draw.split(":");
                String[] types = split[0].split("_");
                List<GoogleProductPromotion> appleProductPromotions = googleProductPromotionService.queryByOffAndPlanLevel(types[0], types[1], Integer.parseInt(split[1]));
                if (appleProductPromotions != null && !appleProductPromotions.isEmpty()) {
                    for (GoogleProductPromotion appleProductPromotion : appleProductPromotions) {
                        lotteryVos.add(GoogleLotteryVo.builder()
                                .type(draw)
                                .off(appleProductPromotion.getOff())
                                .productId(appleProductPromotion.getCode())
                                .build());
                    }
                } else {
                    log.error("抽奖结果: {} 没有找到对应的产品信息", draw);
                    throw new LogicException(LogicErrorCode.DISCOUNT_RESOURCE_USED_UP);
                }
                break;
            case "lumen:500":
                payLumenRecordService.giftLumen(user, 500);
                lotteryVos.add(GoogleLotteryVo.builder().type("lumen:500").build());
                break;
            case "none":
                lotteryVos.add(GoogleLotteryVo.builder().type("none").build());
                break;
        }
        payLotteryLogService.saveLog(user, draw, platform, "sweep_stakes", lotteryVos);
        return lotteryVos;
    }

    public boolean isLotteryActive() {
        boolean sweepStakes = googleActivityConfigService.lambdaQuery()
                .eq(GoogleActivityConfig::getEnable, true)
                .eq(GoogleActivityConfig::getCode, "sweep_stakes")
                .gt(GoogleActivityConfig::getDeadline, System.currentTimeMillis() / 1000)
                .lt(GoogleActivityConfig::getStartTime, System.currentTimeMillis() / 1000)
                .exists();
        return sweepStakes;
    }


//    public static void main(String[] args) throws InterruptedException {
//        Logger redissonLogger = (Logger) LoggerFactory.getLogger("org.redisson");
//        redissonLogger.setLevel(Level.INFO);
//
//        // 关闭 Netty debug
//        Logger nettyLogger = (Logger) LoggerFactory.getLogger("io.netty");
//        nettyLogger.setLevel(Level.INFO);
//        String[] prizes = {"90", "60", "100", "500_lumen", "none"};
//        int[] weights = {5, 68, 2, 5, 20};
//
//        LotteryService pool = new LotteryService("redis://127.0.0.1:6379", prizes, weights);
//
//        int threadCount = 50;
//        int drawCountPerThread = 30;
//
//        Runnable task = () -> {
//            for (int i = 0; i < drawCountPerThread; i++) {
//                pool.draw();
//            }
//        };
//
//        List<Thread> threads = new ArrayList<>();
//        long start = System.currentTimeMillis();
//        for (int t = 0; t < threadCount; t++) {
//            Thread th = new Thread(task);
//            threads.add(th);
//            th.start();
//        }
//        for (Thread th : threads) {
//            th.join();
//        }
//        long end = System.currentTimeMillis();
//
//        System.out.println("耗时: " + (end - start) + " ms");
//        pool.printStats();
//        pool.shutdown();
//    }
}
