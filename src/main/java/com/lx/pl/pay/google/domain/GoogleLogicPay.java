package com.lx.pl.pay.google.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lx.pl.db.mysql.gen.entity.MyBaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName("google_logic_pay")
@Schema(description = "用户订阅信息表")
public class GoogleLogicPay extends MyBaseEntity {

    @TableId(value = "id")
    @Schema(description = "订阅ID")
    private Long id;

    @TableField(value = "user_id")
    @Schema(description = "关联用户ID")
    private Long userId;

    @TableField(value = "login_name")
    @Schema(description = "关联用户登录名称")
    private String loginName;

    @TableField(value = "product_id")
    @Schema(description = "订阅商品ID（如 monthly_premium）")
    private String productId;

    @TableField(value = "purchase_token")
    @Schema(description = "Google Play 购买 Token")
    private String purchaseToken;

    @TableField(value = "purchase_time")
    @Schema(description = "用户购买时间")
    private Long purchaseTime;

    @TableField(value = "start_time")
    @Schema(description = "订阅生效时间")
    private Long startTime;

    @TableField(value = "expiry_time")
    @Schema(description = "订阅到期时间")
    private Long expiryTime;

    @TableField(value = "lumen")
    @Schema(description = "lumen数量")
    private Integer lumen;

    @TableField(value = "count")
    @Schema(description = "数量")
    private Integer count;

    @TableField(value = "invalid")
    @Schema(description = "是否取消")
    private Integer invalid;
}
