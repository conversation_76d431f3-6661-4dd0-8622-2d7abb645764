package com.lx.pl.pay.google.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lx.pl.db.mysql.gen.entity.MyBaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName("google_one_time_purchases")
@Schema(description = "一次性购买商品记录表")
public class GoogleOneTimePurchases extends MyBaseEntity {

    @TableId(value = "id")
    @Schema(description = "购买记录ID")
    private Long id;

    @TableField(value = "user_id")
    @Schema(description = "关联用户ID")
    private Long userId;

    private String email;

    @TableField(value = "product_id")
    @Schema(description = "商品ID（如 premium_unlock）")
    private String productId;

    @TableField(value = "count")
    @Schema(description = "购买数量")
    private Integer count;

    @TableField(value = "purchase_token")
    @Schema(description = "Google Play 购买 Token（唯一）")
    private String purchaseToken;

    @TableField(value = "purchase_time")
    @Schema(description = "购买时间（毫秒级时间戳转换）")
    private Long purchaseTime;

    @TableField(value = "purchase_state")
    @Schema(description = "支付状态（0. 已购买 1. 已取消 2. 待处理）")
    private Integer purchaseState;

    @TableField(value = "kind")
    @Schema(description = "类型")
    private String kind;

    @TableField(value = "developer_payload")
    @Schema(description = "开发者负载")
    private String developerPayload;

    @TableField(value = "order_id")
    @Schema(description = "订单ID")
    private String orderId;

    @TableField(value = "purchase_type")
    @Schema(description = "购买类型")
    private Integer purchaseType;

    @TableField(value = "acknowledgement_state")
    @Schema(description = "确认状态")
    private Integer acknowledgementState;

    @TableField(value = "consumption_state")
    @Schema(description = "消费状态")
    private Integer consumptionState;

    @TableField(value = "obfuscated_external_account_id")
    @Schema(description = "混淆的外部账户ID")
    private String obfuscatedExternalAccountId;

    @TableField(value = "obfuscated_external_profile_id")
    @Schema(description = "混淆的外部配置文件ID")
    private String obfuscatedExternalProfileId;

    @TableField(value = "region_code")
    @Schema(description = "地区代码")
    private String regionCode;

    @TableField(value = "refundable_quantity")
    @Schema(description = "可退款数量")
    private Integer refundableQuantity;

    private Integer lumenQty;
}
