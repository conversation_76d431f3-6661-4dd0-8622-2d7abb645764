package com.lx.pl.pay.google.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lx.pl.pay.google.domain.GoogleOneTimePurchases;
import com.lx.pl.pay.google.mapper.GoogleOneTimePurchasesMapper;
import com.lx.pl.pay.google.service.GoogleOneTimePurchasesService;
import org.springframework.stereotype.Service;

@Service
public class GoogleOneTimePurchasesServiceImpl extends ServiceImpl<GoogleOneTimePurchasesMapper, GoogleOneTimePurchases> implements GoogleOneTimePurchasesService {
    @Override
    public GoogleOneTimePurchases queryByPurchaseToken(String purchaseToken) {
        return this.lambdaQuery().eq(GoogleOneTimePurchases::getPurchaseToken, purchaseToken).one();
    }
}
