package com.lx.pl.pay.google.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lx.pl.db.mysql.gen.entity.MyBaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@TableName("google_play_order")
@Schema(description = "Google支付订单表")
public class GooglePlayOrder extends MyBaseEntity {

    @TableId(value = "id")
    @Schema(description = "主键ID")
    private Long id;

    private Long userId;

    private String loginName;

    @TableField(value = "order_id")
    @Schema(description = "订单ID")
    private String orderId;

    @TableField(value = "purchase_token")
    @Schema(description = "购买凭证")
    private String purchaseToken;

    @TableField(value = "state")
    @Schema(description = "状态")
    private String state;

    @TableField(value = "total")
    @Schema(description = "总金额")
    private String total;

    @TableField(value = "currency_code")
    @Schema(description = "货币代码")
    private String currencyCode;

    @TableField(value = "tax")
    @Schema(description = "税费")
    private String tax;

    private String listingPrice;

    @TableField(value = "product_id")
    @Schema(description = "产品ID")
    private String productId;

    @TableField(value = "qty")
    @Schema(description = "数量")
    private Integer qty;

    @TableField(value = "origin_json")
    @Schema(description = "原始JSON数据")
    private String originJson;
}
