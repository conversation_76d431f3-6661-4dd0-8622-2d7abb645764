package com.lx.pl.pay.google.enums;

public enum ConsumptionState {
    /**
     * 应用内商品的消耗状态。可能的值为：0. 尚未消耗 1. 已使用
     */

    NOT_CONSUMED(0, "尚未消耗"),
    CONSUMED(1, "已使用");

    private int type;
    private String desc;

    ConsumptionState(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public int getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static ConsumptionState fromInt(int type) {
        for (ConsumptionState consumptionState : ConsumptionState.values()) {
            if (consumptionState.type == type) {
                return consumptionState;
            }
        }
        return null;
    }
}
