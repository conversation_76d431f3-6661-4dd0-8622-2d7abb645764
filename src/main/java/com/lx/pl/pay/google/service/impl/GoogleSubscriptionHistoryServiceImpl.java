package com.lx.pl.pay.google.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lx.pl.pay.apple.VipPlatform;
import com.lx.pl.pay.common.dto.SavePaymentRecordRequest;
import com.lx.pl.pay.common.enums.PaymentDetailEnum;
import com.lx.pl.pay.common.enums.PaymentPlatform;
import com.lx.pl.pay.common.enums.PaymentSourceEnum;
import com.lx.pl.pay.common.service.EventReportService;
import com.lx.pl.pay.common.service.PayLumenRecordService;
import com.lx.pl.pay.common.service.UserPayRecordService;
import com.lx.pl.pay.google.domain.GooglePlayOrder;
import com.lx.pl.pay.google.domain.GoogleProduct;
import com.lx.pl.pay.google.domain.GoogleSubscriptionHistory;
import com.lx.pl.pay.google.domain.GoogleSubscriptions;
import com.lx.pl.pay.google.mapper.GoogleSubscriptionHistoryMapper;
import com.lx.pl.pay.google.service.GoogleProductService;
import com.lx.pl.pay.google.service.GoogleSubscriptionHistoryService;
import com.lx.pl.pay.google.service.GoogleSubscriptionsService;
import com.lx.pl.pay.google.service.IGooglePlayOrderService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;

@Service
public class GoogleSubscriptionHistoryServiceImpl extends ServiceImpl<GoogleSubscriptionHistoryMapper, GoogleSubscriptionHistory> implements GoogleSubscriptionHistoryService {

    Logger log = LoggerFactory.getLogger("google-pay-msg");

    @Autowired
    private GoogleSubscriptionsService googleSubscriptionsService;
    @Resource
    private PayLumenRecordService payLumenRecordService;
    @Resource
    private GoogleProductService googleProductService;

    @Resource
    private EventReportService eventReportService;
    @Resource
    private IGooglePlayOrderService googlePlayOrderService   ;
    @Resource
    private UserPayRecordService userPayRecordService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveSubscriptionHistoryAndLumen(GoogleSubscriptions googleSubscriptions, GoogleProduct googleProduct, String json, int type) {
        GoogleSubscriptionHistory one1 = this.lambdaQuery().eq(GoogleSubscriptionHistory::getUserId, googleSubscriptions.getUserId())
                .eq(GoogleSubscriptionHistory::getLatestOrderId, googleSubscriptions.getLatestOrderId())
                .eq(GoogleSubscriptionHistory::getSubscriptionState, googleSubscriptions.getSubscriptionState())
                .one();
        if (one1 != null) {
            log.info("history 存在了，更新到期时间 {}", googleSubscriptions.getLatestOrderId());
            this.lambdaUpdate().eq(GoogleSubscriptionHistory::getId, one1.getId())
                    .set(GoogleSubscriptionHistory::getProductId, googleSubscriptions.getProductId())
                    .set(GoogleSubscriptionHistory::getSubscriptionState, googleSubscriptions.getSubscriptionState())
                    .set(GoogleSubscriptionHistory::getExpiryTime, googleSubscriptions.getExpiryTime())
                    .update();
            payLumenRecordService.updateExistRecordExpiretime(googleSubscriptions.getLatestOrderId(), googleSubscriptions.getExpiryTime());
            return true;
        } else {
            GoogleSubscriptionHistory googleSubscriptionHistory = new GoogleSubscriptionHistory();
            BeanUtils.copyProperties(googleSubscriptions, googleSubscriptionHistory);
            GoogleSubscriptions one = googleSubscriptionsService.queryByLatestSubscriptionByUserId(googleSubscriptions.getUserId());
            if (one != null) {
                googleSubscriptionHistory.setOldProductId(one.getProductId());
                googleSubscriptionHistory.setOldPlanId(one.getPlanId());
            }
            googleSubscriptionHistory.setNotificationType(type);
            googleSubscriptionHistory.setDetails(json);
            try {
               JSONObject jsonObject = JSONUtil.parseObj(json);
                if (Objects.nonNull(jsonObject)
                        && Objects.nonNull(jsonObject.getJSONArray("lineItems"))
                        && !jsonObject.getJSONArray("lineItems").isEmpty()) {

                   JSONObject offerDetails = jsonObject.getJSONArray("lineItems").getJSONObject(0).getJSONObject("offerDetails");
                    if (Objects.nonNull(offerDetails) && Objects.nonNull(offerDetails.getStr("offerId"))) {
                        // 存在 offerId 则为折扣商品
                        googleSubscriptionHistory.setOfferId(offerDetails.getStr("offerId"));
                    }
                }
            } catch (Exception e) {
                log.error("parse google json 4 offerId error {}", e.getMessage());
            }
            googleSubscriptionHistory.setCreateTime(LocalDateTime.now());
            this.save(googleSubscriptionHistory);
            GooglePlayOrder googlePlayOrder = googlePlayOrderService.saveGooglePayOrder(googleSubscriptionHistory.getUserId(),
                    googleSubscriptionHistory.getLoginName(), googleSubscriptionHistory.getLatestOrderId());
//            boolean hasPurchasedLumen = payLumenRecordService.hasPurchasedLumen(googleSubscriptionHistory.getUserId());
            saveGoogleCaptureUserPayRecord(googlePlayOrder, googleProduct);

            // 数据上报
            purchaseEventReport(googleSubscriptionHistory, googleSubscriptions);
            log.info("saveSubscriptionHistoryAndLumen {} {}", googleSubscriptionHistory.getId(), googleSubscriptions.getLatestOrderId());
            payLumenRecordService.saveLumenRecordForGoogle(googleProduct, googleSubscriptionHistory);
            return true;
        }
    }
    /**
     * 参照 Apple 保存支付记录逻辑，保存 Google 订阅/一次性支付的用户支付记录
     */
    private void saveGoogleCaptureUserPayRecord(GooglePlayOrder googlePlayOrder,
                                                GoogleProduct googleProduct) {
        try {
            if (googlePlayOrder == null) {
                return;
            }
            // 幂等校验（purchaseToken + orderId）
            if (userPayRecordService.checkGoogleIdempotency(googlePlayOrder.getPurchaseToken(), googlePlayOrder.getOrderId()) != null) {
                log.info("Google user pay record already exists for purchaseToken: {}, orderId: {}",
                        googlePlayOrder.getPurchaseToken(), googlePlayOrder.getOrderId());
                return;
            }

            SavePaymentRecordRequest request = new SavePaymentRecordRequest();
            request.setLoginName(googlePlayOrder.getLoginName());
            request.setUserId(googlePlayOrder.getUserId());
            request.setPlatform(PaymentPlatform.GOOGLE.getCode());
            request.setExternalTransactionId(googlePlayOrder.getPurchaseToken());
            request.setExternalOrderId(googlePlayOrder.getOrderId());
            request.setPaymentStatus("completed");

            // 金额与币种（Google Play接口返回单位为最小货币单位的字符串，已在GooglePlayOrderServiceImpl中处理为字符串数值）
            if (googlePlayOrder.getTotal() != null) {
                long total = Long.parseLong(googlePlayOrder.getTotal());
                request.setAmount(total);
                request.setAfterDiscountAmount(total);
                // 如需含税/不含税拆分，可根据 tax 字段调整
                request.setAmountExcludingTax(total /*- Long.parseLong(Optional.ofNullable(googlePlayOrder.getTax()).orElse("0"))*/);
            }
            request.setCurrency(googlePlayOrder.getCurrencyCode());

            // 组装明细
            SavePaymentRecordRequest.PaymentRecordItemRequest item = new SavePaymentRecordRequest.PaymentRecordItemRequest();
            String productType = googleProduct != null && "plan".equalsIgnoreCase(googleProduct.getProductType()) ? "plan" : "one";
            item.setProductType(productType);
            if (googleProduct != null) {
                item.setPlanLevel(googleProduct.getPlanLevel());
                item.setPriceInterval(googleProduct.getPriceInterval());
                item.setUnitLumen(googleProduct.getLumen());
                item.setGiftLumen(0);
            } else {
                item.setUnitLumen(0);
                item.setGiftLumen(0);
            }
            item.setQty(googlePlayOrder.getQty() != null ? googlePlayOrder.getQty() : 1);
            item.setUnitAmount(googlePlayOrder.getListingPrice() != null ? Long.parseLong(googlePlayOrder.getListingPrice()) : request.getAmount());
            item.setTotalAmount(request.getAfterDiscountAmount());
            item.setCurrency(googlePlayOrder.getCurrencyCode());

            request.setItems(java.util.Collections.singletonList(item));
            if (item.getUnitLumen() != null) {
                long totalLumen = (long) item.getUnitLumen() * item.getQty() + item.getGiftLumen();
                request.setTotalLumen(totalLumen);
            }

            // 来源与详情
            String source;
            String detail;
            if ("plan".equals(item.getProductType())) {
                source = PaymentSourceEnum.SUBSCRIBE.getName();
                PaymentDetailEnum pd = PaymentDetailEnum.getByPlanLevelAndPriceInterval(item.getPlanLevel(), item.getPriceInterval());
                detail = pd != null ? pd.getName() : "Google Subscription";
            } else {
                source = PaymentSourceEnum.PURCHASE_LUMENS.getName();
                detail = String.format(PaymentDetailEnum.LUMENS_PURCHASE.getName(), request.getTotalLumen() != null ? request.getTotalLumen() : 0);
            }
            request.setSource(source);
            request.setDetail(detail);

            userPayRecordService.savePaymentRecord(request);
            log.info("Successfully saved Google user pay record for userId: {}, orderId: {}", googlePlayOrder.getUserId(), googlePlayOrder.getOrderId());
        } catch (Exception e) {
            log.error("Failed to save Google user pay record for orderId: {}", googlePlayOrder != null ? googlePlayOrder.getOrderId() : null, e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveSubscriptionHistory(GoogleSubscriptions googleSubscriptions, String json, int type) {
        GoogleSubscriptionHistory one1 = this.lambdaQuery()
                .eq(GoogleSubscriptionHistory::getUserId, googleSubscriptions.getUserId())
                .eq(GoogleSubscriptionHistory::getLatestOrderId, googleSubscriptions.getLatestOrderId())
                .eq(GoogleSubscriptionHistory::getSubscriptionState, googleSubscriptions.getSubscriptionState())
                .one();
        if (one1 != null) {
            log.info("saveSubscriptionHistory {} {}", one1.getId(), googleSubscriptions.getLatestOrderId());
            return false;
        }

        GoogleSubscriptionHistory googleSubscriptionHistory = new GoogleSubscriptionHistory();
        BeanUtils.copyProperties(googleSubscriptions, googleSubscriptionHistory);
        GoogleSubscriptions one = googleSubscriptionsService.queryByLatestSubscriptionByUserId(googleSubscriptions.getUserId());
        if (one != null) {
            googleSubscriptionHistory.setOldProductId(one.getProductId());
            googleSubscriptionHistory.setOldPlanId(one.getPlanId());
        }
        googleSubscriptionHistory.setNotificationType(type);
        googleSubscriptionHistory.setDetails(json);
        try {
            JSONObject jsonObject = JSONUtil.parseObj(json);
            if (Objects.nonNull(jsonObject)
                    && Objects.nonNull(jsonObject.getJSONArray("lineItems"))
                    && !jsonObject.getJSONArray("lineItems").isEmpty()) {

                JSONObject offerDetails = jsonObject.getJSONArray("lineItems").getJSONObject(0).getJSONObject("offerDetails");
                if (Objects.nonNull(offerDetails) && Objects.nonNull(offerDetails.getStr("offerId"))) {
                    // 存在 offerId 则为折扣商品
                    googleSubscriptionHistory.setOfferId(offerDetails.getStr("offerId"));
                }
            }
        } catch (Exception e) {
            log.error("parse google json 4 offerId error {}", e.getMessage());
        }
        googleSubscriptionHistory.setCreateTime(LocalDateTime.now());
        this.save(googleSubscriptionHistory);
        log.info("saveSubscriptionHistory {} {}", googleSubscriptionHistory.getId(), googleSubscriptionHistory.getLatestOrderId());
        return true;
    }

    @Override
    public GoogleSubscriptionHistory queryByLatestOrderId(String latestOrderId, String purchaseToken) {
        GoogleSubscriptionHistory one = this.lambdaQuery().eq(GoogleSubscriptionHistory::getLatestOrderId, latestOrderId)
//                .eq(GoogleSubscriptionHistory::getPurchaseToken, purchaseToken)
                .orderByDesc(GoogleSubscriptionHistory::getCreateTime).last("limit 1").one();
        return one;
    }

    /**
     * @param googleSubscriptionHistory googleSubscriptionHistory
     * @param googleSubscriptions       googleSubscriptions
     */
    public void purchaseEventReport(GoogleSubscriptionHistory googleSubscriptionHistory, GoogleSubscriptions googleSubscriptions) {

        // 只有 notificationType 为 2 和 4 才是订阅激活信息
        if (googleSubscriptionHistory.getNotificationType() != 2 && googleSubscriptionHistory.getNotificationType() != 4) {
            return;
        }

        // 如果 notificationType 为4 则代表首次购买， 如果是首次购买且 offer_id 不为空 且 offer_id in (aiease-year-sale, aiease-month-sale) 则为折扣商品
        if (Objects.equals(googleSubscriptionHistory.getNotificationType(), 4)) {
            if (StrUtil.isNotEmpty(googleSubscriptionHistory.getOfferId())) {
                // 安卓产品购买逻辑 不能轻易修改  否则此处需要调整。 目前没有更好的办法判断 试用 （试用不扣钱 不计入收入）
                if (Objects.equals(googleSubscriptionHistory.getOfferId(), "aiease-year-sale")
                        || Objects.equals(googleSubscriptionHistory.getOfferId(), "aiease-month-sale")) {
                    return;
                }
            }
        }

        eventReportService.purchaseEventReport(googleSubscriptionHistory.getUserId(), VipPlatform.GOOGLE, googleSubscriptionHistory.getLatestOrderId(),
                Double.valueOf(googleSubscriptions.getRecurringPrice()), googleSubscriptions.getCurrency(), String.valueOf(googleSubscriptionHistory.getNotificationType()),"googlePay", System.currentTimeMillis());
    }


}
