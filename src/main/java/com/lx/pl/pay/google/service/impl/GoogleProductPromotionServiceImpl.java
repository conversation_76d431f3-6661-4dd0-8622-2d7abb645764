package com.lx.pl.pay.google.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lx.pl.pay.google.domain.GoogleProduct;
import com.lx.pl.pay.google.domain.GoogleProductPromotion;
import com.lx.pl.pay.google.mapper.GoogleProductPromotionMapper;
import com.lx.pl.pay.google.service.GoogleProductPromotionService;
import com.lx.pl.pay.google.service.GoogleProductService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class GoogleProductPromotionServiceImpl extends ServiceImpl<GoogleProductPromotionMapper, GoogleProductPromotion> implements GoogleProductPromotionService {


    @Resource
    private GoogleProductService googleProductService;

    @Override
    public List<GoogleProductPromotion> queryByOffAndPlanLevel(String planLevel, String priceInterval, int off) {
        GoogleProduct one = googleProductService.lambdaQuery()
                .eq(GoogleProduct::getPlanLevel, planLevel)
                .eq(GoogleProduct::getPriceInterval, priceInterval)
                .eq(GoogleProduct::getStatus, true)
                .one();
        if (one != null) {
            return this.lambdaQuery().
                    eq(GoogleProductPromotion::getOff, off)
                    .eq(GoogleProductPromotion::getRelationId, one.getId())
                    .eq(GoogleProductPromotion::getEnable, true)
                    .gt(GoogleProductPromotion::getDeadline, System.currentTimeMillis() / 1000)
                    .list();

        }
        return List.of();
    }
}
