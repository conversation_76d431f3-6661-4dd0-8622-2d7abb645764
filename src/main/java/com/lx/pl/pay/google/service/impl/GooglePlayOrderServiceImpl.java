package com.lx.pl.pay.google.service.impl;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lx.pl.pay.google.config.GooglePayConfiguration;
import com.lx.pl.pay.google.domain.GooglePlayOrder;
import com.lx.pl.pay.google.mapper.GooglePlayOrderMapper;
import com.lx.pl.pay.google.service.GooglePlayOrderClient;
import com.lx.pl.pay.google.service.IGooglePlayOrderService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class GooglePlayOrderServiceImpl extends ServiceImpl<GooglePlayOrderMapper, GooglePlayOrder> implements IGooglePlayOrderService {

    @Resource
    GooglePlayOrderClient googlePlayOrderClient;

    @Resource
    private GooglePayConfiguration googlePayConfiguration;

    @Override
    public GooglePlayOrder saveGooglePayOrder(Long userId, String email, String orderId) {

        String packageName = googlePayConfiguration.getPackageName();
        JSONObject orderDetails = googlePlayOrderClient.getOrderDetails(packageName, orderId);
        if (orderDetails != null) {
            JSONObject lineItems = orderDetails.getJSONArray("lineItems").getJSONObject(0);
//            for (Object lineItem : lineItems) {
//                JSONObject lineItemObj = (JSONObject) lineItem;
//            }
            GooglePlayOrder googlePlayOrder = new GooglePlayOrder();
            googlePlayOrder.setUserId(userId);
            googlePlayOrder.setLoginName(email);
            googlePlayOrder.setOrderId(orderDetails.getStr("orderId"));
            googlePlayOrder.setProductId(lineItems.getStr("productId"));
            googlePlayOrder.setPurchaseToken(orderDetails.getStr("purchaseToken"));
            googlePlayOrder.setState(orderDetails.getStr("state"));
            JSONObject total = lineItems.getJSONObject("total");
            if (total != null) {
                googlePlayOrder.setCurrencyCode(total.getStr("currencyCode"));
                String units = total.getStr("units", "0");
                long nanos = total.getLong("nanos", 0L) / 1000 / 1000;
                googlePlayOrder.setTotal(units +  (nanos == 0 ? "000" : nanos));
            }
            JSONObject tax = lineItems.getJSONObject("tax");
            if (tax != null) {
                String units = tax.getStr("units", "0");
                long nanos = tax.getLong("nanos", 0L) / 1000 / 1000;
                googlePlayOrder.setTax(units + (nanos == 0 ? "000" : nanos));
            }
            JSONObject listingPrice = lineItems.getJSONObject("listingPrice");
            if (listingPrice != null) {
                String units = listingPrice.getStr("units", "0");
                long nanos = listingPrice.getLong("nanos", 0L) / 1000 / 1000;
                googlePlayOrder.setListingPrice(units + (nanos == 0 ? "000" : nanos));
            }

            googlePlayOrder.setQty(1);
            googlePlayOrder.setOriginJson(orderDetails.toString());
            save(googlePlayOrder);
            return googlePlayOrder;
        }
        return null;
    }
}
