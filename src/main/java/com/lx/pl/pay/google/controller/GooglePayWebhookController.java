package com.lx.pl.pay.google.controller;


import com.google.api.client.googleapis.auth.oauth2.GoogleIdToken;
import com.google.api.client.googleapis.auth.oauth2.GoogleIdTokenVerifier;
import com.google.auth.oauth2.GoogleCredentials;
import com.google.auth.oauth2.ServiceAccountCredentials;
import com.google.gson.JsonElement;
import com.google.gson.JsonParser;
import com.lx.pl.pay.google.config.GooglePayConfiguration;
import com.lx.pl.pay.google.exception.PayGoogleException;
import com.lx.pl.pay.google.service.IGooglePayService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.stream.Collectors;

import static com.lx.pl.pay.google.constant.GoogleConstants.GOOGLE_PAY_NOTIFICATION_LOCK;
import static com.lx.pl.pay.google.exception.GoogleErrorCode.SUB_EXTERNAL_ACCOUNT_IS_NULL;
import static com.lx.pl.pay.google.exception.GoogleErrorCode.TEST_NOT_ALLOWED_PRO;

@RestController
@RequestMapping("api/google")
@Tag(name = "google回调")
public class GooglePayWebhookController {
    Logger log = LoggerFactory.getLogger("google-pay-msg");
    @Autowired
    private IGooglePayService googlePayService;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private GoogleIdTokenVerifier verifier;
    @Autowired
    private GooglePayConfiguration googlePayConfiguration;
    @Autowired
    private GoogleCredentials googleCredentials;

    @PostMapping(value = "/pub-sub-push")
    @Operation(summary = "google pubsub push")
    public void pubSubPush(HttpServletRequest req, HttpServletResponse resp) {
        log.info("pubsub push message start");
        // Get the Cloud Pub/Sub-generated JWT in the "Authorization" header.
        String authorizationHeader = req.getHeader("Authorization");
        if (authorizationHeader == null
                || authorizationHeader.isEmpty()
                || authorizationHeader.split(" ").length != 2) {
            resp.setStatus(HttpServletResponse.SC_BAD_REQUEST);
            return;
        }
        String authorization = authorizationHeader.split(" ")[1];

        try {
            // Verify and decode the JWT.
            // Note: For high volume push requests, it would save some network overhead
            // if you verify the tokens offline by decoding them using Google's Public
            // Cert; caching already seen tokens works best when a large volume of
            // messsages have prompted a single push server to handle them, in which
            // case they would all share the same token for a limited time window.
            GoogleIdToken idToken = verifier.verify(authorization);

            GoogleIdToken.Payload payload = idToken.getPayload();
            // IMPORTANT: you should validate claim details not covered by signature
            // and audience verification above, including:
            //   - Ensure that `payload.getEmail()` is equal to the expected service
            //     account set up in the push subscription settings.
            //   - Ensure that `payload.getEmailVerified()` is set to true.
            if (!payload.getEmailVerified()) {
                resp.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                return;
            }

            if (!((ServiceAccountCredentials) googleCredentials).getClientEmail().equals(payload.getEmail())) {
                resp.setStatus(HttpServletResponse.SC_BAD_REQUEST);
            }

            String requestBody = req.getReader().lines().collect(Collectors.joining("\n"));

            JsonElement jsonRoot = JsonParser.parseString(requestBody).getAsJsonObject();
            log.info("jsonRoot: {}", jsonRoot);
            JsonElement message = jsonRoot.getAsJsonObject().get("message");
            JsonElement subscription = jsonRoot.getAsJsonObject().get("subscription");
            if (subscription == null) {
                log.info("subscription is null");
                resp.setStatus(102);
                return;
            }
            if (!googlePayConfiguration.getSubscriptionName().equals(subscription.getAsString())) {
                log.info("subscription is not match");
                resp.setStatus(102);
                return;
            }

            if (message == null) {
                log.info("message is null");
                resp.setStatus(102);
                return;
            }
            String data = message.getAsJsonObject().get("data").getAsString();
            if (data == null) {
                resp.setStatus(102);
                log.info("data is null");
                return;
            }
            String messageId = message.getAsJsonObject().get("messageId").getAsString();
            RLock lock = redissonClient.getLock(GOOGLE_PAY_NOTIFICATION_LOCK + messageId);
            try {
                lock.lock();
                googlePayService.handleRTDNEvent(new String(Base64.getDecoder().decode(data), StandardCharsets.UTF_8), messageId);
            } finally {
                if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
            // 200, 201, 204, 102 status codes are interpreted as success by the Pub/Sub system
            resp.setStatus(200);
        } catch (PayGoogleException e) {
            String code = e.getCode();
            if (TEST_NOT_ALLOWED_PRO.getCode().equals(code)) {
                log.info("test not allowed to prod OR prod not allowed test");
                resp.setStatus(200);
            } else if (SUB_EXTERNAL_ACCOUNT_IS_NULL.getCode().equals(code)) {
                log.info("external account is null or cant find user id");
                resp.setStatus(200);
            } else {
                log.error("receiver pubsub msg error", e);
                resp.setStatus(500);
            }
        } catch (Exception e) {
            log.error("receiver pubsub msg error {}", e.getMessage(), e);
            resp.setStatus(HttpServletResponse.SC_BAD_REQUEST);
        }
    }
}
