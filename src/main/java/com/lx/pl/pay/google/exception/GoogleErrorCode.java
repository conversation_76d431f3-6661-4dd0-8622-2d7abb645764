package com.lx.pl.pay.google.exception;

/**
 * @Description: stripe支付处理枚举，描述不返回前端，用于细分错误码，message 描述前端显示错误
 * @Author: senlin_he
 * @Date: 2024/12/27
 */
public enum GoogleErrorCode {
    VALIDATE_RECEIPT_ERROR("900", " ", "验证receipt 出错"),
    PRODUCT_NOT_EXIST("901", "not find product ", "未查询到产品信息"),
    NOT_FOUND_PRODUCT_INFO("902", "not find product info", "未查询到产品信息"),

    NOT_FOUND_SUBSCRIPTION_INFO("903", "not find subscription info", "未查询到订阅信息"),
    TEST_NOT_ALLOWED_PRO("904", "test not allowed", "生成环境不允许购买测试产品"),

    SUB_EXPIRED("905", "subscription expired", "订阅已过期"),
    SUB_EXTERNAL_ACCOUNT_IS_NULL("906", "external account is null or cant find user id", "external account is null or cant find user id"),
    VALIDATE_RECEIPT_ERROR_ONE("907", " ", "验证one time receipt 出错"),
    ;

    private final String code;
    private final String message;
    private final String description;

    GoogleErrorCode(String code, String message, String description) {
        this.code = code;
        this.message = message;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public String getDescription() {
        return description;
    }
}
