package com.lx.pl.pay.google.service;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.google.api.client.http.GenericUrl;
import com.google.api.client.http.HttpRequest;
import com.google.api.client.http.HttpRequestFactory;
import com.google.api.client.http.HttpResponse;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.auth.oauth2.AccessToken;
import com.google.auth.oauth2.GoogleCredentials;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Collections;

@Component
@Slf4j
public class GooglePlayOrderClient {

    private static final String ORDERS_GET_URL_TEMPLATE =
            "https://androidpublisher.googleapis.com/androidpublisher/v3/applications/%s/orders/%s";
    @Value(value = "classpath:google/piclumen-454007-f13f7b8c1a76.json")
    private Resource resource;

    public JSONObject getOrderDetails(String packageName, String orderId) {

        try {
            String url = String.format(ORDERS_GET_URL_TEMPLATE, packageName, orderId);
            HttpRequestFactory requestFactory = new NetHttpTransport()
                    .createRequestFactory(request -> request.getHeaders()
                            .setAccept("application/json")
                            .setAuthorization("Bearer " + getAccessToken()));

            HttpRequest request = requestFactory.buildGetRequest(new GenericUrl(url));
            HttpResponse response = request.execute();
//            String json = new BufferedReader(new InputStreamReader(response.getContent()))
//                    .lines().collect(Collectors.joining("\n"));
//            System.out.println(json); // 检查是否为 {}、null 还是空串
            String json = response.parseAsString();
            return JSONUtil.parseObj(json);
        } catch (IOException e) {
           log.error("getOrderDetails error: ", e);
            return null;
        }
    }
//    private static final String SERVICE_ACCOUNT_JSON = "D:\\easeus\\core\\src\\main\\resources\\google\\piclumen-454007-f13f7b8c1a76.json";

    private String getAccessToken() {
        try {
//            GoogleCredentials credential = GoogleCredentials.fromStream(new FileInputStream(new File(SERVICE_ACCOUNT_JSON)))
            GoogleCredentials credential = GoogleCredentials.fromStream(resource.getInputStream())
                    .createScoped(Collections.singleton("https://www.googleapis.com/auth/androidpublisher"));
            AccessToken accessToken = credential.refreshAccessToken();
            return accessToken.getTokenValue();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

//    public static void main(String[] args) {
//        GooglePlayOrderService googlePlayOrderService = new GooglePlayOrderService();
//        JSONObject orderDetails = googlePlayOrderService.getOrderDetails("nullart.ai",
//                "GPA.3364-7495-8928-58854");
//        System.out.println(orderDetails);
//    }

}
