package com.lx.pl.pay.google.listener;

import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.json.gson.GsonFactory;
import com.google.api.gax.core.FixedCredentialsProvider;
import com.google.api.services.androidpublisher.AndroidPublisher;
import com.google.api.services.androidpublisher.AndroidPublisherScopes;
import com.google.api.services.androidpublisher.model.ProductPurchase;
import com.google.auth.http.HttpCredentialsAdapter;
import com.google.auth.oauth2.GoogleCredentials;
import com.google.cloud.pubsub.v1.AckReplyConsumer;
import com.google.cloud.pubsub.v1.MessageReceiver;
import com.google.cloud.pubsub.v1.Subscriber;
import com.google.common.collect.Sets;
import com.google.pubsub.v1.ProjectSubscriptionName;
import org.json.JSONObject;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.security.GeneralSecurityException;
import java.util.HashSet;

import static com.google.api.services.pubsub.PubsubScopes.all;

public class GooglePlaySubscriptionListener {
    private static final String PROJECT_ID = "piclumen-454007";
    private static final String SUBSCRIPTION_ID = "vip-msg-sub-test";
    private static final String SERVICE_ACCOUNT_JSON = "D:\\easeus\\core\\src\\main\\resources\\google\\piclumen-454007-f13f7b8c1a76.json";
    private static final String PACKAGE_NAME = "nullart.ai";
    public static void main(String... args) {
        try {
            // Load the service account credentials from the JSON file
            InputStream inputStream = new FileInputStream(new File(SERVICE_ACCOUNT_JSON));
            if (inputStream == null) {
                throw new IOException("Service account JSON file not found: " + SERVICE_ACCOUNT_JSON);
            }

            HashSet<String> all = Sets.newHashSet(all());
            all.addAll(AndroidPublisherScopes.all());
            // Create GoogleCredentials from the input stream
            GoogleCredentials credentials = GoogleCredentials.fromStream(inputStream)
                    .createScoped(Sets.newHashSet(all));

            // Create an HTTP transport and JSON factory
//            HttpTransport httpTransport = GoogleNetHttpTransport.newTrustedTransport();
//            GsonFactory gson = GsonFactory.getDefaultInstance();

            // Create an AndroidPublisher service object
            AndroidPublisher androidPublisher = new AndroidPublisher.Builder(GoogleNetHttpTransport.newTrustedTransport(), GsonFactory.getDefaultInstance(), new HttpCredentialsAdapter(credentials))
//                    .setApplicationName("Piclumen")
                    .build();
            try {
                ProductPurchase lumens1000 = androidPublisher.purchases().products().get(PACKAGE_NAME,
                                "lumens_1000",
                                "cmmdhpfbmaomdoijdmcbacjp.AO-J1OzyLl7YLCWaWV5Ryp3K0xlelXyFPBKCXuMLzOmdbqg6bFVG1Us6PvYc7Z7mP-SZGKCSh3PWK4Rff9dO5zvVWdyJ6fFAkg")
                        .execute();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            AndroidPublisher.Monetization.Subscriptions.List request =
                    androidPublisher.monetization().subscriptions().list("nullart.ai");
            request.execute();


            AndroidPublisher.Monetization.Subscriptions subscriptions = androidPublisher.monetization().subscriptions();
            System.out.println(subscriptions.get(PACKAGE_NAME, "piclumen_standard").execute().getBasePlans());
            // You can now use the androidPublisher object to interact with the Google Play Developer API
            System.out.println("AndroidPublisher service created successfully.");


            ProjectSubscriptionName subscriptionName =
                    ProjectSubscriptionName.of(PROJECT_ID, SUBSCRIPTION_ID);

            Subscriber subscriber = Subscriber.newBuilder(subscriptionName, new MessageReceiver() {
                        @Override
                        public void receiveMessage(com.google.pubsub.v1.PubsubMessage pubsubMessage, AckReplyConsumer ackReplyConsumer) {
                            // 解码 Base64 消息
                            String decodedMessage = new String(pubsubMessage.getData().toByteArray());
                            System.out.println("Received message: " + decodedMessage);

                            try {
                                // 处理订阅事件
                                handleSubscriptionEvent(decodedMessage);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }


                            // 确认消息处理完成
                            ackReplyConsumer.ack();
                        }
                    }).setCredentialsProvider(FixedCredentialsProvider.create(credentials))
                    .build();

            subscriber.startAsync().awaitRunning();
            System.out.println("Listening for Google Play subscription events...");
            while (true) {
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        } catch (IOException | GeneralSecurityException e) {
            e.printStackTrace();
        }
    }

    private static void handleSubscriptionEvent(String decodedMessage) {
        // 解析 JSON
        JSONObject json = new JSONObject(decodedMessage);
        String eventType = json.getString("subscriptionNotification");
        String purchaseToken = json.getString("purchaseToken");

        switch (eventType) {
            case "SUBSCRIPTION_PURCHASED":
                System.out.println("📢 用户购买了订阅: " + purchaseToken);
                break;
            case "SUBSCRIPTION_RENEWED":
                System.out.println("🔄 订阅已续订: " + purchaseToken);
                break;
            case "SUBSCRIPTION_CANCELED":
                System.out.println("⚠️ 订阅被取消: " + purchaseToken);
                break;
            case "SUBSCRIPTION_EXPIRED":
                System.out.println("⛔ 订阅已过期: " + purchaseToken);
                break;
            case "SUBSCRIPTION_REFUNDED":
                System.out.println("💰 订阅已退款: " + purchaseToken);
                break;
            default:
                System.out.println("test ok");

        }
    }
}
