package com.lx.pl.pay.google.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class DeveloperNotification {

    @JsonProperty("version")
    private String version;

    @JsonProperty("packageName")
    private String packageName;

    @JsonProperty("eventTimeMillis")
    private Long eventTimeMillis;

    @JsonProperty("subscriptionNotification")
    private SubscriptionNotification subscriptionNotification;

    @JsonProperty("oneTimeProductNotification")
    private OneTimeProductNotification oneTimeProductNotification;

    @JsonProperty("voidedPurchaseNotification")
    private VoidedPurchaseNotification voidedPurchaseNotification;

    @JsonProperty("testNotification")
    private TestNotification testNotification;
}
