package com.lx.pl.pay.google.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lx.pl.pay.google.domain.GoogleProduct;
import com.lx.pl.pay.google.mapper.GoogleProductMapper;
import com.lx.pl.pay.google.service.GoogleProductService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class GoogleProductServiceImpl extends ServiceImpl<GoogleProductMapper, GoogleProduct> implements GoogleProductService {
    @Override
    public List<GoogleProduct> findByProductId(String productId) {
        return this.lambdaQuery().eq(GoogleProduct::getProductId, productId)
                .eq(GoogleProduct::getStatus, true)
                .list();
    }

    @Override
    public GoogleProduct findByPlanId(String planId) {
        return this.lambdaQuery().eq(GoogleProduct::getPlanId, planId)
                .eq(GoogleProduct::getStatus, true)
                .one();
    }

    @Override
    public String findTypeByProductId(String productId) {
        GoogleProduct one = this.lambdaQuery()
                .select(GoogleProduct::getProductType)
                .eq(GoogleProduct::getStatus, true)
                .eq(GoogleProduct::getProductId, productId)
                .groupBy(GoogleProduct::getProductType)
                .one();
        return one.getProductType();
    }
}
