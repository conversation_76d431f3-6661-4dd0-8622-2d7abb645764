package com.lx.pl.pay.google.listener;

import com.google.api.gax.core.FixedCredentialsProvider;
import com.google.auth.oauth2.GoogleCredentials;
import com.google.cloud.pubsub.v1.AckReplyConsumer;
import com.google.cloud.pubsub.v1.MessageReceiver;
import com.google.cloud.pubsub.v1.Subscriber;
import com.google.pubsub.v1.ProjectSubscriptionName;
import com.google.pubsub.v1.PubsubMessage;
import com.lx.pl.pay.common.service.SubscriptionCurrentService;
import com.lx.pl.pay.google.config.GooglePayConfiguration;
import com.lx.pl.pay.google.exception.PayGoogleException;
import com.lx.pl.pay.google.service.GoogleSubscriptionNotificationsService;
import com.lx.pl.pay.google.service.IGooglePayService;
import com.lx.pl.util.UUID;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static com.lx.pl.pay.google.constant.GoogleConstants.GOOGLE_PAY_NOTIFICATION_LOCK;
import static com.lx.pl.pay.google.exception.GoogleErrorCode.SUB_EXTERNAL_ACCOUNT_IS_NULL;
import static com.lx.pl.pay.google.exception.GoogleErrorCode.TEST_NOT_ALLOWED_PRO;

@Component
@Profile({"test", "dev"})
public class GoogleAsyncSubscriptionListener implements ApplicationContextAware {
    Logger log = LoggerFactory.getLogger(GoogleAsyncSubscriptionListener.class);

    @Resource
    private RedissonClient redissonClient;
    @Resource
    private GoogleSubscriptionNotificationsService googleSubscriptionNotificationsService;

    @Resource
    private GooglePayConfiguration googlePayConfiguration;
    @Resource
    private IGooglePayService googlePayService;
    private ApplicationContext applicationContext;
    @Resource
    private SubscriptionCurrentService subscriptionCurrentService;
    @Resource
    private GoogleCredentials credentials;

    private ExecutorService executor = Executors.newFixedThreadPool(1);

    @PostConstruct
    public void init() {
        executor.execute(this::subscribeAsync);
    }

    public void subscribeAsync() {
        // Instantiate an asynchronous message receiver.
        MessageReceiver receiver =
                (PubsubMessage message, AckReplyConsumer consumer) -> {
                    // Handle incoming message, then ack the received message.
                    MDC.put("TRACE_ID", UUID.fastUUID() + "-" + "google-pubsub");
                    log.info("Id: " + message.getMessageId());
                    log.info("Data: " + message.getData().toStringUtf8());
                    log.info("Data: " + message);
                    RLock lock = redissonClient.getLock(GOOGLE_PAY_NOTIFICATION_LOCK + message.getMessageId());
                    try {
                        lock.lock();
                        String decodedMessage = new String(message.getData().toByteArray());
                        googlePayService.handleRTDNEvent(decodedMessage, message.getMessageId());
                        consumer.ack();
                    } catch (PayGoogleException e) {
                        String code = e.getCode();
                        if (TEST_NOT_ALLOWED_PRO.getCode().equals(code)) {
                            log.info("test not allowed to prod OR prod not allowed test");
                            consumer.ack();
                        } else if (SUB_EXTERNAL_ACCOUNT_IS_NULL.getCode().equals(code)) {
                            log.info("external account is null or cant find user id");
                            consumer.ack();
                        } else {
                            log.error("receiver pubsub msg error", e);
                            consumer.nack();
                        }
                    } catch (Exception e) {
                        log.error("receiver pubsub msg error", e);
                        consumer.nack();
                    } finally {
                        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                            lock.unlock();
                        }
                        MDC.clear();
                    }
                };

        Subscriber subscriber = null;
        try {
            subscriber = Subscriber.newBuilder(ProjectSubscriptionName.of(googlePayConfiguration.getProjectId(), googlePayConfiguration.getSubscriptionId()), receiver)
                    .setCredentialsProvider(FixedCredentialsProvider.create(credentials))
                    .build();
            // Start the subscriber.
            subscriber.startAsync().awaitRunning();
            log.info("Listening for messages on {}:\n", googlePayConfiguration.getSubscriptionId());
            // Allow the subscriber to run for 30s unless an unrecoverable error occurs.
//            subscriber.awaitTerminated(30, TimeUnit.SECONDS);
        } catch (Exception exception) {
            log.error("Subscriber error", exception);
            // Shut down the subscriber after 30s. Stop receiving messages.
            if (subscriber != null) {
                subscriber.stopAsync();
            }
            executor.execute(this::init);
        }
    }


    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}