package com.lx.pl.pay.google.model;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "Google 产品信息")
public class GoogleProductVo {

    @TableField(value = "product_id")
    @Schema(description = "订阅商品ID")
    private String productId;

    @TableField(value = "plan_id")
    @Schema(description = "订阅计划ID")
    private String planId;

    @TableField(value = "product_type")
    @Schema(description = "plan/one")
    private String productType;

    @TableField(value = "price_interval")
    @Schema(description = "month/year")
    private String priceInterval;

    @TableField(value = "plan_level")
    @Schema(description = "standard/pro")
    private String planLevel;

    @TableField(value = "vip_level")
    @Schema(description = "1/-1")
    private Integer vipLevel;

    @Schema(description = "规则")
    private JSONObject lumenRule;

    @Schema(description = "首充lumen/订阅试用lumen")
    private Integer initialLumen;
}
