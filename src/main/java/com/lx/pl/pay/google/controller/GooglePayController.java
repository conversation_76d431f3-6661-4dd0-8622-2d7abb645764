package com.lx.pl.pay.google.controller;

import com.lx.pl.annotation.Authorization;
import com.lx.pl.annotation.CurrentUser;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.dto.generic.R;
import com.lx.pl.enums.LogicErrorCode;
import com.lx.pl.exception.LogicException;
import com.lx.pl.pay.common.dto.GoogleLotteryVo;
import com.lx.pl.pay.common.service.PayLotteryLogService;
import com.lx.pl.pay.google.dto.GoogleActivityConfigVo;
import com.lx.pl.pay.google.model.GooglePayVerifyRequest;
import com.lx.pl.pay.google.model.GoogleProductRequest;
import com.lx.pl.pay.google.model.GoogleProductVo;
import com.lx.pl.pay.google.model.GoogleValidateResponse;
import com.lx.pl.pay.google.service.GoogleActivityConfigService;
import com.lx.pl.pay.google.service.IGooglePayService;
import com.lx.pl.pay.google.service.LotteryServiceGoogle;
import com.lx.pl.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.redisson.api.RLock;
import org.redisson.api.RRateLimiter;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.time.Duration;
import java.util.List;

@RestController
@RequestMapping("api/google")
@Tag(name = "google支付")
public class GooglePayController {

    Logger log = LoggerFactory.getLogger("google-pay-msg");

    @Autowired
    private IGooglePayService googlePayService;
    @Autowired
    private UserService userApplication;
    @Value("${apple.visitor.purchase:false}")
    private Boolean vistorPurchase;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private LotteryServiceGoogle lotteryService;
    @Autowired
    private PayLotteryLogService payLotteryLogService;
    @Autowired
    private GoogleActivityConfigService activityService;

    @Value("${pay.lottery.rate.permits:200}")
    private int lotteryRatePermits;
    @Value("${pay.lottery.rate.interval-seconds:60}")
    private int lotteryRateIntervalSeconds;
    @PostMapping("/validate")
    @Operation(summary = "google支付验证")
    @Authorization
    public R<GoogleValidateResponse> validate(@RequestBody GooglePayVerifyRequest request,
                                              @CurrentUser @Parameter(hidden = true) User userPO) throws IOException {
//        String requestBody = req.getReader().lines().collect(Collectors.joining("\n"));
        log.info("google支付验证: {}", request);
        GoogleValidateResponse rtn = googlePayService.verifyInApp(request, userPO);
        return R.success(rtn);
    }

    @PostMapping("/query-product")
    @Operation(summary = "查询产品ID")
    @Authorization
    public R<List<GoogleProductVo>> queryProduct(@RequestBody(required = false) GoogleProductRequest request) {
        List<GoogleProductVo> rtn = googlePayService.queryProduct(request);
        return R.success(rtn);
    }

    @Operation(summary = "抽奖：按权重返回一个折扣比例（支持限流与幂等）")
    @GetMapping(path = "/lottery")
    @Authorization
    public R<List<GoogleLotteryVo>> lotteryDiscount(@Parameter(hidden = true) @CurrentUser User user, HttpServletRequest request) {
        String rateKey = "google-pay:lottery:rate";
        RRateLimiter limiter = redissonClient.getRateLimiter(rateKey);
        if (!limiter.isExists()) {
            limiter.trySetRate(org.redisson.api.RateType.OVERALL, lotteryRatePermits, Duration.ofSeconds(lotteryRateIntervalSeconds));
        }
        if (!limiter.tryAcquire()) {
            return R.fail(429, "Too Many Requests");
        }
        // 判断活动是是否再有效期
        if (!lotteryService.isLotteryActive()) {
            throw new LogicException(LogicErrorCode.ACTIVITY_NOT_STARTED_OR_ENDED);
        }
        RLock lock = redissonClient.getLock("google-pay:lottery:lock:" + user.getId());
        // 上锁
        try {
            lock.lock();   // 限流：每 intervalSeconds 允许 permits 次
            String platform = request.getHeader("Platform");
            // 判断是否抽过奖
            if (payLotteryLogService.hasParticipated(user.getId(), platform, "sweep_stakes")) {
                log.info("用户 {} 已经参与过抽奖，不再重复抽奖", user.getLoginName());
                throw new LogicException(LogicErrorCode.HAS_PARTICIPATED);
            }
            List<GoogleLotteryVo> awards = lotteryService.getAwardsGoogle(user, platform);
            return R.success(awards);
        } finally {
            if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
            }
        }
    }
    @Operation(summary = "查询活动配置列表")
    @GetMapping("/activity/list")
    @Authorization
    public R<List<GoogleActivityConfigVo>> listActivity(@CurrentUser @Parameter(hidden = true) User user, HttpServletRequest request) {
        log.info("listActivity start: {}", user.getLoginName());
        String platform = request.getHeader("Platform");
        return R.success(activityService.listActivity(user, platform));
    }


}
