package com.lx.pl.pay.google.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lx.pl.db.mysql.gen.entity.MyBaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName("google_product")
@Schema(description = "Google 产品信息")
public class GoogleProduct extends MyBaseEntity {

    @TableId(value = "id")
    @Schema(description = "订阅ID")
    private Long id;

    @TableField(value = "product_id")
    @Schema(description = "订阅商品ID（如 monthly_premium）")
    private String productId;

    private String planId;

    @TableField(value = "product_type")
    @Schema(description = "plan/one")
    private String productType;

    @TableField(value = "price_interval")
    @Schema(description = "month/year")
    private String priceInterval;

    @TableField(value = "plan_level")
    @Schema(description = "standard/pro")
    private String planLevel;

    @TableField(value = "vip_level")
    @Schema(description = "1/-1")
    private Integer vipLevel;

    @TableField(value = "lumen")
    @Schema(description = "lumen数量")
    private Integer lumen;


    /**
     * lumen 数量
     */

    @TableField(value = "initial_lumen")
    @Schema(description = "订阅： 试用订阅发放的点数")
    private Integer initialLumen;


    private Boolean status;

    private String mark;

    private String rule;
}
