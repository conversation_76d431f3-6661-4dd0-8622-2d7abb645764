package com.lx.pl.pay.google.model;

import lombok.Data;

@Data
public class GooglePayVerifyRequest {
    /**
     * {
     *   "orderId": "GPA.3312-2602-2159-47038",
     *   "packageName": "com.yile.ai",
     *   "productId": "credit_200",
     *   "purchaseTime": *************,
     *   "purchaseState": 0,
     *   "purchaseToken": "gjfdapdihgcmjlpbfcaemepj.AO-J1Oy0esViHGhDaVmQau8Y3DbBvYMALIZVG30i8cEaglEIBR5CLh4rJqWAZzz2VIbKhnhb5bWZaWVwtBwUriUDz2hQ0Mr2Qg",
     *   "obfuscatedAccountId": "77",
     *   "quantity": 1,
     *   "acknowledged": false
     * }]
     *{
     *   "orderId": "GPA.3367-4751-8384-74350",
     *   "packageName": "com.yile.ai",
     *   "productId": "aiease.pro",
     *   "purchaseTime": *************,
     *   "purchaseState": 0,
     *   "purchaseToken": "gffjhbjlcdmlbafpopaenjdh.AO-J1OyQ3o4l-eKIEhkA3x49DZbyzm2SPWDWAnu-QEh99ZjwUAeKybehd9fXc1Yk3a-dENVvE10uRJ1eHf5y67TI51yj3G4CHw",
     *   "obfuscatedAccountId": "77",
     *   "quantity": 1,
     *   "autoRenewing": true,
     *   "acknowledged": false,
          "originalJson":"******" ;
     *
     * }
     *
     */
    private String orderId;
    private String packageName;
    private String productId;
    private long purchaseTime;
    private int purchaseState;
    private String purchaseToken;
    private String obfuscatedAccountId;
    private String signature;
    private Boolean autoRenewing;
    private Boolean acknowledged;
    private String originalJson;
}


