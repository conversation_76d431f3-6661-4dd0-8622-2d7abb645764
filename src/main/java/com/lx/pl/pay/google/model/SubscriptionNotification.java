package com.lx.pl.pay.google.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class SubscriptionNotification {

    @JsonProperty("version")
    private String version;

    @JsonProperty("notificationType")
    private int notificationType;

    @JsonProperty("purchaseToken")
    private String purchaseToken;

    @JsonProperty("subscriptionId")
    private String subscriptionId;

    @JsonProperty("offerDetails")
    private OfferDetails offerDetails;


//    @JsonProperty("userId")
//    private String userId;
//
//    @JsonProperty("profileId")
//    private String profileId;
//
//    @JsonProperty("lineItems")
//    private List<LineItem> lineItems;
//
//    @JsonProperty("linkedPurchaseToken")
//    private String linkedPurchaseToken;
//
//    @JsonProperty("cancellationTimeMillis")
//    private Long cancellationTimeMillis;
//
//    @JsonProperty("cancelReason")
//    private String cancelReason;
//
//    @JsonProperty("userCancellationTimeMillis")
//    private Long userCancellationTimeMillis;
//
//    @JsonProperty("developerPayload")
//    private String developerPayload;
//
//
//    @Data
//    public static class LineItem {
//
//        @JsonProperty("itemId")
//        private String itemId;
//
//        @JsonProperty("itemType")
//        private String itemType;
//
//        @JsonProperty("priceAmountMicros")
//        private Long priceAmountMicros;
//
//        @JsonProperty("priceCurrencyCode")
//        private String priceCurrencyCode;
//
//        @JsonProperty("startTimeMillis")
//        private Long startTimeMillis;
//
//        @JsonProperty("expiryTimeMillis")
//        private Long expiryTimeMillis;
//
//        @JsonProperty("autoRenewing")
//        private Boolean autoRenewing;
//
//        @JsonProperty("paymentState")
//        private String paymentState;
//
//        @JsonProperty("countryCode")
//        private String countryCode;
//
//        @JsonProperty("developerPayload")
//        private String developerPayload;
//
//        @JsonProperty("freeTrialEndTimeMillis")
//        private Long freeTrialEndTimeMillis;
//
//        @JsonProperty("introductoryPriceInfo")
//        private IntroductoryPriceInfo introductoryPriceInfo;
//
//        @JsonProperty("offerDetails")
//        private OfferDetails offerDetails;
//    }
//
//    @Data
//    public static class IntroductoryPriceInfo {
//
//        @JsonProperty("offerId")
//        private String offerId;
//
//        @JsonProperty("introductoryPriceAmountMicros")
//        private Long introductoryPriceAmountMicros;
//
//        @JsonProperty("introductoryPriceCurrencyCode")
//        private String introductoryPriceCurrencyCode;
//
//        @JsonProperty("introductoryPriceCycles")
//        private Integer introductoryPriceCycles;
//
//        @JsonProperty("introductoryPricePeriod")
//        private String introductoryPricePeriod;
//    }
//



    @Data
    public static class OfferDetails {

        @JsonProperty("offerId")
        private String offerId;

        @JsonProperty("offerType")
        private String offerType;

        @JsonProperty("basePlanId")
        private String basePlanId;

        @JsonProperty("offerTags")
        private List<String> offerTags;
    }


}

