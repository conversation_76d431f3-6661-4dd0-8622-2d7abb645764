package com.lx.pl.pay.google.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * apple_activity_config 表实体
 */
@Data
@Schema(description = "Apple 活动配置")
public class GoogleActivityConfigVo {
    @Schema(description = "ID")
    private Long id;
    @Schema(description = "活动名称")
    private String name;
    @Schema(description = "活动编码")
    private String code;
    @Schema(description = "是否启用")
    private Boolean enable;
    @Schema(description = "开始日期 (秒时间戳)")
    private Long startTime;
    @Schema(description = "截止日期 (秒时间戳)")
    private Long deadline;
}

