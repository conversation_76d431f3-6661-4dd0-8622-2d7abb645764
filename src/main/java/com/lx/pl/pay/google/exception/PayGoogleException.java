package com.lx.pl.pay.google.exception;

import com.lx.pl.exception.BaseException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 支付异常类
 */
public class PayGoogleException extends BaseException {
    Logger log = LoggerFactory.getLogger("google-pay-msg");
    private static final long serialVersionUID = 1L;

    public PayGoogleException(GoogleErrorCode errorCode) {
        super("payment", errorCode.getCode(), null, errorCode.getMessage());
        log.error("支付异常代码: {}, 消息: {}, {}", errorCode.getCode(), errorCode.getMessage(), errorCode.getDescription());
    }

    public PayGoogleException(GoogleErrorCode errorCode, Throwable cause) {
        super("payment", errorCode.getCode(), null, String.valueOf(cause));
        log.error("付异常代码: {}, 消息: {}, {}", errorCode.getCode(), errorCode.getMessage(), errorCode.getDescription());
    }

}