package com.lx.pl.pay.google.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class OneTimeProductNotification {

    @JsonProperty("version")
    private String version;

    /**
     * @see com.ai.pay.google.enums.OneTimeNotificationType
     */
    @JsonProperty("notificationType")
    private int notificationType;

    @JsonProperty("purchaseToken")
    private String purchaseToken;

    @JsonProperty("sku")
    private String sku;
}
