package com.lx.pl.pay.google.listener;

import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.json.JsonFactory;
import com.google.api.client.json.gson.GsonFactory;
import com.google.auth.oauth2.GoogleCredentials;
import com.google.auth.http.HttpCredentialsAdapter;
import com.google.api.services.androidpublisher.AndroidPublisher;
import com.google.api.services.androidpublisher.model.SubscriptionPurchase;

import java.io.FileInputStream;
import java.io.IOException;
import java.security.GeneralSecurityException;

public class GooglePlayValidator {
    private static final String SERVICE_ACCOUNT_JSON = "path/to/your-service-account.json";
    private static final String PACKAGE_NAME = "com.yourapp.package";

    public static SubscriptionPurchase verifySubscription(String purchaseToken) throws IOException {
//        GoogleCredentials credentials = GoogleCredentials.fromStream(new FileInputStream(SERVICE_ACCOUNT_JSON))
//                .createScoped("https://www.googleapis.com/auth/androidpublisher");
        GoogleCredentials credentials = GoogleCredentials.fromStream(
                new FileInputStream("/path/to/your-service-account-key.json")
        );

        AndroidPublisher publisher = null;
        try {
            publisher = new AndroidPublisher.Builder(
                    GoogleNetHttpTransport.newTrustedTransport(),
                    GsonFactory.getDefaultInstance(),
                    new HttpCredentialsAdapter(credentials)
            ).setApplicationName("YourAppName").build();
        } catch (GeneralSecurityException e) {
            throw new RuntimeException(e);
        }

        return publisher.purchases().subscriptions()
                .get(PACKAGE_NAME, "your_subscription_id", purchaseToken)
                .execute();
    }
}
