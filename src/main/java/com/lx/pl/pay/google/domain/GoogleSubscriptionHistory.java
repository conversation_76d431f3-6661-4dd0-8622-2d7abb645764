package com.lx.pl.pay.google.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lx.pl.db.mysql.gen.entity.MyBaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName("google_subscription_history")
@Schema(description = "订阅状态变更历史记录表")
public class GoogleSubscriptionHistory extends MyBaseEntity {

    @TableId(value = "id")
    @Schema(description = "订阅ID")
    private Long id;

    @TableField(value = "user_id")
    @Schema(description = "关联用户ID")
    private Long userId;

    @TableField(value = "login_name")
    @Schema(description = "关联用户登录名称")
    private String loginName;

    private Integer notificationType;

    private String latestOrderId;

    @TableField(value = "purchase_token")
    @Schema(description = "Google Play 购买 Token")
    private String purchaseToken;

    @TableField(value = "product_id")
    @Schema(description = "订阅商品ID（如 monthly_premium）")
    private String productId;

    private String planId;

    private String oldProductId;

    private String oldPlanId;

    @TableField(value = "start_time")
    @Schema(description = "订阅生效时间")
    private Long startTime;

    @TableField(value = "expiry_time")
    @Schema(description = "订阅到期时间")
    private Long expiryTime;

    @TableField(value = "subscription_state")
    @Schema(description = "订阅状态")
    private String subscriptionState;

    @TableField(value = "linked_purchase_token")
    @Schema(description = "旧订阅的 Token（如用户升级订阅时存储旧Token）")
    private String linkedPurchaseToken;

    @TableField(value = "details")
    @Schema(description = "额外信息（JSON 格式存储 Google API 响应）")
    private String details;

    @TableField(value = "offer_id")
    @Schema(description = "offerId 折扣商品ID， 取自 details")
    private String offerId;

}
