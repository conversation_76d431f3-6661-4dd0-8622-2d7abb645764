package com.lx.pl.pay.google.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lx.pl.pay.google.domain.GoogleSubscriptions;
import com.lx.pl.pay.google.mapper.GoogleSubscriptionsMapper;
import com.lx.pl.pay.google.service.GoogleProductService;
import com.lx.pl.pay.google.service.GoogleSubscriptionsService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;

@Service
public class GoogleSubscriptionsServiceImpl extends ServiceImpl<GoogleSubscriptionsMapper, GoogleSubscriptions> implements GoogleSubscriptionsService {

    @Resource
    private GoogleProductService googleProductService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public GoogleSubscriptions saveIfNeed(GoogleSubscriptions googleSubscriptions) {
        GoogleSubscriptions subscription = this.queryByLatestOrderId(googleSubscriptions.getLatestOrderId());
        if (subscription == null) {
            this.save(googleSubscriptions);
            return googleSubscriptions;
        }
        return null;
    }

    @Override
    public GoogleSubscriptions queryActiveSubscriptionByUserId(Long userId) {
        return this.lambdaQuery()
                .eq(GoogleSubscriptions::getUserId, userId)
                .eq(GoogleSubscriptions::getSubscriptionState, "SUBSCRIPTION_STATE_ACTIVE")
                .one();
    }

    @Override
    public GoogleSubscriptions queryByToken(String purchaseToken) {
        return this.lambdaQuery().eq(GoogleSubscriptions::getPurchaseToken, purchaseToken).one();
    }

    @Override
    public GoogleSubscriptions queryByLatestOrderId(String latestOrderId) {
        return this.lambdaQuery()
                .eq(GoogleSubscriptions::getLatestOrderId, latestOrderId)
                .orderByDesc(GoogleSubscriptions::getCreateTime)
                .last("limit 1")
                .one();
    }

    @Override
    public GoogleSubscriptions queryByLatestSubscriptionByUserId(Long userId) {
        return this.lambdaQuery()
                .eq(GoogleSubscriptions::getUserId, userId)
                .orderByDesc(GoogleSubscriptions::getCreateTime)
                .last("limit 1")
                .one();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOrSaveByLatestOrderId(GoogleSubscriptions googleSubscriptions) {
        GoogleSubscriptions existSub = this.queryByLatestOrderId(googleSubscriptions.getLatestOrderId());
        if (existSub == null) {
            googleSubscriptions.setCreateTime(LocalDateTime.now());
            this.save(googleSubscriptions);
        } else {
            this.lambdaUpdate().eq(GoogleSubscriptions::getId, existSub.getId())
                    .set(GoogleSubscriptions::getUpdateTime, LocalDateTime.now())
                    .set(GoogleSubscriptions::getStartTime, googleSubscriptions.getStartTime())
                    .set(GoogleSubscriptions::getLatestOrderId, googleSubscriptions.getLatestOrderId())
                    .set(GoogleSubscriptions::getExpiryTime, googleSubscriptions.getExpiryTime())
                    .set(GoogleSubscriptions::getSubscriptionState, googleSubscriptions.getSubscriptionState())
                    .set(GoogleSubscriptions::getAutoRenewing, googleSubscriptions.getAutoRenewing())
                    .set(GoogleSubscriptions::getProductId, googleSubscriptions.getProductId())
                    .set(GoogleSubscriptions::getPlanId, googleSubscriptions.getPlanId())
                    .set(GoogleSubscriptions::getRegionCode, googleSubscriptions.getRegionCode())
                    .set(GoogleSubscriptions::getStartTime, googleSubscriptions.getStartTime())
                    .set(GoogleSubscriptions::getLinkedPurchaseToken, googleSubscriptions.getLinkedPurchaseToken())
                    .set(GoogleSubscriptions::getRecurringPrice, googleSubscriptions.getRecurringPrice())
                    .set(GoogleSubscriptions::getCanceledStateContext, googleSubscriptions.getCanceledStateContext())
                    .update();
        }
    }

    @Override
    public void updateExpiryTimeAndState(GoogleSubscriptions googleSubscriptions) {
        this.lambdaUpdate().eq(GoogleSubscriptions::getLatestOrderId, googleSubscriptions.getLatestOrderId())
                .set(GoogleSubscriptions::getExpiryTime, googleSubscriptions.getExpiryTime())
                .set(GoogleSubscriptions::getSubscriptionState, googleSubscriptions.getSubscriptionState())
                .set(GoogleSubscriptions::getUpdateTime, LocalDateTime.now())
                .update();
    }
}
