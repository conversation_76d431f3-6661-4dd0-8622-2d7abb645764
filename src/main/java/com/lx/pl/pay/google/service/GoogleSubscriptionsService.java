package com.lx.pl.pay.google.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lx.pl.pay.google.domain.GoogleSubscriptions;

public interface GoogleSubscriptionsService extends IService<GoogleSubscriptions> {

    GoogleSubscriptions saveIfNeed(GoogleSubscriptions googleSubscriptions);

    GoogleSubscriptions queryActiveSubscriptionByUserId(Long userId);

    GoogleSubscriptions queryByToken(String purchaseToken);

    GoogleSubscriptions queryByLatestOrderId(String latestOrderId);

    GoogleSubscriptions queryByLatestSubscriptionByUserId(Long userId);

    void updateOrSaveByLatestOrderId(GoogleSubscriptions googleSubscriptions);

    void updateExpiryTimeAndState(GoogleSubscriptions googleSubscriptions);
}
