package com.lx.pl.pay.google.enums;

public enum AcknowledgementState {
    ACKNOWLEDGEMENT_STATE_UNSPECIFIED("ACKNOWLEDGEMENT_STATE_UNSPECIFIED", "未指定确认状态"),
    ACKNOWLEDGEMENT_STATE_PENDING("ACK<PERSON>OWLEDGEMENT_STATE_PENDING", "订阅尚未确认"),
    ACKNOWLEDGEMENT_STATE_ACKNOWLEDGED("ACKNOWLEDGEMENT_STATE_ACKNOWLEDGED", "订阅已确认");

    private final String value;
    private final String description;

    AcknowledgementState(String value, String description) {
        this.value = value;
        this.description = description;
    }

    public String getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    public static AcknowledgementState fromValue(String value) {
        for (AcknowledgementState state : AcknowledgementState.values()) {
            if (state.getValue().equalsIgnoreCase(value)) {
                return state;
            }
        }
        throw new IllegalArgumentException("Unknown AcknowledgementState value: " + value);
    }
}
