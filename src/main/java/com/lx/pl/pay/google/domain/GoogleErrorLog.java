
package com.lx.pl.pay.google.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lx.pl.db.mysql.gen.entity.MyBaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName("google_error_log")
@Schema(description = "用户订阅信息表")
public class GoogleErrorLog extends MyBaseEntity {

    private static final long serialVersionUID = 1084526169979572654L;
    @TableId(value = "id")
    @Schema(description = "订阅ID")
    private Long id;

    private String type;

    @TableField(value = "purchase_token")
    @Schema(description = "Google Play 购买 Token")
    private String purchaseToken;

    private String productId;

    private Integer qty;

    @TableField(value = "error")
    @Schema(description = "错误码")
    private String error;

}