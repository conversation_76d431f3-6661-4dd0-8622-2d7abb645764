package com.lx.pl.pay.google.enums;

import lombok.Getter;

public enum OneTimePurchaseState {
    /**
     * 0. 已购买 1. 已取消 2. 待处理
     */
    PURCHASED(0, "用户成功购买了一次性商品"),
    CANCELED(1, "用户已取消待处理的一次性商品购买交易"),
    PENDING(2, "用户已取消待处理的一次性商品购买交易");

    @Getter
    private int type;
    private String desc;

    OneTimePurchaseState(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static OneTimePurchaseState fromInt(int type) {
        for (OneTimePurchaseState oneTimePurchaseState : OneTimePurchaseState.values()) {
            if (oneTimePurchaseState.type == type) {
                return oneTimePurchaseState;
            }
        }
        return null;
    }
}
