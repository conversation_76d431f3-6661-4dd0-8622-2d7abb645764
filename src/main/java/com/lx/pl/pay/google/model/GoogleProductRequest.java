package com.lx.pl.pay.google.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 *
 * <AUTHOR>
 */
@Data
public class GoogleProductRequest {

    @Schema(description = "订阅类型：pro")
    private String planLevel;

    @Schema(description = "订阅周期月/年在：month/year")
    private String priceInterval;

    @Schema(description = "购买credit数量：200")
    private String creditNum;

    public boolean emptyParam() {
        return planLevel == null && priceInterval == null && creditNum == null;
    }
}
