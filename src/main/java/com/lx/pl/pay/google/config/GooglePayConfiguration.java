package com.lx.pl.pay.google.config;

import com.google.api.client.googleapis.auth.oauth2.GoogleIdTokenVerifier;
import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.gson.GsonFactory;
import com.google.api.services.androidpublisher.AndroidPublisher;
import com.google.api.services.androidpublisher.AndroidPublisherScopes;
import com.google.api.services.pubsub.PubsubScopes;
import com.google.auth.http.HttpCredentialsAdapter;
import com.google.auth.oauth2.GoogleCredentials;
import com.google.common.collect.Sets;
import lombok.Getter;
import lombok.Setter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;

import java.io.IOException;
import java.security.GeneralSecurityException;
import java.util.HashSet;
import java.util.List;

@Configuration
@ConfigurationProperties(prefix = "google")
@Getter
@Setter
public class GooglePayConfiguration {

    Logger log = LoggerFactory.getLogger("google-pay-msg");
    // 将google 提供的私钥放入项目 classpath 下（放在项目 resources 文件夹下即可）
    @Value(value = "classpath:google/piclumen-454007-f13f7b8c1a76.json")
    private Resource resource;
    @Value("${google.project-id:piclumen-454007}")
    private String projectId;
    @Value("${google.subscription-id:vip-msg-sub}")
    private String subscriptionId;
    @Value("${google.subscription-name:projects/piclumen-454007/subscriptions/vip-msg-sub}")
    private String subscriptionName;
    @Value("${google.verifier-audience:}")
    private List<String> verifierAudience;
    private String packageName;
    private String publicKey;

    @Bean
    public GoogleCredentials googleCredentials() throws IOException {
        HashSet<String> all = Sets.newHashSet(PubsubScopes.all());
        all.addAll(AndroidPublisherScopes.all());
        // 凭证
        return GoogleCredentials.fromStream(resource.getInputStream())
                .createScoped(all);
    }


    @Bean
    public AndroidPublisher androidPublisher(GoogleCredentials googleCredentials) throws IOException {
        AndroidPublisher androidPublisher;
        try {
            androidPublisher = new AndroidPublisher
                    .Builder(GoogleNetHttpTransport.newTrustedTransport(), GsonFactory.getDefaultInstance(), new HttpCredentialsAdapter(googleCredentials))
                    .setApplicationName("Piclumen")
                    .build();
        } catch (GeneralSecurityException | IOException ex) {
            log.error("初始化Google服务失败：", ex);
            throw new RuntimeException(ex);
        }
        return androidPublisher;
    }

    @Bean
    public GoogleIdTokenVerifier googleIdTokenVerifier() {
        GoogleIdTokenVerifier verifier =
                new GoogleIdTokenVerifier.Builder(new NetHttpTransport(), new GsonFactory())
                        /**
                         * Please change example.com to match with value you are providing while creating
                         * subscription as provided in @see <a
                         * href="https://github.com/GoogleCloudPlatform/java-docs-samples/tree/main/appengine-java8/pubsub">README</a>.
                         */
                        .setAudience(verifierAudience)
                        .build();

        return verifier;
    }
}
