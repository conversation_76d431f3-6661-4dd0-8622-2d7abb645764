package com.lx.pl.pay.google.enums;

import lombok.Getter;

/**
 * 通知的类型。它可以具有以下值：
 * (1) ONE_TIME_PRODUCT_PURCHASED - 用户成功购买了一次性商品。
 * (2) ONE_TIME_PRODUCT_CANCELED - 用户已取消待处理的一次性商品购买交易。
 */
public enum OneTimeNotificationType {

    ONE_TIME_PRODUCT_PURCHASED(1, "用户成功购买了一次性商品"),
    ONE_TIME_PRODUCT_CANCELED(2, "用户已取消待处理的一次性商品购买交易");

    @Getter
    private String desc;

    @Getter
    private int type;

    OneTimeNotificationType(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static OneTimeNotificationType fromInt(int type) {
        for (OneTimeNotificationType value : OneTimeNotificationType.values()) {
            if (value.getType() == type) {
                return value;
            }
        }
        throw new IllegalArgumentException("Unknown notification type: " + type);
    }
}
