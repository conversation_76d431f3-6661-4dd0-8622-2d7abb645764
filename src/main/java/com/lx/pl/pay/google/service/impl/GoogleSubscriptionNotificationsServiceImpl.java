package com.lx.pl.pay.google.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lx.pl.pay.google.domain.GoogleSubscriptionNotifications;
import com.lx.pl.pay.google.mapper.GoogleSubscriptionNotificationsMapper;
import com.lx.pl.pay.google.service.GoogleSubscriptionNotificationsService;
import org.springframework.stereotype.Service;

@Service
public class GoogleSubscriptionNotificationsServiceImpl extends ServiceImpl<GoogleSubscriptionNotificationsMapper, GoogleSubscriptionNotifications> implements GoogleSubscriptionNotificationsService {
    @Override
    public GoogleSubscriptionNotifications queryByMessageId(String messageId) {
        return this.lambdaQuery().eq(GoogleSubscriptionNotifications::getMessageId, messageId).one();
    }
}
