package com.lx.pl.pay.google.service.impl;

import cn.hutool.json.JSONUtil;
import com.google.api.services.androidpublisher.AndroidPublisher;
import com.google.api.services.androidpublisher.model.*;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.pay.apple.VipPlatform;
import com.lx.pl.pay.common.domain.PayLumenRecord;
import com.lx.pl.pay.common.domain.SubscriptionCurrent;
import com.lx.pl.pay.common.service.PayLumenRecordService;
import com.lx.pl.pay.common.service.SubscriptionCurrentService;
import com.lx.pl.pay.google.config.GooglePayConfiguration;
import com.lx.pl.pay.google.domain.*;
import com.lx.pl.pay.google.enums.*;
import com.lx.pl.pay.google.exception.GoogleErrorCode;
import com.lx.pl.pay.google.exception.PayGoogleException;
import com.lx.pl.pay.google.model.*;
import com.lx.pl.pay.google.service.*;
import com.lx.pl.service.UserService;
import com.lx.pl.service.VipService;
import com.lx.pl.util.MyEnvironmentUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.Signature;
import java.security.spec.X509EncodedKeySpec;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static com.lx.pl.pay.common.util.TimeUtil.convertToUtcTimestamp;
import static com.lx.pl.pay.google.constant.GoogleConstants.GOOGLE_PAY_VERIFY_LOCK;
import static com.lx.pl.pay.google.enums.SubscriptionNotificationType.*;
import static com.lx.pl.pay.google.enums.SubscriptionState.SUBSCRIPTION_STATE_EXPIRED;
import static com.lx.pl.pay.google.enums.SubscriptionState.SUBSCRIPTION_STATE_IN_GRACE_PERIOD;
import static com.lx.pl.pay.google.exception.GoogleErrorCode.*;

@Service
public class GooglePayServiceImpl implements IGooglePayService, ApplicationContextAware {
    Logger log = LoggerFactory.getLogger("google-pay-msg");
    @Autowired
    private GooglePayConfiguration googlePayConfiguration;
    @Resource
    private RedissonClient redissonClient;

    @Resource
    private AndroidPublisher androidPublisher;
    @Resource
    private GoogleSubscriptionNotificationsService googleSubscriptionNotificationsService;
    @Resource
    private GoogleOneTimePurchasesService googleOneTimePurchasesService;
    @Resource
    private GoogleSubscriptionHistoryService googleSubscriptionHistoryService;
    @Resource
    private GoogleProductService googleProductService;
    @Resource
    private GoogleSubscriptionsService googleSubscriptionsService;
    @Resource
    private GoogleDeferredSubscriptionsService googleDeferredSubscriptionsService;
    @Resource
    private PayLumenRecordService payLumenRecordService;
    @Resource
    private SubscriptionCurrentService subscriptionCurrentService;
    @Resource
    private GoogleErrorLogService googleErrorLogService;
    private static final int MAX_RETRIES = 3;
    @Resource
    private VipService vipService;
    private ApplicationContext applicationContext;
    @Resource
    private UserService userApplication;
    @Resource
    private MyEnvironmentUtils environmentUtils;
    @Resource
    private IGooglePlayOrderService googlePlayOrderService;
    // thread pool
    private static final ThreadPoolExecutor THREAD_POOL = new ThreadPoolExecutor(1, 2, 0L, TimeUnit.SECONDS, new LinkedBlockingQueue<>(10));

    /**
     * 处理 RTDN 消息
     *
     * @param decodedMessage
     * @param messageId
     */
    @Override
    public void handleRTDNEvent(String decodedMessage, String messageId) throws Exception {
        GoogleSubscriptionNotifications googleNotification = googleSubscriptionNotificationsService.queryByMessageId(messageId);
        if (googleNotification != null) {
            log.info("消息ID：{}，已经处理过，直接返回", messageId);
            return;
        }

        // 解析 JSON
        DeveloperNotification notification = JSONUtil.toBean(decodedMessage, DeveloperNotification.class);
        log.info("消息ID：{}，消息内容：{}", messageId, notification);
        String purchaseToken = null;
        String notificationType = null;

        if (notification.getSubscriptionNotification() != null) {
            processNotification(notification.getSubscriptionNotification());
            notificationType = "subscriptionNotification";
            purchaseToken = notification.getSubscriptionNotification().getPurchaseToken();
        } else if (notification.getOneTimeProductNotification() != null) {
            processNotification(notification.getOneTimeProductNotification());
            notificationType = "oneTimeProductNotification";
            purchaseToken = notification.getOneTimeProductNotification().getPurchaseToken();
        } else if (notification.getVoidedPurchaseNotification() != null) {
            processNotification(notification.getVoidedPurchaseNotification());
            notificationType = "voidedPurchaseNotification";
            purchaseToken = notification.getVoidedPurchaseNotification().getPurchaseToken();
        } else if (notification.getTestNotification() != null) {
            this.handleTestNotification(notification.getTestNotification());
            notificationType = "testNotification";
        } else {
            log.warn("未知通知类型: {}", decodedMessage);
        }

        // 保存通知记录到数据库
        googleNotification = new GoogleSubscriptionNotifications();
        googleNotification.setPurchaseToken(purchaseToken);
        googleNotification.setNotificationType(notificationType);
        googleNotification.setReceivedAt(LocalDateTime.now().toEpochSecond(ZoneOffset.UTC));
        googleNotification.setMessageId(messageId);
        googleNotification.setRawData(decodedMessage);
        googleSubscriptionNotificationsService.save(googleNotification);
    }

    private void processNotification(Object notification) throws Exception {
        String loginName = null;
        if (notification instanceof SubscriptionNotification) {
            loginName = handleSubscriptionNotification((SubscriptionNotification) notification);
        } else if (notification instanceof OneTimeProductNotification) {
            loginName = handleOneTimeProductNotification((OneTimeProductNotification) notification);
        } else if (notification instanceof VoidedPurchaseNotification) {
            handleVoidedPurchaseNotification((VoidedPurchaseNotification) notification);
        } else {
            log.warn("这是未知通知类型: {}", notification);
        }

        if (loginName != null) {
            vipService.resettingPersonalLumens(loginName);
            User user = userApplication.getByLoginName(loginName);
            if (user != null) {
                subscriptionCurrentService.clearUserCache(user.getId());
                updateUserVipStatus(user.getId());
            }
        }
    }


    private String getPurchaseToken(Object notification) {
        if (notification instanceof SubscriptionNotification) {
            return ((SubscriptionNotification) notification).getPurchaseToken();
        } else if (notification instanceof OneTimeProductNotification) {
            return ((OneTimeProductNotification) notification).getPurchaseToken();
        } else if (notification instanceof VoidedPurchaseNotification) {
            return ((VoidedPurchaseNotification) notification).getPurchaseToken();
        }
        return null;
    }

    @Override
    public GoogleValidateResponse verifyInApp(GooglePayVerifyRequest request, User userPO) {
        // 渠道
//        String clientType = httpServletRequestHelper.getClientType();
        log.info("Google API Verify 校验订单：{} email={}, userId={}", request, userPO.getLoginName(), userPO.getId());
        String publicKey = googlePayConfiguration.getPublicKey();
        log.info("包名：{}，公钥：{} 准备开始本地RSA校验", request.getPackageName(), publicKey);
//        if (!doCheck(request.getSignature(), request.getSignature(), publicKey)) {
//            log.info("服务端RSA校验未通过，参数：{}", request);
//            throw new PayGoogleException(GoogleErrorCode.VALIDATE_RECEIPT_ERROR);
//        }
        GoogleValidateResponse googleValidateResponse = new GoogleValidateResponse(true);

        // 检查 订单是否存在库里
        this.doHandleVerify(userPO, request);

        return googleValidateResponse;
    }

    public void doHandleVerify(User user, GooglePayVerifyRequest signtureData) {
        String productType = googleProductService.findTypeByProductId(signtureData.getProductId());
        if (productType == null) {
            throw new PayGoogleException(GoogleErrorCode.PRODUCT_NOT_EXIST);
        }

        if ("plan".equals(productType)) {
            log.info("购买订阅商品 {} {} {}", signtureData.getProductId(), signtureData.getPurchaseToken(), user.getEmail());
            SubscriptionPurchaseV2 subscriptionPurchaseV2 = getSubscriptionPurchaseV2WithRetry(signtureData.getPurchaseToken());
            log.info("subscriptionPurchaseV2: {}", subscriptionPurchaseV2);
            if (subscriptionPurchaseV2 == null) {
                log.info("Google API Verify 校验订单：{} 失败", signtureData.getOrderId());
                throw new PayGoogleException(GoogleErrorCode.NOT_FOUND_SUBSCRIPTION_INFO);
            }
            String linkedPurchaseToken = subscriptionPurchaseV2.getLinkedPurchaseToken();
            SubscriptionPurchaseV2 linkedPurchaseSubscription = null;
            if (linkedPurchaseToken != null && !linkedPurchaseToken.equals(signtureData.getPurchaseToken())) {
                log.info("Verify 旧订阅: {} 新订阅: {}", linkedPurchaseToken, signtureData.getPurchaseToken());
                linkedPurchaseSubscription = getSubscriptionPurchaseV2WithRetry(linkedPurchaseToken);
            }
            log.info("旧订阅: {}", linkedPurchaseToken);

            GoogleSubscriptions googleSubscriptions = buildSubscription(subscriptionPurchaseV2, signtureData.getPurchaseToken());
            RLock lock = redissonClient.getLock(GOOGLE_PAY_VERIFY_LOCK + googleSubscriptions.getUserId());
            log.info("googleSubscriptions lock key: {}", GOOGLE_PAY_VERIFY_LOCK + googleSubscriptions.getUserId());
            try {
                lock.lock();
                applicationContext.getBean(GooglePayServiceImpl.class).handleSubscriptionPurchased(googleSubscriptions, subscriptionPurchaseV2,
                        signtureData.getProductId(),
                        signtureData.getPurchaseToken(),
                        linkedPurchaseSubscription);
                subscriptionCurrentService.clearUserCache(googleSubscriptions.getUserId());
                vipService.resettingPersonalLumens(user.getLoginName());
            } finally {
                if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }

        } else {
            log.info("Verify 购买一次性商品 {} {} {}", signtureData.getProductId(), signtureData.getPurchaseToken(), user.getEmail());
            ProductPurchase productPurchase = getOneTimePurchaseWithRetry(signtureData.getPurchaseToken(), signtureData.getProductId());
            if (productPurchase == null) {
                log.info("Google API 校验订单：{} 失败", signtureData.getOrderId());
                throw new PayGoogleException(NOT_FOUND_PRODUCT_INFO);
            }
            GoogleOneTimePurchases googleOneTimePurchases = convertToGoogleOneTimePurchases(productPurchase, signtureData.getProductId(), signtureData.getPurchaseToken());
            RLock lock = redissonClient.getLock(GOOGLE_PAY_VERIFY_LOCK + googleOneTimePurchases.getUserId());
            try {
                lock.lock();
                applicationContext.getBean(GooglePayServiceImpl.class).doHandleOneTime(googleOneTimePurchases);
            } finally {
                vipService.resettingPersonalLumens(user.getLoginName());
                if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        }
    }


    private boolean doCheck(String content, String sign, String publicKey) {
        try {
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            byte[] encodedKey = Base64.getDecoder().decode(publicKey);
            PublicKey pubKey = keyFactory
                    .generatePublic(new X509EncodedKeySpec(encodedKey));
            Signature signature = Signature.getInstance("SHA1WithRSA");
            signature.initVerify(pubKey);
            signature.update(content.getBytes(StandardCharsets.UTF_8));
            return signature.verify(Base64.getDecoder().decode(sign));
        } catch (Exception e) {
            log.error("Google pay RSA 校验异常：", e);
        }
        return false;
    }

    private static final String BASE_64_PUBLIC_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA1G48VK4yYyNNVAHKvqilxih0RxKMsO7tLrymHJF2RYR4eZbrP26WPGk+AAF8DKWgKYwzfso4ak3iLc1ACuejW3EtFRY1/i47yrSDCa+V/bZ4dPC7bhaA4IORBX/hOcDvgPJQHR7X6OnDqsjNV+16wzSeEGva4QV817x6HjheAnNAmt+7Q4HPlH3iVZYM6zshjfz+VJGTKP2gRaQJxSZxwSm3+v266smsYcNPWznoiz8KnNlsdKmnVLvDn3+KGKpTfQvORR0BHAQWmgkK3sTumRZlvb2il13ZaLJfUYJlZpBfu3Ihj1BpW492KtFZvUG54bvFc9ezHB2PNs9Fh/nrnwIDAQAB";

    public static boolean verifyPurchase(String signedData, String signature) {
        if (signedData == null || signature == null) {
            return false;
        }

        try {
            PublicKey key = generatePublicKey(BASE_64_PUBLIC_KEY);
            return verify(key, signedData, signature);
        } catch (Exception e) {
            return false;
        }
    }

    private static PublicKey generatePublicKey(String encodedPublicKey) throws Exception {
        byte[] decodedKey = Base64.getDecoder().decode(encodedPublicKey);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        return keyFactory.generatePublic(new X509EncodedKeySpec(decodedKey));
    }

    private static boolean verify(PublicKey publicKey, String signedData, String signature) throws Exception {
        Signature sig = Signature.getInstance("SHA1withRSA");
        sig.initVerify(publicKey);
        sig.update(signedData.getBytes());
        return sig.verify(Base64.getDecoder().decode(signature));
    }

    public static void main(String[] args) {
        String signedData = "YOUR_SIGNED_DATA_HERE";
        String signature = "YOUR_SIGNATURE_HERE";

        boolean isValid = verifyPurchase(signedData, signature);
        System.out.println("Signature is valid: " + isValid);
    }


    private String handleSubscriptionNotification(SubscriptionNotification subscriptionNotification) {
        String purchaseToken = subscriptionNotification.getPurchaseToken();

        int eventType = subscriptionNotification.getNotificationType();
        log.info("🔄 订阅通知: notificationType={}, purchaseToken={}, subscriptionId={}, version={}",
                eventType,
                purchaseToken,
                subscriptionNotification.getSubscriptionId(),
                subscriptionNotification.getVersion());

        SubscriptionPurchaseV2 subscriptionPurchaseV2 = getSubscriptionPurchaseV2WithRetry(purchaseToken);
        log.info("subscriptionPurchaseV2 handle: {}", subscriptionPurchaseV2);
        if (subscriptionPurchaseV2 == null) {
            log.error("Failed to retrieve subscription purchase information after {} attempts.", MAX_RETRIES);
            throw new RuntimeException("Failed to retrieve subscription purchase information.");
        }
        String linkedPurchaseToken = subscriptionPurchaseV2.getLinkedPurchaseToken();
        SubscriptionPurchaseV2 linkedPurchaseSubscription = null;
        if (linkedPurchaseToken != null && !linkedPurchaseToken.equals(purchaseToken)) {
            log.info("旧订阅: {} 新订阅: {}", linkedPurchaseToken, purchaseToken);
            linkedPurchaseSubscription = getSubscriptionPurchaseV2WithRetry(linkedPurchaseToken);
            log.info("linkedPurchaseSubscription: {}", linkedPurchaseSubscription);
        }
        log.info("旧订阅 : {}", linkedPurchaseToken);

        GoogleSubscriptions googleSubscriptions = buildSubscription(subscriptionPurchaseV2, purchaseToken);

        RLock lock = redissonClient.getLock(GOOGLE_PAY_VERIFY_LOCK + googleSubscriptions.getUserId());
        log.info("googleSubscriptions lock key: {}", GOOGLE_PAY_VERIFY_LOCK + googleSubscriptions.getUserId());
        try {
            lock.lock();
            return applicationContext.getBean(GooglePayServiceImpl.class)
                    .doHandle(subscriptionNotification, subscriptionPurchaseV2, linkedPurchaseSubscription, googleSubscriptions);
        } finally {
            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }

        }
//        return doHandleBySubscriptionState(subscriptionNotification, subscriptionPurchaseV2, linkedPurchaseSubscription);
    }

    private void updateUserVipStatus(Long userId) {
        SubscriptionCurrent subscription = subscriptionCurrentService.getLogicValidHighSubscriptionsFromDb(userId);
        log.info("更新用户VIP等级2 start: loginName={} newPlanLevel={} priceInterval={}", subscription.getLoginName(), subscription.getPlanLevel(), subscription.getPriceInterval());
        userApplication.updateUserVipInfo(subscription, userId);
        log.info("更新用户VIP等级2 end: loginName={}, newPlanLevel={} priceInterval={}", subscription.getLoginName(), subscription.getPlanLevel(), subscription.getPriceInterval());
    }
//    private String doHandleBySubscriptionState(SubscriptionNotification subscriptionNotification,
//                                           SubscriptionPurchaseV2 subscriptionPurchaseV2,
//                                           SubscriptionPurchaseV2 linkedPurchaseSubscription) {
//        int eventType = subscriptionNotification.getNotificationType();
//        SubscriptionNotificationType notificationType = SubscriptionNotificationType.fromInt(eventType);
//        String purchaseToken = subscriptionNotification.getPurchaseToken();
//        String subscriptionState = subscriptionPurchaseV2.getSubscriptionState();
//        SubscriptionState subState = SubscriptionState.valueOf(subscriptionState);
//        switch (subState) {
//            case SUBSCRIPTION_STATE_ACTIVE:
//                log.info("🔄 订阅已激活: {} {}", purchaseToken, subscriptionPurchaseV2.getLatestOrderId());
//                break;
//            case SUBSCRIPTION_STATE_PENDING:
//                log.info("🔄 订阅待处理: {} {}", purchaseToken, subscriptionPurchaseV2.getLatestOrderId());
//                break;
//            case SUBSCRIPTION_STATE_PAUSED:
//                log.info("🔄 订阅已暂停: {} {}", purchaseToken, subscriptionPurchaseV2.getLatestOrderId());
//                handleSubscriptionPaused(subscriptionPurchaseV2, subscriptionNotification.getSubscriptionId(), purchaseToken);
//                break;
//            case SUBSCRIPTION_STATE_IN_GRACE_PERIOD:
//                log.info("🔄 订阅已进入宽限期: {} {}", purchaseToken, subscriptionPurchaseV2.getLatestOrderId());
//                handleSubscriptionInGracePeriod(subscriptionPurchaseV2, subscriptionNotification.getSubscriptionId(), purchaseToken);
//                break;
//            case SUBSCRIPTION_STATE_ON_HOLD:
//                log.info("🔄 订阅已进入账号保留状态: {} {}", purchaseToken, subscriptionPurchaseV2.getLatestOrderId());
//                handleSubscriptionOnHold(subscriptionPurchaseV2, subscriptionNotification.getSubscriptionId(), purchaseToken);
//                break;
//            case SUBSCRIPTION_STATE_CANCELED:
//                log.info("🔄 订阅已取消: {} {}", purchaseToken, subscriptionPurchaseV2.getLatestOrderId());
//                handleSubscriptionCancel(subscriptionPurchaseV2, subscriptionNotification.getSubscriptionId(), purchaseToken);
//                break;
//            case SUBSCRIPTION_STATE_EXPIRED:
//                log.info("🔄 订阅已过期: {} {}", purchaseToken, subscriptionPurchaseV2.getLatestOrderId());
//                handleSubscriptionExpired(subscriptionPurchaseV2, subscriptionNotification.getSubscriptionId(), purchaseToken);
//                break;
//            case SUBSCRIPTION_STATE_PENDING_PURCHASE_CANCELED:
//                log.info("🔄 订阅的待处理交易已取消: {} {}", purchaseToken, subscriptionPurchaseV2.getLatestOrderId());
//                handleSubscriptionPendingPurchaseCanceled(subscriptionPurchaseV2, subscriptionNotification.getSubscriptionId(), purchaseToken);
//                break;
//            default:
//                log.info("🔄 订阅状态: {}", subState.getValue());
//                break;
//        }
//
//        return null;
//    }

    @Transactional(rollbackFor = Exception.class)
    public String doHandle(SubscriptionNotification subscriptionNotification, SubscriptionPurchaseV2 subscriptionPurchaseV2, SubscriptionPurchaseV2 linkedPurchaseSubscription
            , GoogleSubscriptions googleSubscriptions) {
        int eventType = subscriptionNotification.getNotificationType();
        SubscriptionNotificationType notificationType = SubscriptionNotificationType.fromInt(eventType);
        String purchaseToken = subscriptionNotification.getPurchaseToken();
        // 根据不同的通知类型进行处理
        String loginName = null;
        switch (notificationType) {
            case SUBSCRIPTION_PURCHASED:
                log.info("🔄 订阅已购买: {} {}", googleSubscriptions.getUserId(), subscriptionPurchaseV2.getLatestOrderId());
                // 处理订阅 purchase
                loginName = handleSubscriptionPurchased(googleSubscriptions, subscriptionPurchaseV2, subscriptionNotification.getSubscriptionId(), purchaseToken, linkedPurchaseSubscription);
                break;
            case SUBSCRIPTION_RENEWED:
                log.info("🔄 订阅已续费 : {} {}", googleSubscriptions.getUserId(), subscriptionPurchaseV2.getLatestOrderId());
                // 处理订阅续费
                loginName = handleSubscriptionRenewed(googleSubscriptions, subscriptionPurchaseV2, subscriptionNotification.getSubscriptionId(), purchaseToken);
                break;
            case SUBSCRIPTION_IN_GRACE_PERIOD:
                log.info("🔄 订阅已进入宽限期（如果已启用）: {} {}", googleSubscriptions.getUserId(), subscriptionPurchaseV2.getLatestOrderId());
                // 处理订阅替换
                loginName = handleSubscriptionInGracePeriod(googleSubscriptions, subscriptionPurchaseV2, subscriptionNotification.getSubscriptionId(), purchaseToken);
            case SUBSCRIPTION_REVOKED:
                log.info("⚠️ 订阅被撤销: {} {}", googleSubscriptions.getUserId(), subscriptionPurchaseV2.getLatestOrderId());
                loginName = handleSubscriptionExpired(googleSubscriptions, subscriptionPurchaseV2, subscriptionNotification.getSubscriptionId(), purchaseToken);
                break;
            case SUBSCRIPTION_ON_HOLD:
                log.info("⏰ 订阅已进入账号保留状态（如果已启用）: {} {}", googleSubscriptions.getUserId(), subscriptionPurchaseV2.getLatestOrderId());
                loginName = handleSubscriptionExpired(googleSubscriptions, subscriptionPurchaseV2, subscriptionNotification.getSubscriptionId(), purchaseToken);
                break;
            case SUBSCRIPTION_CANCELED:
                log.info("⚠️ 订阅被取消: {} {}", googleSubscriptions.getUserId(), subscriptionPurchaseV2.getLatestOrderId());
                loginName = handleSubscriptionCancel(googleSubscriptions, subscriptionPurchaseV2, subscriptionNotification.getSubscriptionId(), purchaseToken);
                break;
            case SUBSCRIPTION_EXPIRED:
                log.info("⛔ 订阅已过期: {} {}", googleSubscriptions.getUserId(), subscriptionPurchaseV2.getLatestOrderId());
                loginName = handleSubscriptionExpired(googleSubscriptions, subscriptionPurchaseV2, subscriptionNotification.getSubscriptionId(), purchaseToken);
                break;
            case SUBSCRIPTION_RECOVERED:
                log.info("💰 恢复了订阅: {}", googleSubscriptions.getUserId());
                loginName = handleSubscriptionRecovered(googleSubscriptions, subscriptionPurchaseV2, subscriptionNotification.getSubscriptionId(), purchaseToken);
                break;
            case SUBSCRIPTION_DEFERRED:
                log.info("💰 延迟了订阅: {} 「」 {}", googleSubscriptions.getUserId(), subscriptionPurchaseV2.toString());
//                loginName = handleSubscriptionDeferred(subscriptionPurchaseV2, subscriptionNotification.getSubscriptionId(), purchaseToken);
                break;
            case SUBSCRIPTION_RESTARTED:
                log.info("💰 重新启动了订阅: {}", purchaseToken);
                loginName = handleSubscriptionRestarted(googleSubscriptions, subscriptionPurchaseV2, subscriptionNotification.getSubscriptionId(), purchaseToken);
                break;
            default:
                log.info("未知事件类型: {} ({})", eventType, notificationType.getDescription());
                break;
        }
        subscriptionCurrentService.clearUserCache(googleSubscriptions.getUserId());
        return loginName;
    }

    private String handleSubscriptionRestarted(GoogleSubscriptions googleSubscriptions, SubscriptionPurchaseV2 subscriptionPurchaseV2, String subscriptionId, String purchaseToken) {
        googleSubscriptionsService.updateOrSaveByLatestOrderId(googleSubscriptions);

        if (!SubscriptionState.SUBSCRIPTION_STATE_ACTIVE.getValue().equals(subscriptionPurchaseV2.getSubscriptionState())) {
            log.warn("订阅状态不是 ACTIVE，不处理 {} {}", googleSubscriptions.getLatestOrderId(), googleSubscriptions.getSubscriptionState());
            throw new RuntimeException("订阅状态不是 ACTIVE，不处理");
        }
        GoogleProduct googleProduct = googleProductService.findByPlanId(googleSubscriptions.getPlanId());
        boolean success = googleSubscriptionHistoryService.saveSubscriptionHistoryAndLumen(googleSubscriptions, googleProduct, subscriptionPurchaseV2.toString(), SUBSCRIPTION_RESTARTED.getType());
        if (success) {
            googleSubscriptionsService.updateOrSaveByLatestOrderId(googleSubscriptions);
            subscriptionCurrentService.saveOrUpdateGoogleSubscriptionCurrent(buildGoogleSubscriptionCurrent(googleSubscriptions, googleProduct));
        }
        return googleSubscriptions.getLoginName();
    }

    private String handleSubscriptionCancel(GoogleSubscriptions googleSubscriptions, SubscriptionPurchaseV2 subscriptionPurchaseV2, String subscriptionId, String purchaseToken) {
        log.info("💰 取消了订阅: {} {} {}", purchaseToken, subscriptionPurchaseV2.getLatestOrderId(), subscriptionPurchaseV2.getSubscriptionState());
        long now = System.currentTimeMillis();
        googleSubscriptionsService.updateOrSaveByLatestOrderId(googleSubscriptions);
        this.handleSubscriptionExpired(googleSubscriptions, subscriptionPurchaseV2, subscriptionId, purchaseToken);
        return googleSubscriptions.getLoginName();
    }


    private String handleSubscriptionRecovered(GoogleSubscriptions googleSubscriptions, SubscriptionPurchaseV2 subscriptionPurchaseV2, String subscriptionId, String purchaseToken) {
        log.info("💰 恢复了订阅Recovered: {} {} {}", purchaseToken, subscriptionPurchaseV2.getLatestOrderId(), subscriptionPurchaseV2.getSubscriptionState());
        SubscriptionState subscriptionState = SubscriptionState.fromValue(subscriptionPurchaseV2.getSubscriptionState());
        if (!subscriptionState.equals(SubscriptionState.SUBSCRIPTION_STATE_ACTIVE)) {
            log.warn("订阅状态不是 ACTIVE，不处理 {}", purchaseToken);
            throw new RuntimeException("订阅状态不是 ACTIVE，不处理");
        }
        GoogleProduct googleProduct = googleProductService.findByPlanId(googleSubscriptions.getPlanId());
        boolean success = googleSubscriptionHistoryService.saveSubscriptionHistoryAndLumen(googleSubscriptions, googleProduct, subscriptionPurchaseV2.toString(), SUBSCRIPTION_RENEWED.getType());
        if (success) {
            googleSubscriptionsService.updateOrSaveByLatestOrderId(googleSubscriptions);
            subscriptionCurrentService.saveOrUpdateGoogleSubscriptionCurrent(buildGoogleSubscriptionCurrent(googleSubscriptions, googleProduct));
        }
        return googleSubscriptions.getLoginName();
    }

    private String handleSubscriptionExpired(GoogleSubscriptions googleSubscriptions, SubscriptionPurchaseV2 subscriptionPurchaseV2, String subscriptionId, String purchaseToken) {
        log.info("失效订阅: {} {} {}", purchaseToken, subscriptionPurchaseV2.getLatestOrderId(), subscriptionPurchaseV2.getSubscriptionState());
        googleSubscriptionsService.updateOrSaveByLatestOrderId(googleSubscriptions);
        googleSubscriptionHistoryService.saveSubscriptionHistory(googleSubscriptions, subscriptionPurchaseV2.toString(), SUBSCRIPTION_EXPIRED.getType());
        subscriptionCurrentService.updateExpiryTime(googleSubscriptions);
        // 撤销lumen
        GoogleSubscriptionHistory history = googleSubscriptionHistoryService.queryByLatestOrderId(googleSubscriptions.getLatestOrderId(), googleSubscriptions.getPurchaseToken());
        if (googleSubscriptions.getExpiryTime() < System.currentTimeMillis() / 1000) {
            payLumenRecordService.cancelLumen(history);
        } else {
            payLumenRecordService.updateExistRecordExpiretime(googleSubscriptions.getLatestOrderId(), googleSubscriptions.getExpiryTime());
        }
        return googleSubscriptions.getLoginName();
    }

    private String handleSubscriptionInGracePeriod(GoogleSubscriptions googleSubscriptions, SubscriptionPurchaseV2 subscriptionPurchaseV2, String subscriptionId, String purchaseToken) {
        log.info("⏰ 订阅宽限期: {} {} {}", purchaseToken, subscriptionPurchaseV2.getLatestOrderId(), subscriptionPurchaseV2.getSubscriptionState());
        if (SUBSCRIPTION_STATE_IN_GRACE_PERIOD.getValue().equalsIgnoreCase(subscriptionPurchaseV2.getSubscriptionState())) {
            googleSubscriptionHistoryService.saveSubscriptionHistory(googleSubscriptions, subscriptionPurchaseV2.toString(), SUBSCRIPTION_IN_GRACE_PERIOD.getType());
            // 更新订阅到期时间
            googleSubscriptionsService.updateExpiryTimeAndState(googleSubscriptions);
            // 更新订阅到期时间
            subscriptionCurrentService.updateExpiryTime(googleSubscriptions);
            // 撤销lumen
            return googleSubscriptions.getLoginName();
        } else {
            throw new RuntimeException("SubscriptionState 状态不是 SUBSCRIPTION_STATE_IN_GRACE_PERIOD，不处理");
        }
    }

    /**
     * SUBSCRIPTION_RENEWED - 续订了处于活动状态的订阅
     *
     * @param googleSubscriptions
     * @param subscriptionPurchaseV2
     * @param subscriptionId
     * @param purchaseToken
     * @return
     */
    private String handleSubscriptionRenewed(GoogleSubscriptions googleSubscriptions, SubscriptionPurchaseV2 subscriptionPurchaseV2, String subscriptionId, String purchaseToken) {
        log.info("🔄 订阅已续费: {} {}", googleSubscriptions.getLatestOrderId(), googleSubscriptions.getExpiryTime());
        String productId = googleSubscriptions.getProductId();
        SubscriptionState subscriptionState = SubscriptionState.fromValue(subscriptionPurchaseV2.getSubscriptionState());
        if (SUBSCRIPTION_STATE_EXPIRED.getValue().equalsIgnoreCase(subscriptionState.getValue())) {
            log.warn("订阅状态是 EXPIRED，{}", googleSubscriptions.getLatestOrderId());
            return this.handleSubscriptionExpired(googleSubscriptions, subscriptionPurchaseV2, subscriptionId, purchaseToken);
        }
        if (!subscriptionState.equals(SubscriptionState.SUBSCRIPTION_STATE_ACTIVE)) {
            log.warn("订阅状态不是 ACTIVE，不处理 {} {}", googleSubscriptions.getLatestOrderId(), subscriptionPurchaseV2.getSubscriptionState());
            throw new RuntimeException("订阅状态不是 ACTIVE，不处理");
        }
        GoogleProduct googleProduct = googleProductService.findByPlanId(googleSubscriptions.getPlanId());
        if (googleProduct == null) {
            log.error("GoogleProduct is null, planId: {}", googleSubscriptions.getPlanId());
            throw new RuntimeException("GoogleProduct is null, planId: " + googleSubscriptions.getPlanId());
        }
        boolean success = googleSubscriptionHistoryService.saveSubscriptionHistoryAndLumen(googleSubscriptions, googleProduct, subscriptionPurchaseV2.toString(), SUBSCRIPTION_RENEWED.getType());
        if (success) {
            googleSubscriptionsService.updateOrSaveByLatestOrderId(googleSubscriptions);
            subscriptionCurrentService.saveOrUpdateGoogleSubscriptionCurrent(buildGoogleSubscriptionCurrent(googleSubscriptions, googleProduct));
        }
        return googleSubscriptions.getLoginName();
    }

    /**
     * SUBSCRIPTION_PURCHASED - 购买了新的订阅。
     *
     * @param subscriptionPurchaseV2
     * @param purchaseToken
     * @param linkedPurchaseSubscription
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String handleSubscriptionPurchased(GoogleSubscriptions googleSubscriptions, SubscriptionPurchaseV2 subscriptionPurchaseV2,
                                              String subscriptionId,
                                              String purchaseToken,
                                              SubscriptionPurchaseV2 linkedPurchaseSubscription) {
        String acknowledgementStateValue = subscriptionPurchaseV2.getAcknowledgementState();
        AcknowledgementState acknowledgementState = AcknowledgementState.fromValue(acknowledgementStateValue);
        log.info("确认状态: {} {}", acknowledgementState.getDescription(), subscriptionPurchaseV2.getLatestOrderId());
        String subscriptionStateValue = subscriptionPurchaseV2.getSubscriptionState();
        SubscriptionState subscriptionState = SubscriptionState.fromValue(subscriptionStateValue);
        log.info("订阅状态: {} {}", subscriptionState, subscriptionPurchaseV2.getLatestOrderId());
//        if (SUBSCRIPTION_STATE_EXPIRED.getValue().equalsIgnoreCase(subscriptionState.getValue())) {
//            log.warn("订阅状态不是 EXPIRED，不处理 {}", purchaseToken);
//            return null;
//        }
//        if (!subscriptionState.equals(SubscriptionState.SUBSCRIPTION_STATE_ACTIVE)) {
//            log.warn("订阅状态不是 ACTIVE，不处理 {}", purchaseToken);
//            throw new PayGoogleException(SUB_EXPIRED);
//        }

        switch (acknowledgementState) {
            case ACKNOWLEDGEMENT_STATE_UNSPECIFIED:
                throw new RuntimeException("确认状态未指定");
            case ACKNOWLEDGEMENT_STATE_PENDING:
                try {
                    log.info("⏰ 确认订阅中: {}", subscriptionPurchaseV2.getLatestOrderId());
                    AndroidPublisher.Purchases.Subscriptions.Acknowledge acknowledge = androidPublisher.purchases().subscriptions()
                            .acknowledge(googlePayConfiguration.getPackageName(), subscriptionId, purchaseToken, new SubscriptionPurchasesAcknowledgeRequest());
                    acknowledge.execute();
                    log.info("✅ 确认订阅成功: {}", subscriptionPurchaseV2.getLatestOrderId());
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
                break;
            case ACKNOWLEDGEMENT_STATE_ACKNOWLEDGED:
                // 处理已确认的订阅
                break;
            default:
                log.warn("未知确认状态: {}", acknowledgementStateValue);
                break;
        }

//        GoogleSubscriptions one = googleSubscriptionsService.lambdaQuery()
//                .eq(GoogleSubscriptions::getPurchaseToken, purchaseToken)
//                .eq(GoogleSubscriptions::getLatestOrderId, googleSubscriptions.getLatestOrderId())
//                .one();
        GoogleSubscriptions one = googleSubscriptionsService.queryByLatestOrderId(googleSubscriptions.getLatestOrderId());
        if (one != null) {
            log.info("✅ 订阅已确认: {}", purchaseToken);
            return null;
        }

        if (googleSubscriptions.getDeferredSubscriptions() != null) {
//            googleSubscriptionsService.updateOrSaveByPurcahseToken(googleSubscriptions);
            googleDeferredSubscriptionsService.saveOrUpdate(googleSubscriptions.getDeferredSubscriptions());
            log.info("✅ 确认延迟订阅购买成功: {}", purchaseToken);
            return googleSubscriptions.getLoginName();
        }

        googleSubscriptionsService.updateOrSaveByLatestOrderId(googleSubscriptions);
        GoogleProduct googleProduct = googleProductService.findByPlanId(googleSubscriptions.getPlanId());
        boolean successSave = googleSubscriptionHistoryService.saveSubscriptionHistoryAndLumen(googleSubscriptions, googleProduct, subscriptionPurchaseV2.toString(), SUBSCRIPTION_PURCHASED.getType());
        if (successSave) {
            subscriptionCurrentService.saveOrUpdateGoogleSubscriptionCurrent(buildGoogleSubscriptionCurrent(googleSubscriptions, googleProduct));
        }

        if (linkedPurchaseSubscription != null) {
            // 如果过期 撤回相应的lumen 和会员
            GoogleSubscriptions linkGoogleSubscription = buildSubscription(linkedPurchaseSubscription, subscriptionPurchaseV2.getLinkedPurchaseToken());
            subscriptionCurrentService.updateExpiryTime(linkGoogleSubscription);
            // 撤销lumen
            GoogleSubscriptionHistory history = googleSubscriptionHistoryService.queryByLatestOrderId(linkGoogleSubscription.getLatestOrderId(), linkGoogleSubscription.getPurchaseToken());
            payLumenRecordService.cancelLumen(history);
        }
        return googleSubscriptions.getLoginName();
    }

    protected static SubscriptionCurrent buildGoogleSubscriptionCurrent(GoogleSubscriptions payload, GoogleProduct product) {
        // 改成setter
        SubscriptionCurrent build = new SubscriptionCurrent();
        build.setCreateTime(LocalDateTime.now());
        build.setUserId(payload.getUserId());
        build.setLoginName(payload.getLoginName());
        build.setPurchaseToken(payload.getPurchaseToken());
        build.setLatestOrderId(payload.getLatestOrderId());
        build.setVipPlatform(VipPlatform.GOOGLE.getPlatformName());
        long now = System.currentTimeMillis();
        build.setCurrentPeriodStart(payload.getStartTime());
        build.setCurrentPeriodEnd(payload.getExpiryTime());
        build.setVipBeginTime(payload.getStartTime());
        build.setVipEndTime(payload.getExpiryTime());
        build.setPlanLevel(product.getPlanLevel());
        build.setPriceInterval(product.getPriceInterval());
        build.setAutoRenewStatus(1);
        build.setInvalid(false);
        return build;
    }

    private GoogleSubscriptions buildSubscription(SubscriptionPurchaseV2 subscriptionPurchaseV2, String purchaseToken) {
        ExternalAccountIdentifiers externalAccountIdentifiers = subscriptionPurchaseV2.getExternalAccountIdentifiers();
        if (externalAccountIdentifiers == null) {
            throw new PayGoogleException(SUB_EXTERNAL_ACCOUNT_IS_NULL);
        }
        String obfuscatedExternalAccountId = externalAccountIdentifiers.getObfuscatedExternalAccountId();
        GoogleSubscriptions subscription = new GoogleSubscriptions();
        if (obfuscatedExternalAccountId != null) {
//            obfuscatedExternalAccountId = "15390";
            User userById = userApplication.getUserById(Long.parseLong(obfuscatedExternalAccountId));
            if (userById != null) {
                subscription.setUserId(userById.getId());
                subscription.setLoginName(userById.getEmail());
            } else {
                throw new PayGoogleException(SUB_EXTERNAL_ACCOUNT_IS_NULL);
            }
        } else {
            throw new PayGoogleException(SUB_EXTERNAL_ACCOUNT_IS_NULL);
        }

        subscription.setPurchaseToken(purchaseToken);
        subscription.setLinkedPurchaseToken(subscriptionPurchaseV2.getLinkedPurchaseToken());
        subscription.setStartTime(convertToUtcTimestamp(subscriptionPurchaseV2.getStartTime()));

        List<SubscriptionPurchaseLineItem> lineItems = subscriptionPurchaseV2.getLineItems();

        SubscriptionPurchaseLineItem subscriptionPurchaseLineItem = null;
        GoogleDeferredSubscriptions deferredSubscriptions = null;
        if (lineItems.size() > 1) {
            for (SubscriptionPurchaseLineItem lineItem : lineItems) {
                DeferredItemReplacement deferredItemReplacement = lineItem.getDeferredItemReplacement();
                if (deferredItemReplacement != null) {
                    subscriptionPurchaseLineItem = lineItem;
                } else {
                    deferredSubscriptions = buildDeferredSubscriptions(lineItem, subscriptionPurchaseV2);
                    deferredSubscriptions.setLoginName(subscription.getLoginName());
                    deferredSubscriptions.setUserId(subscription.getId());
                    deferredSubscriptions.setPurchaseToken(purchaseToken);
                    subscription.setDeferredSubscriptions(deferredSubscriptions);
                }
            }
        } else {
            subscriptionPurchaseLineItem = lineItems.get(0);
        }
        if (subscriptionPurchaseLineItem == null) {
            log.info("❌ 订阅信息有误，");
            throw new RuntimeException("❌ 订阅信息有误，");
        }
        OfferDetails offerDetails = subscriptionPurchaseLineItem.getOfferDetails();
        if (offerDetails != null) {
            subscription.setPlanId(offerDetails.getBasePlanId());
        }

        subscription.setProductId(subscriptionPurchaseLineItem.getProductId());
        if (deferredSubscriptions != null) {
            deferredSubscriptions.setSrcProductId(subscriptionPurchaseLineItem.getProductId());
        }
        subscription.setExpiryTime(convertToUtcTimestamp(subscriptionPurchaseLineItem.getExpiryTime()));


        AutoRenewingPlan autoRenewingPlan = subscriptionPurchaseLineItem.getAutoRenewingPlan();
        if (autoRenewingPlan != null) {
            subscription.setAutoRenewing(autoRenewingPlan.getAutoRenewEnabled() != null && autoRenewingPlan.getAutoRenewEnabled());
            subscription.setCurrency(autoRenewingPlan.getRecurringPrice().getCurrencyCode());
            subscription.setRecurringPrice(autoRenewingPlan.getRecurringPrice().getUnits());
            subscription.setNanos(autoRenewingPlan.getRecurringPrice().getNanos());
        } else {
            subscription.setAutoRenewing(false);
        }

        subscription.setSubscriptionState(subscriptionPurchaseV2.getSubscriptionState());
        subscription.setRegionCode(subscriptionPurchaseV2.getRegionCode());
        subscription.setLatestOrderId(subscriptionPurchaseV2.getLatestOrderId());

        CanceledStateContext canceledStateContext = subscriptionPurchaseV2.getCanceledStateContext();
        if (canceledStateContext != null) {
            subscription.setCanceledStateContext(JSONUtil.toJsonStr(canceledStateContext));
        }
        return subscription;
    }

    private GoogleDeferredSubscriptions buildDeferredSubscriptions(SubscriptionPurchaseLineItem lineItem, SubscriptionPurchaseV2 subscriptionPurchaseV2) {
        GoogleDeferredSubscriptions deferredSubscriptions = new GoogleDeferredSubscriptions();
        deferredSubscriptions.setDeferredProductId(lineItem.getProductId());
        deferredSubscriptions.setStartTime(convertToUtcTimestamp(subscriptionPurchaseV2.getStartTime()));
        deferredSubscriptions.setExpiryTime(convertToUtcTimestamp(lineItem.getExpiryTime()));
        AutoRenewingPlan autoRenewingPlan = lineItem.getAutoRenewingPlan();
        if (autoRenewingPlan != null) {
            deferredSubscriptions.setAutoRenewing(autoRenewingPlan.getAutoRenewEnabled());
        }
        deferredSubscriptions.setLatestOrderId(subscriptionPurchaseV2.getLatestOrderId());
        return deferredSubscriptions;
    }

    private SubscriptionPurchaseV2 getSubscriptionPurchaseV2WithRetry(String purchaseToken) {
        SubscriptionPurchaseV2 subscriptionPurchaseV2 = null;
        boolean success = false;
        int attempt = 0;
        String error = null;
        while (!success && attempt < MAX_RETRIES) {
            try {
                subscriptionPurchaseV2 = androidPublisher.purchases().subscriptionsv2()
                        .get(googlePayConfiguration.getPackageName(), purchaseToken)
                        .execute();
                success = true;
            } catch (Exception e) {
                error = Arrays.toString(e.getStackTrace());
                log.error("Unexpected exception on attempt {}: {}", attempt + 1, e.getMessage());
            }
            attempt++;
            if (!success && attempt < MAX_RETRIES) {
                log.info("Retrying in 2 seconds...");
                try {
                    Thread.sleep(2000);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    log.error("Thread interrupted while retrying: {}", ie.getMessage());
                }
            }
        }
        if (subscriptionPurchaseV2 == null) {
            String finalError = error;
            THREAD_POOL.execute(() -> {
                try {
                    log.error("Failed to retrieve subscription purchase after {} attempts.", MAX_RETRIES);
                    GoogleErrorLog googleErrorLog = new GoogleErrorLog();
                    googleErrorLog.setType("subscriptionsv2.get");
                    googleErrorLog.setError(finalError);
                    googleErrorLog.setPurchaseToken(purchaseToken);
                    googleErrorLogService.save(googleErrorLog);
                } catch (Exception ex) {
                    log.error("Failed to consume one time purchase save log: ", ex);
                }
            });
            return null;
        }
        TestPurchase testPurchase = subscriptionPurchaseV2.getTestPurchase();
        checkSubEnv(testPurchase);
        return subscriptionPurchaseV2;
    }

    public void checkSubEnv(TestPurchase testPurchase) {
        if (testPurchase != null && environmentUtils.isProdEnvironment()) {
            log.error("Test purchase detected, skipping processing sub from prod.");
            throw new PayGoogleException(GoogleErrorCode.TEST_NOT_ALLOWED_PRO);
        }
        if (testPurchase == null && !environmentUtils.isProdEnvironment()) {
            log.error("prod purchase detected, skipping processing sub from test.");
            throw new PayGoogleException(GoogleErrorCode.TEST_NOT_ALLOWED_PRO);
        }
    }

    private String handleOneTimeProductNotification(OneTimeProductNotification oneTimeProductNotification) {
        String purchaseToken = oneTimeProductNotification.getPurchaseToken();
        int notificationType = oneTimeProductNotification.getNotificationType();
        OneTimeNotificationType oneTimeNotificationType = OneTimeNotificationType.fromInt(notificationType);
        log.info("📢 一次性商品购买通知: notificationType={}, purchaseToken={}, sku={}",
                oneTimeNotificationType,
                purchaseToken,
                oneTimeProductNotification.getSku());

        ProductPurchase productPurchase = getOneTimePurchaseWithRetry(purchaseToken, oneTimeProductNotification.getSku());
        if (productPurchase == null) {
            log.error("Failed to retrieve one time purchase after {} attempts.", MAX_RETRIES);
            return null;
        }
        GoogleOneTimePurchases googleOneTimePurchases = convertToGoogleOneTimePurchases(productPurchase, oneTimeProductNotification.getSku(), purchaseToken);
        RLock lock = redissonClient.getLock(GOOGLE_PAY_VERIFY_LOCK + googleOneTimePurchases.getUserId());
        try {
            lock.lock();
            applicationContext.getBean(GooglePayServiceImpl.class).doHandleOneTime(googleOneTimePurchases);
        } catch (Exception e) {
            throw new PayGoogleException(VALIDATE_RECEIPT_ERROR_ONE);
        } finally {
            if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
            }
        }
        return googleOneTimePurchases.getEmail();
    }


    @Transactional(rollbackFor = Exception.class)
    public void doHandleOneTime(GoogleOneTimePurchases googleOneTimePurchases) {
        GoogleOneTimePurchases googleOneTimePurchasesExist = googleOneTimePurchasesService.queryByPurchaseToken(googleOneTimePurchases.getPurchaseToken());
        if (googleOneTimePurchasesExist == null) {
            googleOneTimePurchases.setCreateTime(LocalDateTime.now());
            googleOneTimePurchasesService.save(googleOneTimePurchases);
            googleOneTimePurchasesExist = googleOneTimePurchases;
        } else {
            if (googleOneTimePurchasesExist.getPurchaseState() == 0) {
                log.info("One time purchase already exists and is already purchased. purchaseToken={}", googleOneTimePurchases.getPurchaseToken());
                return;
            }
            googleOneTimePurchases.setId(googleOneTimePurchasesExist.getId());
            googleOneTimePurchasesService.updateById(googleOneTimePurchases);
        }
        Integer purchaseState = googleOneTimePurchases.getPurchaseState();
        if (purchaseState == OneTimePurchaseState.PURCHASED.getType()) {
            if (googleOneTimePurchases.getConsumptionState().equals(ConsumptionState.NOT_CONSUMED.getType())) {
                try {
                    Thread.sleep(5000);
                    androidPublisher
                            .purchases()
                            .products()
                            .consume(googlePayConfiguration.getPackageName(), googleOneTimePurchases.getProductId(), googleOneTimePurchases.getPurchaseToken())
                            .execute();
                } catch (Exception e) {
                    ProductPurchase oneTimePurchaseWithRetry = getOneTimePurchaseWithRetry(googleOneTimePurchases.getPurchaseToken(), googleOneTimePurchases.getProductId());
                    if (oneTimePurchaseWithRetry != null && oneTimePurchaseWithRetry.getConsumptionState().equals(ConsumptionState.CONSUMED.getType())) {
                        log.info("googleOneTimePurchases.getPurchaseToken()={} already consumed.", googleOneTimePurchases.getPurchaseToken());
                    } else {
                        THREAD_POOL.execute(() -> {
                            try {
                                GoogleErrorLog googleErrorLog = new GoogleErrorLog();
                                googleErrorLog.setType("products.consume");
                                googleErrorLog.setError(e.getMessage());
                                googleErrorLog.setPurchaseToken(googleOneTimePurchases.getPurchaseToken());
                                googleErrorLog.setProductId(googleOneTimePurchases.getProductId());
                                googleErrorLog.setQty(googleOneTimePurchases.getCount());
                                googleErrorLogService.save(googleErrorLog);
                            } catch (Exception ex) {
                                log.error("Failed to consume one time purchase save log: ", ex);
                            }
                        });

                        log.error("Failed to consume one time purchase: ", e);
                        throw new RuntimeException(e);
                    }
                }
            }
            List<PayLumenRecord> existRecord = payLumenRecordService.findByLatestOrderId(googleOneTimePurchases.getOrderId());
            if (existRecord.isEmpty()) {
                // 发放lumen
                GooglePlayOrder googlePlayOrder = googlePlayOrderService.saveGooglePayOrder(googleOneTimePurchases.getUserId(), googleOneTimePurchases.getEmail(), googleOneTimePurchases.getOrderId());
                payLumenRecordService.saveOneTimeLumenRecordForGoogle(googleOneTimePurchases, googlePlayOrder);
            } else {
                log.info("One time purchase already exists and is already purchased. purchaseToken={} orderId={}", googleOneTimePurchases.getPurchaseToken(), googleOneTimePurchases.getOrderId());
            }
        } else if (purchaseState == OneTimePurchaseState.CANCELED.getType()) {
            googleOneTimePurchasesService.lambdaUpdate()
                    .eq(GoogleOneTimePurchases::getId, googleOneTimePurchasesExist.getId())
                    .set(GoogleOneTimePurchases::getPurchaseState, googleOneTimePurchases.getPurchaseState())
                    .update();
            payLumenRecordService.cancelLumenByLatestOrderId(googleOneTimePurchases.getOrderId());
        }
    }

    private GoogleOneTimePurchases convertToGoogleOneTimePurchases(ProductPurchase productPurchase, String sku, String purchaseToken) {
        GoogleOneTimePurchases googleOneTimePurchases = new GoogleOneTimePurchases();
        String obfuscatedExternalAccountId = productPurchase.getObfuscatedExternalAccountId();
        if (obfuscatedExternalAccountId != null) {
//            obfuscatedExternalAccountId = "15390";
            googleOneTimePurchases.setObfuscatedExternalAccountId(obfuscatedExternalAccountId);
            User userById = userApplication.getUserById(Long.parseLong(obfuscatedExternalAccountId));
            if (userById == null) {
                throw new PayGoogleException(SUB_EXTERNAL_ACCOUNT_IS_NULL);
            }
            googleOneTimePurchases.setUserId(userById.getId());
            googleOneTimePurchases.setEmail(userById.getEmail());
        } else {
            throw new PayGoogleException(SUB_EXTERNAL_ACCOUNT_IS_NULL);
        }
        // 补全其他字段设置
        // 设置其他字段
        googleOneTimePurchases.setKind(productPurchase.getKind());
        googleOneTimePurchases.setDeveloperPayload(productPurchase.getDeveloperPayload());
        googleOneTimePurchases.setOrderId(productPurchase.getOrderId());
        googleOneTimePurchases.setPurchaseType(productPurchase.getPurchaseType());
        googleOneTimePurchases.setAcknowledgementState(productPurchase.getAcknowledgementState());
        googleOneTimePurchases.setConsumptionState(productPurchase.getConsumptionState());
        googleOneTimePurchases.setPurchaseToken(purchaseToken);
        googleOneTimePurchases.setProductId(sku);
        googleOneTimePurchases.setCount(productPurchase.getQuantity() == null ? 1 : productPurchase.getQuantity());
        googleOneTimePurchases.setPurchaseTime(productPurchase.getPurchaseTimeMillis() / 1000);
        googleOneTimePurchases.setPurchaseState(productPurchase.getPurchaseState());
        googleOneTimePurchases.setObfuscatedExternalProfileId(productPurchase.getObfuscatedExternalProfileId());
        googleOneTimePurchases.setRegionCode(productPurchase.getRegionCode());
        googleOneTimePurchases.setRefundableQuantity(productPurchase.getRefundableQuantity());
        GoogleProduct byProductId = googleProductService.findByPlanId(sku);
        if (byProductId == null) {
            log.error("Failed to find GoogleProduct by productId: {}", sku);
            throw new PayGoogleException(NOT_FOUND_PRODUCT_INFO);
        }
        googleOneTimePurchases.setLumenQty(byProductId.getLumen());
        return googleOneTimePurchases;
    }

    /**
     * @param purchaseToken
     * @param skuId         应用内商品的 SKU（例如“com.some.thing.inapp1”）。
     * @return
     */

    private ProductPurchase getOneTimePurchaseWithRetry(String purchaseToken, String skuId) {
        ProductPurchase purchase = null;
        boolean success = false;
        int attempt = 0;
        String error = null;
        while (!success && attempt < MAX_RETRIES) {
            try {
                purchase = androidPublisher.purchases().products()
                        .get(googlePayConfiguration.getPackageName(), skuId, purchaseToken)
                        .execute();
                success = true;
            } catch (Exception e) {
                error = Arrays.toString(e.getStackTrace());
                log.error("Unexpected exception on attempt {}:", attempt + 1, e);
            }
            attempt++;
            if (!success && attempt < MAX_RETRIES) {
                log.info("Retrying in 2 seconds...");
                try {
                    Thread.sleep(2000);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    log.error("Thread interrupted while retrying: ", ie);
                }
            }
        }
        if (purchase == null) {
            String finalError = error;
            THREAD_POOL.execute(() -> {
                try {
                    log.error("Failed to retrieve one time purchase after {} attempts.", MAX_RETRIES);
                    GoogleErrorLog googleErrorLog = new GoogleErrorLog();
                    googleErrorLog.setType("purchase.get");
                    googleErrorLog.setError(finalError);
                    googleErrorLog.setPurchaseToken(purchaseToken);
                    googleErrorLogService.save(googleErrorLog);
                } catch (Exception ex) {
                    log.error("Failed to consume one time purchase save log: ", ex);
                }
            });

            return null;
        }
        checkOneTimeEnv(purchase.getPurchaseType());
        return purchase;
    }

    public void checkOneTimeEnv(Integer purchaseType) {
        if ((purchaseType != null && purchaseType == 0 && environmentUtils.isProdEnvironment())) {
            log.error("Test purchase detected, skipping processing one time from prod.");
            throw new PayGoogleException(GoogleErrorCode.TEST_NOT_ALLOWED_PRO);
        }
        if ((purchaseType == null || purchaseType != 0) && !environmentUtils.isProdEnvironment()) {
            log.error("Prod purchase detected, skipping processing one time from test.");
            throw new PayGoogleException(GoogleErrorCode.TEST_NOT_ALLOWED_PRO);
        }
    }

    private void handleVoidedPurchaseNotification(VoidedPurchaseNotification voidedPurchaseNotification) {
        String purchaseToken = voidedPurchaseNotification.getPurchaseToken();
        log.info("📢 退款通知: notificationType={}, purchaseToken={}, productType={}, refundType={}",
                voidedPurchaseNotification.getNotificationType(),
                purchaseToken,
                voidedPurchaseNotification.getProductType(),
                voidedPurchaseNotification.getRefundType());
    }

    @Override
    public void handleTestNotification(TestNotification testNotification) {
        log.info("🧪 测试通知: version={}", testNotification.getVersion());
    }

    @Override
    public List<GoogleProductVo> queryProduct(GoogleProductRequest request) {
        List<GoogleProduct> list = new ArrayList<>();
        if (request != null && request.getPlanLevel() != null && request.getPriceInterval() != null) {
            list.addAll(googleProductService.lambdaQuery()
                    .eq(GoogleProduct::getPlanLevel, request.getPlanLevel())
                    .eq(GoogleProduct::getPriceInterval, request.getPriceInterval())
                    .eq(GoogleProduct::getStatus, true)
                    .list());
        }
        if (request != null && request.getCreditNum() != null) {
            list.addAll(googleProductService.lambdaQuery()
                    .eq(GoogleProduct::getLumen, request.getCreditNum())
                    .eq(GoogleProduct::getStatus, true)
                    .list());
        }

        if (request == null || request.emptyParam()) {
            list.addAll(googleProductService.lambdaQuery()
                    .eq(GoogleProduct::getStatus, true)
                    .list());
        }
        List<GoogleProductVo> listRtn = new ArrayList<>();
        list.forEach(googleProduct -> {
            GoogleProductVo googleProductVo = new GoogleProductVo();
            String rule = googleProduct.getRule();
            if (rule != null) {
                googleProductVo.setLumenRule(JSONUtil.parseObj(rule));
            }
            BeanUtils.copyProperties(googleProduct, googleProductVo);
            googleProductVo.setProductId(googleProduct.getProductId());
            googleProductVo.setProductType(googleProduct.getProductType());
            googleProductVo.setPriceInterval(googleProduct.getPriceInterval());
            googleProductVo.setPlanLevel(googleProduct.getPlanLevel());
            googleProductVo.setVipLevel(googleProduct.getVipLevel());
            listRtn.add(googleProductVo);
        });
        return listRtn;
    }

    @Override
    public boolean checkCanByNewSub(User userPO) {
        List<SubscriptionCurrent> validSubscriptionsFromDb = subscriptionCurrentService.getValidSubscriptionsFromDb(userPO.getId());
        if (validSubscriptionsFromDb.isEmpty()) {
            return true;
        }
        return validSubscriptionsFromDb.stream().noneMatch(subscription -> {
            return subscription.getVipPlatform().equalsIgnoreCase(VipPlatform.GOOGLE.getPlatformName());
        });
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
