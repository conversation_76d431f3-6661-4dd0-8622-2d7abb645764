package com.lx.pl.pay.google.enums;

public enum SubscriptionNotificationType {
    SUBSCRIPTION_RECOVERED(1, "从账号保留状态恢复了订阅"),
    SUBSCRIPTION_RENEWED(2, "续订了处于活动状态的订阅"),
    SUBSCRIPTION_CANCELED(3, "自愿或非自愿地取消了订阅。如果是自愿取消，在用户取消时发送"),
    SUBSCRIPTION_PURCHASED(4, "购买了新的订阅"),
    SUBSCRIPTION_ON_HOLD(5, "订阅已进入账号保留状态（如果已启用）"),
    SUBSCRIPTION_IN_GRACE_PERIOD(6, "订阅已进入宽限期（如果已启用）"),
    SUBSCRIPTION_RESTARTED(7, "用户已通过 Play > 账号 > 订阅恢复了订阅。订阅已取消，但在用户恢复时尚未到期"),
    SUBSCRIPTION_PRICE_CHANGE_CONFIRMED(8, "用户已成功确认订阅价格变动"),
    SUBSCRIPTION_DEFERRED(9, "订阅的续订时间点已延期"),
    SUBSCRIPTION_PAUSED(10, "订阅已暂停"),
    SUBSCRIPTION_PAUSE_SCHEDULE_CHANGED(11, "订阅暂停计划已更改"),
    SUBSCRIPTION_REVOKED(12, "用户在到期时间之前已撤消订阅"),
    SUBSCRIPTION_EXPIRED(13, "订阅已到期"),
    SUBSCRIPTION_PENDING_PURCHASE_CANCELED(20, "待处理的交易项订阅已取消");

    private final int type;
    private final String description;

    SubscriptionNotificationType(int type, String description) {
        this.type = type;
        this.description = description;
    }

    public int getType() {
        return type;
    }

    public String getDescription() {
        return description;
    }

    public static SubscriptionNotificationType fromInt(int type) {
        for (SubscriptionNotificationType value : SubscriptionNotificationType.values()) {
            if (value.getType() == type) {
                return value;
            }
        }
        throw new IllegalArgumentException("Unknown notification type: " + type);
    }
}
