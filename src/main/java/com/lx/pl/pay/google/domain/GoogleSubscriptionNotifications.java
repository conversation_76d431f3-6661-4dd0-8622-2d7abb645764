package com.lx.pl.pay.google.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lx.pl.db.mysql.gen.entity.MyBaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName("google_subscription_notifications")
@Schema(description = "存储 Google Pub/Sub 订阅变更通知")
public class GoogleSubscriptionNotifications extends MyBaseEntity {

    @TableId(value = "id")
    @Schema(description = "订阅通知ID")
    private Long id;

    @TableField(value = "purchase_token")
    @Schema(description = "Google Play 购买 Token")
    private String purchaseToken;

    private String messageId;

    @TableField(value = "notification_type")
    @Schema(description = "通知类型（例如 subscriptionNotification, oneTimeProductNotification, voidedPurchaseNotification,testNotification）")
    private String notificationType;

    @TableField(value = "received_at")
    @Schema(description = "通知接收时间")
    private Long receivedAt;

    @TableField(value = "raw_data")
    @Schema(description = "Google 订阅通知的 JSON 原始数据")
    private String rawData;
}
