package com.lx.pl.pay.google.service;

import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.pay.google.model.*;

import java.util.List;

public interface IGooglePayService {


//    GoogleValidateResponse verifyInApp(GooglePayVerifyRequest request, User user);
//
//    String handleSubscriptionNotification(SubscriptionNotification subscriptionNotification);
//
//    void handleOneTimeProductNotification(OneTimeProductNotification oneTimeProductNotification);
//
//    void handleVoidedPurchaseNotification(VoidedPurchaseNotification voidedPurchaseNotification);
//
//    void handleTestNotification(TestNotification testNotification);


    void handleRTDNEvent(String decodedMessage, String messageId) throws Exception;

    GoogleValidateResponse verifyInApp(GooglePayVerifyRequest request, User userPO);

    void handleTestNotification(TestNotification testNotification);

    List<GoogleProductVo> queryProduct(GoogleProductRequest request);

    boolean checkCanByNewSub(User userPO);

}
