package com.lx.pl.pay.stripe.service.strategy;

import com.lx.pl.pay.common.service.PayLumenRecordService;
import com.lx.pl.pay.common.service.SubscriptionCurrentService;
import com.lx.pl.pay.stripe.service.StripeUserCustomerService;
import com.lx.pl.service.VipService;
import com.stripe.model.StripeObject;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import javax.annotation.Resource;

/**
 * 订单状态处理
 *
 * <AUTHOR>
 */
public abstract class IStripeEventHandler<T extends StripeObject> implements ApplicationContextAware {
    protected Logger log = LoggerFactory.getLogger("stripe-pay-msg");
    @Resource
    protected RedissonClient redissonClient;
    @Resource
    protected StripeUserCustomerService stripeUserCustomerService;
    @Resource
    protected VipService vipService;
    @Resource
    protected SubscriptionCurrentService subscriptionCurrentService;
    protected ApplicationContext applicationContext;
    @Resource
    protected PayLumenRecordService payLumenRecordService;

    public abstract void handleEvent(T data, String eventId);

    final public void doHandle(StripeObject data, String eventId) {
        @SuppressWarnings("unchecked")
        T typedData = (T) data;
        this.handleEvent(typedData, eventId);
    }

    @Override
    public void setApplicationContext(@NotNull ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }
}
