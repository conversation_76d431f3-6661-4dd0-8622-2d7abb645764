package com.lx.pl.pay.stripe.service.strategy.invoice;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.lx.pl.db.mysql.gen.entity.StripeProduct;
import com.lx.pl.pay.common.domain.UserPayRecord;
import com.lx.pl.pay.common.dto.SavePaymentRecordRequest;
import com.lx.pl.pay.common.enums.PaymentDetailEnum;
import com.lx.pl.pay.common.enums.PaymentPlatform;
import com.lx.pl.pay.common.enums.PaymentSourceEnum;
import com.lx.pl.pay.common.service.UserPayRecordService;
import com.lx.pl.pay.common.service.PayCouponLogService;
import com.lx.pl.pay.stripe.annotation.StripeEvent;
import com.lx.pl.pay.stripe.domain.StripeInvoice;
import com.lx.pl.pay.stripe.domain.StripeUserCustomer;
import com.lx.pl.pay.stripe.service.PayLogicPurchaseRecordService;
import com.lx.pl.pay.stripe.service.StripeInvoiceService;
import com.lx.pl.pay.stripe.service.strategy.IStripeEventHandler;
import com.lx.pl.service.StripeProductService;
import com.stripe.model.*;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.lx.pl.pay.PayConstant.INVOICE_LOCK_PREFIX;
import static com.lx.pl.pay.common.enums.PaymentDetailEnum.getByPlanLevelAndPriceInterval;
import static com.lx.pl.pay.stripe.service.strategy.invoice.InvoicePaymentFailedEvent.buildStripeInvoice;

/**
 * <AUTHOR>
 */
@Component
@StripeEvent(eventType = {"invoice.payment_succeeded", "invoice.paid"})
public class InvoicePaymentSucceededEvent extends IStripeEventHandler<Invoice> {

    @Resource
    private StripeInvoiceService stripeInvoiceService;

    @Resource
    private PayLogicPurchaseRecordService paymentLogicPurchaseRecordService;

    @Resource
    private UserPayRecordService userPayRecordService;
    @Autowired
    private StripeProductService stripeProductService;

    @Resource
    private PayCouponLogService payCouponLogService;

    @Override
    public void handleEvent(Invoice event, String eventId) {
        String id = event.getId();
        RLock lock = redissonClient.getLock(INVOICE_LOCK_PREFIX + id);
        log.info("start invoice.payment_succeeded: {} {} {}", event.getCustomer(), eventId, INVOICE_LOCK_PREFIX + id);
        try {
            lock.lock();
            String loginName = applicationContext.getBean(InvoicePaymentSucceededEvent.class)
                    .doHandleEvent(event, id);
            if (StrUtil.isNotBlank(loginName)) {
                vipService.resettingPersonalLumens(loginName);
                vipService.clearTrialFirst(loginName);
            }
        } finally {
            if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
                log.info("unlock invoice.payment_succeeded: {} {} {}", event.getCustomer(), eventId, INVOICE_LOCK_PREFIX + id);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public String doHandleEvent(Invoice event, String id) {
        log.info("start doHandleEvent invoice.payment_succeeded: {} {}", event.getCustomer(), id);
        StripeInvoice one = stripeInvoiceService.lambdaQuery()
                .eq(StripeInvoice::getInvoiceId, id)
                .one();
        StripeUserCustomer userCustomer = stripeUserCustomerService.lambdaQuery()
                .eq(StripeUserCustomer::getCustomerId, event.getCustomer())
                .one();
        if (one == null) {
            StripeInvoice invoice = buildStripeInvoice(event, userCustomer);
            stripeInvoiceService.save(invoice);
            // 订单完成的处理逻辑
            log.info("save invoice.payment_succeeded: {} {}", event.getCustomer(), event.getId());
        } else {
            if (!InvoiceUtil.isTerminalState(one)) {
                stripeInvoiceService.lambdaUpdate()
                        .eq(StripeInvoice::getInvoiceId, id)
                        .set(StripeInvoice::getStatus, event.getStatus())
                        .set(StripeInvoice::getAmountPaid, event.getAmountPaid())
                        .set(StripeInvoice::getAmountDue, event.getAmountDue())
                        .set(StripeInvoice::getAmountRemaining, event.getAmountRemaining())
                        .set(event.getCustomerAddress() != null, StripeInvoice::getCustomerCountry, event.getCustomerAddress().getCountry())
                        .set(StripeInvoice::getUpdateTime, LocalDateTime.now()).
                        update();
                log.info("exist invoice.payment_succeeded: {} {} {} ", event.getCustomer(), event.getId(), event.getStatus());
            } else {
                log.info("isTerminalState invoice.payment_succeeded: {} {} {}", event.getCustomer(), event.getId(), event.getStatus());
            }
        }
        InvoiceLineItemCollection lines = event.getLines();
        if (lines != null) {
            if (CollUtil.isNotEmpty(lines.getData())) {
                List<InvoiceLineItem> data = lines.getData();
                Map<String, Integer> priceQtyMap = new HashMap<>();
                for (InvoiceLineItem invoiceLineItem : data) {
                    Price price = invoiceLineItem.getPrice();
                    if (price != null && "one_time".equals(price.getType())) {
                        String priceId = price.getId();
                        int quantity = Integer.parseInt(invoiceLineItem.getQuantity()
                                .toString());
                        priceQtyMap.put(priceId, quantity);
                    }
                }
                boolean isFirstBy = !payLumenRecordService.hasPurchasedLumen(userCustomer.getUserId());
                if (CollUtil.isNotEmpty(priceQtyMap)) {
                    paymentLogicPurchaseRecordService.saveOneTimeLumen(priceQtyMap, userCustomer, event.getId(), isFirstBy);
                    if (event.getDiscount() != null) {
                        payCouponLogService.saveLumenLogIfNeed(priceQtyMap, event, userCustomer);
                    }
                } else {
                    log.info("no one_time price in invoice.payment_succeeded: {} {}", event.getCustomer(), event.getId());
                    payCouponLogService.saveLogIfNeed(event, userCustomer);
                }
                saveUserPayRecord(event, userCustomer, isFirstBy);
            }
            log.info("end doHandleEvent invoice.payment_succeeded: {} {}", event.getCustomer(), id);
        }
        return userCustomer.getLoginName();

    }

    private void saveUserPayRecord(Invoice event, StripeUserCustomer userCustomer, boolean isFirstBy) {
        UserPayRecord exist = userPayRecordService.checkStripeIdempotency(event.getId());
        if (exist != null) {
            log.info("exist userPayRecord: {} {}", event.getCustomer(), event.getId());
            return;
        }
        if (!"paid".equals(event.getStatus())) {
            log.info("not paid invoice.payment_succeeded: {} {}", event.getCustomer(), event.getId());
            return;
        }
        SavePaymentRecordRequest userPayRecord = new SavePaymentRecordRequest();
        userPayRecord.setLoginName(userCustomer.getLoginName());
        userPayRecord.setUserId(userCustomer.getUserId());
        userPayRecord.setPlatform(PaymentPlatform.STRIPE.getCode());
        userPayRecord.setCurrency(event.getCurrency());
        // Convert amounts to milliunits (multiply by 10)
        userPayRecord.setAfterDiscountAmount(event.getAmountPaid() * 10L);
        userPayRecord.setAmountExcludingTax(event.getSubtotalExcludingTax() * 10L);
        userPayRecord.setExternalTransactionId(event.getId());
        userPayRecord.setAmount(event.getSubtotal() * 10L);
        Discount discount = event.getDiscount();
        if (discount != null) {
            Coupon coupon = discount.getCoupon();
            if (coupon != null) {
                userPayRecord.setCouponCode(coupon.getId());
                userPayRecord.setPercentOff(coupon.getPercentOff().intValue());
            }
        }
        InvoiceLineItemCollection lines = event.getLines();
        if (lines == null || CollUtil.isEmpty(lines.getData())) {
            return;
        }
        List<InvoiceLineItem> data = lines.getData();
        if (CollUtil.isEmpty(data)) {
            return;
        }
        String source = PaymentSourceEnum.SUBSCRIBE.getName();
        List<SavePaymentRecordRequest.PaymentRecordItemRequest> items = new ArrayList<>();
        PaymentDetailEnum byPlanLevelAndPriceInterval = null;
        String detailSubscription = null;
        for (InvoiceLineItem invoiceLineItem : data) {
            SavePaymentRecordRequest.PaymentRecordItemRequest paymentRecordItemRequest = new SavePaymentRecordRequest.PaymentRecordItemRequest();
            Price price = invoiceLineItem.getPrice();
            String priceId = price.getId();
            StripeProduct stripeProductByPriceId = stripeProductService.getStripeProductByPriceId(priceId);
            if (stripeProductByPriceId == null) {
                continue;
            }
            if ("one_time".equals(price.getType())) {
                paymentRecordItemRequest.setProductType("one");
                paymentRecordItemRequest.setGiftLumen(isFirstBy ? stripeProductByPriceId.getInitialLumen() : 0);
                source = PaymentSourceEnum.PURCHASE_LUMENS.getName();
                byPlanLevelAndPriceInterval = PaymentDetailEnum.LUMENS_PURCHASE;
            } else {
                paymentRecordItemRequest.setProductType("plan");
                paymentRecordItemRequest.setGiftLumen(0);
                byPlanLevelAndPriceInterval = getByPlanLevelAndPriceInterval(stripeProductByPriceId.getPlanLevel(), stripeProductByPriceId.getPriceInterval());
                detailSubscription = byPlanLevelAndPriceInterval.getName();
                if (invoiceLineItem.getDescription() != null && invoiceLineItem.getDescription().toLowerCase().contains("trial")) {
                    detailSubscription = byPlanLevelAndPriceInterval.getName() + " (Trial)";
                }
            }

            paymentRecordItemRequest.setPriceInterval(stripeProductByPriceId.getPriceInterval());
            paymentRecordItemRequest.setPlanLevel(stripeProductByPriceId.getPlanLevel());

            // Convert amounts to milliunits (multiply by 10)
            paymentRecordItemRequest.setUnitAmount(price.getUnitAmount() * 10L);
            paymentRecordItemRequest.setTotalAmount(invoiceLineItem.getAmount() * 10L);
//            paymentRecordItemRequest.setTotalAmountExcludingTax(invoiceLineItem.getAmountExcludingTax() * 10L);

            paymentRecordItemRequest.setCurrency(invoiceLineItem.getCurrency());
            paymentRecordItemRequest.setUnitLumen(stripeProductByPriceId.getLumen());
            paymentRecordItemRequest.setQty(Integer.parseInt(invoiceLineItem.getQuantity().toString()));
            paymentRecordItemRequest.setTotalLumen(stripeProductByPriceId.getLumen() * paymentRecordItemRequest.getQty() + paymentRecordItemRequest.getGiftLumen());
            // Note: unitAmount already set above with milliunit conversion
            paymentRecordItemRequest.setPercentOff(userPayRecord.getPercentOff());
            items.add(paymentRecordItemRequest);
        }
        Long totalLumen = items.stream().mapToLong(SavePaymentRecordRequest.PaymentRecordItemRequest::getTotalLumen).sum();
        userPayRecord.setTotalLumen(totalLumen);
        if (byPlanLevelAndPriceInterval != null) {
            userPayRecord.setDetail(String.format(byPlanLevelAndPriceInterval.getName(), totalLumen));
        }
        if (detailSubscription != null) {
            userPayRecord.setDetail(detailSubscription);

        }
        userPayRecord.setSource(String.format(source, totalLumen));
        userPayRecord.setItems(items);
        userPayRecordService.savePaymentRecord(userPayRecord);
    }
}