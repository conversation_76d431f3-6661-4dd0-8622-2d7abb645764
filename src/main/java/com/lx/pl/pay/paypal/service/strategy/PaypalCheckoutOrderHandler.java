package com.lx.pl.pay.paypal.service.strategy;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.lx.pl.pay.common.service.UserPayRecordService;
import com.lx.pl.enums.AlarmEnum;
import com.lx.pl.pay.paypal.annotation.PaypalEvent;
import com.lx.pl.pay.paypal.model.PaypalCheckoutOrderModel;
import com.lx.pl.pay.paypal.model.domain.PayPalOrderPaymentRecord;
import com.lx.pl.pay.paypal.model.event.PaypalCheckoutOrderEvent;
import com.lx.pl.pay.paypal.service.IPaypalEventHandler;
import com.lx.pl.service.message.DingTalkAlert;
import com.lx.pl.util.LogicUtil;
import com.lx.pl.pay.paypal.service.PayPalProductService;
import com.paypal.api.payments.Error;
import com.paypal.api.payments.ErrorDetails;
import com.paypal.base.rest.PayPalRESTException;
import org.redisson.api.RLock;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 *
 */
@Component
@PaypalEvent(eventType = {"CHECKOUT.ORDER.APPROVED"})
public class PaypalCheckoutOrderHandler extends IPaypalEventHandler<PaypalCheckoutOrderEvent> {
    @Resource
    private DingTalkAlert dingTalkAlert;


    @Resource
    private UserPayRecordService userPayRecordService;
    @Resource
    private PayPalProductService payPalProductService;
    @Override
    public void handleEvent(PaypalCheckoutOrderEvent data) {
        PaypalCheckoutOrderModel checkoutOrderModel = data.getModel();
        List<PaypalCheckoutOrderModel.PurchaseUnit> purchaseUnits = checkoutOrderModel.getPurchaseUnits();
        String customerId = checkoutOrderModel.getPurchaseUnits().get(0).getCustomId();
        // str to json
        if (customerId != null) {
            JSONObject entries = JSONUtil.parseObj(customerId);
            customerId = entries.getStr("userId");
        }
        RLock lock = redissonClient.getLock(PAYPAL_USER_LOCK_PREFIX + customerId);
        String loginName = null;
        try {
            lock.lock();
            loginName = applicationContext.getBean(PaypalCheckoutOrderHandler.class).doHandleEvent(checkoutOrderModel);
            if (StrUtil.isNotBlank(loginName)) {
                vipService.resettingPersonalLumens(loginName);
            }
        } finally {
            unlockAndRefreshVip(lock, loginName);
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public String doHandleEvent(PaypalCheckoutOrderModel data) {
        List<PayPalOrderPaymentRecord> captureModelList = payPalOrderPaymentRecordService.createPayment(data);
        if (captureModelList == null || captureModelList.isEmpty()) {
            log.info("createPayment failed");
            return null;
        }
        String loginName = captureModelList.get(0).getLoginName();
        String paymentId = captureModelList.get(0).getOrderId();
        try {
            PaypalCheckoutOrderModel detail = PaypalCheckoutOrderModel.detail(apiContext, paymentId);
            log.info("paypal checkout order detail,{}", detail);
            if ("COMPLETED".equalsIgnoreCase(detail.getStatus())) {
                // 保存用户支付记录
//                savePayPalUserPayRecord(data, captureModelList.get(0));
                return loginName;
            }
            PaypalCheckoutOrderModel capture = PaypalCheckoutOrderModel.capture(apiContext, paymentId);
            log.info("paypal checkout order capture success,{}", capture);

//            // 保存用户支付记录
//            savePayPalUserPayRecord(data, captureModelList.get(0));
        } catch (PayPalRESTException e) {
            int responsecode = e.getResponsecode();
            //发送告警信息
            dingTalkAlert.sendAlarmMessage(LogicUtil.buildAlarmMessage(
                    AlarmEnum.AlarmTypeEnum.EXCEPTION.getDescription(),
                    AlarmEnum.AlarmSourceEnum.PAY_PAL.getDescription(),
                    "PayPal checkout order exception, responseCode: " + responsecode,
                    loginName,
                    e));
            if (responsecode == 422) {
                Error details = e.getDetails();
                if (details != null) {
                    List<ErrorDetails> details1 = details.getDetails();
                    for (ErrorDetails errorDetails : details1) {
                        String issue = errorDetails.getIssue();
                        if ("INSTRUMENT_DECLINED".equalsIgnoreCase(issue)) {
                            log.info("paypal checkout order capture failed,{},{}", loginName, issue);
                            return loginName;
                        }
                    }
                }
            }
            throw new RuntimeException(e);
        }
        return loginName;
    }

    /**
     * 保存PayPal一次性支付的用户支付记录
     *
     * @param orderModel PayPal订单模型
     * @param paymentRecord PayPal支付记录
     */
    /*rivate void savePayPalUserPayRecord(PaypalCheckoutOrderModel orderModel, PayPalOrderPaymentRecord paymentRecord) {
        try {
            // 检查幂等性
            if (userPayRecordService.checkPayPalIdempotency(orderModel.getId(), null, paymentRecord.getPayerId()) != null) {
                log.info("PayPal user pay record already exists for orderId: {}", orderModel.getId());
                return;
            }

            User user = userService.getUserById(paymentRecord.getUserId());
            if (user == null) {
                log.error("User not found for userId: {}", paymentRecord.getUserId());
                return;
            }

            SavePaymentRecordRequest request = new SavePaymentRecordRequest();
            request.setLoginName(user.getLoginName());
            request.setUserId(user.getId());
            request.setPlatform(PaymentPlatform.PAYPAL.getCode());
            request.setExternalTransactionId(orderModel.getId());
            request.setExternalOrderId(paymentRecord.getPaymentId());
            request.setPaymentStatus("completed");

            // 设置金额信息
            if (paymentRecord.getAmount() != null) {
                // PayPal金额通常以字符串形式存储，需要转换为Long（分为单位）
                try {
                    Double amount = Double.parseDouble(paymentRecord.getAmount());
                    Long amountInCents = Math.round(amount * 100);
                    request.setAmount(amountInCents);
                    request.setAfterDiscountAmount(amountInCents);
                    request.setAmountExcludingTax(amountInCents);
                } catch (NumberFormatException e) {
                    log.warn("Failed to parse amount: {}", paymentRecord.getAmount());
                }
            }

            // 设置货币
            if (paymentRecord.getCurrency() != null) {
                request.setCurrency(paymentRecord.getCurrency());
            }

            // 设置来源和详情（一次性支付通常是购买Lumens）
            String source = PaymentSourceEnum.PURCHASE_LUMENS.getName();
            String detail = "PayPal Purchase";
            PaymentDetailEnum paymentDetail = PaymentDetailEnum.LUMENS_PURCHASE;

            // 尝试从订单中获取产品信息
            List<PaypalCheckoutOrderModel.PurchaseUnit> purchaseUnits = orderModel.getPurchaseUnits();
            if (purchaseUnits != null && !purchaseUnits.isEmpty()) {
                PaypalCheckoutOrderModel.PurchaseUnit purchaseUnit = purchaseUnits.get(0);
                String customId = purchaseUnit.getCustomId();

                if (customId != null) {
                    try {
                        JSONObject customData = JSONUtil.parseObj(customId);
                        Integer lumenAmount = customData.getInt("lumen");
                        if (lumenAmount != null) {
                            request.setTotalLumen(lumenAmount.longValue());
                            detail = String.format(paymentDetail.getName(), lumenAmount);
                        }
                    } catch (Exception e) {
                        log.warn("Failed to parse custom data: {}", customId, e);
                    }
                }
            }

            request.setSource(source);
            request.setDetail(detail);

            // 创建支付记录项
            List<SavePaymentRecordRequest.PaymentRecordItemRequest> items = new ArrayList<>();
            SavePaymentRecordRequest.PaymentRecordItemRequest item = new SavePaymentRecordRequest.PaymentRecordItemRequest();

            item.setProductType("one");
            item.setQty(1);

            if (paymentRecord.getAmount() != null) {
                try {
                    Double amount = Double.parseDouble(paymentRecord.getAmount());
                    Long amountInCents = Math.round(amount * 100);
                    item.setUnitAmount(amountInCents);
                    item.setTotalAmount(amountInCents);
                    item.setTotalAmountExcludingTax(amountInCents);
                } catch (NumberFormatException e) {
                    log.warn("Failed to parse amount for item: {}", paymentRecord.getAmount());
                }
            }

            if (paymentRecord.getCurrency() != null) {
                item.setCurrency(paymentRecord.getCurrency());
            }

            if (request.getTotalLumen() != null) {
                item.setUnitLumen(request.getTotalLumen().intValue());
                item.setTotalLumen(request.getTotalLumen().intValue());
            }

            items.add(item);
            request.setItems(items);

            // 保存支付记录
            userPayRecordService.savePaymentRecord(request);
            log.info("Successfully saved PayPal user pay record for userId: {}, orderId: {}",
                    user.getId(), orderModel.getId());

        } catch (Exception e) {
            log.error("Failed to save PayPal user pay record for orderId: {}",
                    orderModel.getId(), e);
            // 不抛出异常，避免影响主流程
        }
    }*/

}
