package com.lx.pl.pay.paypal.service.strategy;

import cn.hutool.core.util.StrUtil;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.enums.AlarmEnum;
import com.lx.pl.pay.common.dto.SavePaymentRecordRequest;
import com.lx.pl.pay.common.enums.PaymentDetailEnum;
import com.lx.pl.pay.common.enums.PaymentPlatform;
import com.lx.pl.pay.common.enums.PaymentSourceEnum;
import com.lx.pl.pay.common.service.UserPayRecordService;
import com.lx.pl.pay.common.service.PayCouponLogService;
import com.lx.pl.pay.paypal.annotation.PaypalEvent;
import com.lx.pl.pay.paypal.model.PaypalPaymentSaleModel;
import com.lx.pl.pay.paypal.model.domain.PayPalLogicSubscription;
import com.lx.pl.pay.paypal.model.domain.PayPalProduct;
import com.lx.pl.pay.paypal.model.domain.PayPalSubPaymentRecord;
import com.lx.pl.pay.paypal.model.event.PaypalPaymentSaleEvent;
import com.lx.pl.pay.paypal.service.IPaypalEventHandler;
import com.lx.pl.pay.paypal.service.PayPalProductService;
import com.lx.pl.service.message.DingTalkAlert;
import com.lx.pl.util.LogicUtil;
import org.redisson.api.RLock;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 *
 */
@Component
@PaypalEvent(eventType = {"PAYMENT.SALE.COMPLETED", "PAYMENT.SALE.PENDING", "PAYMENT.SALE.DENIED", "PAYMENT.SALE.REVERSED"})
public class PaypalPaymentSaleHandler extends IPaypalEventHandler<PaypalPaymentSaleEvent> {
    @Resource
    private DingTalkAlert dingTalkAlert;
    @Resource
    private PayCouponLogService payCouponLogService;

    @Resource
    private UserPayRecordService userPayRecordService;
    @Resource
    private PayPalProductService payPalProductService;

    @Override
    public void handleEvent(PaypalPaymentSaleEvent data) {
        PaypalPaymentSaleModel saleModel = data.getModel();
        String billingAgreementId = saleModel.getBillingAgreementId();
        String id = saleModel.getId();
        String lockId = billingAgreementId == null ? id : billingAgreementId;
//        PayPalLogicSubscription payPalLogicSubscription = applicationContext.getBean(PayPalLogicSubscriptionService.class).queryBySubscriptionId(billingAgreementId);
//        if (payPalLogicSubscription != null) {
//            lockId= payPalLogicSubscription.getUserId().toString();
//        }
//        customId == null ? subscription.getId() : entries.getStr("userId"))
        log.info("lock key {}", PAYPAL_ACTION_LOCK_PREFIX + lockId);
        RLock lock = redissonClient.getLock(PAYPAL_ACTION_LOCK_PREFIX + lockId);
        String loginName = null;
        try {
            lock.lock();
            loginName = applicationContext.getBean(PaypalPaymentSaleHandler.class).doHandleEvent(data);
            if (StrUtil.isNotBlank(loginName)) {
                vipService.resettingPersonalLumens(loginName);
            }
        } finally {
            unlockAndRefreshVip(lock, loginName);
        }
    }


    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public String doHandleEvent(PaypalPaymentSaleEvent data) {
        log.info("doHandleEvent: {}", data.toJSON());
        if (!Objects.equals(data.getEventType(), "PAYMENT.SALE.COMPLETED")) {
            //发送告警信息
            dingTalkAlert.sendAlarmMessage(LogicUtil.buildAlarmMessage(
                    AlarmEnum.AlarmTypeEnum.BUSINESS.getDescription(),
                    AlarmEnum.AlarmSourceEnum.PAY_PAL.getDescription(),
                    "PayPal payment sale alarm, id: " + data.getId() + ", event: " + data.getEventType(),
                    LogicUtil.getUserIdByPayPalEvent(data),
                    null));
        }
        switch (data.getEventType()) {
            case "PAYMENT.SALE.COMPLETED":
                log.info("PAYMENT.SALE.COMPLETED: {}", data.getModel().toJSON());
                return handleCompletedPayment(data);
            case "PAYMENT.SALE.PENDING":
                log.info("PAYMENT.SALE.PENDING:{} {}", data.getModel().getBillingAgreementId(), data.getModel().toJSON());
                // 通知 管理员检查是否到账会员
                return null;
            case "PAYMENT.SALE.DENIED":
            case "PAYMENT.SALE.REFUNDED":
            case "PAYMENT.SALE.REVERSED":
                return handleReversedPayment(data);
            default:
                log.info("PAYMENT.SALE.OTHER: {}", data.getModel().toJSON());
                // todo ADD MSG
                return null;
        }

    }

    private String handleReversedPayment(PaypalPaymentSaleEvent data) {
        PayPalSubPaymentRecord payPalSubPaymentRecord = payPalSubPaymentRecordService.queryByPaymentId(data.getModel().getId());
        if (payPalSubPaymentRecord != null) {
            payPalSubPaymentRecordService.lambdaUpdate().eq(PayPalSubPaymentRecord::getId, payPalSubPaymentRecord.getId()).set(PayPalSubPaymentRecord::getState, data.getEventType())
                    .set(PayPalSubPaymentRecord::getReasonCode, data.getModel().getReasonCode()).set(PayPalSubPaymentRecord::getUpdateTime, LocalDateTime.now()).update();
        } else {
            log.info("PAYMENT.SALE.REVERSED: {} 不存在", data.getModel().getId());
            payPalSubPaymentRecordService.saveRecordIfNeed(data.getModel());
            return null;
        }
        return null;
    }

    private String handleCompletedPayment(PaypalPaymentSaleEvent data) {
        PaypalPaymentSaleModel saleModel = data.getModel();
        PayPalSubPaymentRecord payPalSubPaymentRecord = payPalSubPaymentRecordService.saveRecordIfNeed(saleModel);
        if (payPalSubPaymentRecord != null) {
            // 保存用户支付记录
            savePayPalSubscriptionUserPayRecord(saleModel, payPalSubPaymentRecord);

            // 记录优惠券使用日志
            payCouponLogService.savePaypalSubLogIfNeed(payPalSubPaymentRecord, saleModel.getCustom());

            // 计算会员逻辑
            log.info("start calculateVipByPayment");
            return payPalLogicSubscriptionService.calculateVipByPayment(payPalSubPaymentRecord);
        }
        return null;
    }

    /**
     * 保存PayPal订阅支付的用户支付记录
     *
     * @param saleModel PayPal支付销售模型
     * @param paymentRecord PayPal订阅支付记录
     */
    private void savePayPalSubscriptionUserPayRecord(PaypalPaymentSaleModel saleModel, PayPalSubPaymentRecord paymentRecord) {
        try {
            // 检查幂等性+
            if (userPayRecordService.checkPayPalIdempotency( saleModel.getBillingAgreementId(), saleModel.getId()) != null) {

                log.info("PayPal subscription user pay record alrnull,eady exists for paymentId: {}, subscriptionId: {}",
                        saleModel.getId(), saleModel.getBillingAgreementId());
                return;
            }

            User user = userService.getUserById(paymentRecord.getUserId());
            if (user == null) {
                log.error("User not found for userId: {}", paymentRecord.getUserId());
                return;
            }

            // 获取订阅信息
            PayPalLogicSubscription subscription = payPalLogicSubscriptionService.queryBySubscriptionId(saleModel.getBillingAgreementId());
            if (subscription == null) {
                log.error("Subscription not found for subscriptionId: {}", saleModel.getBillingAgreementId());
                return;
            }

            SavePaymentRecordRequest request = new SavePaymentRecordRequest();
            request.setLoginName(user.getLoginName());
            request.setUserId(user.getId());
            request.setPlatform(PaymentPlatform.PAYPAL.getCode());
            request.setExternalTransactionId(saleModel.getBillingAgreementId());
            request.setExternalOrderId(saleModel.getId());
            request.setPaymentStatus("completed");

            // 设置金额信息
            if (saleModel.getAmount() != null && saleModel.getAmount().getTotal() != null) {
                try {
                    Double amount = Double.parseDouble(saleModel.getAmount().getTotal());
                    Long amountInCents = Math.round(amount * 1000);
                    request.setAmount(amountInCents);
                    request.setAfterDiscountAmount(amountInCents);
                    request.setAmountExcludingTax(amountInCents);
                } catch (NumberFormatException e) {
                    log.warn("Failed to parse amount: {}", saleModel.getAmount().getTotal());
                }
            }

            // 设置货币
            if (saleModel.getAmount() != null && saleModel.getAmount().getCurrency() != null) {
                request.setCurrency(saleModel.getAmount().getCurrency());
            }

            // 获取产品信息
            PayPalProduct product = payPalProductService.getPaypalProductByPlanId(subscription.getPlanId());

            // 设置来源和详情
            String source = PaymentSourceEnum.SUBSCRIBE.getName();
            String detail;
            PaymentDetailEnum paymentDetail = null;

            if (product != null) {
                paymentDetail = getPaymentDetailByPlanAndInterval(product.getPlanLevel(), product.getPriceInterval());
                if (paymentDetail != null) {
                    detail = paymentDetail.getName();
                } else {
                    detail = String.format("%s %s Subscription",
                            product.getPlanLevel() != null ? product.getPlanLevel() : "Standard",
                            product.getPriceInterval() != null ? product.getPriceInterval() : "Monthly");
                }
                request.setAmount(new BigDecimal(product.getPrice()).multiply(new BigDecimal(1000)).longValue());

                // 设置总Lumen数量
                if (product.getLumen() != null) {
                    request.setTotalLumen(product.getLumen().longValue());
                }
            } else {
                detail = "PayPal Subscription";
            }

            request.setSource(source);
            request.setDetail(detail);

            // 创建支付记录项
            List<SavePaymentRecordRequest.PaymentRecordItemRequest> items = new ArrayList<>();
            SavePaymentRecordRequest.PaymentRecordItemRequest item = new SavePaymentRecordRequest.PaymentRecordItemRequest();

            item.setProductType("plan");
            item.setQty(1);

            if (product != null) {
                item.setPlanLevel(product.getPlanLevel());
                item.setPriceInterval(product.getPriceInterval());

                if (product.getLumen() != null) {
                    item.setUnitLumen(product.getLumen());
                    item.setTotalLumen(product.getLumen());
                }
            }

            if (saleModel.getAmount() != null && saleModel.getAmount().getTotal() != null) {
                try {
                    Double amount = Double.parseDouble(saleModel.getAmount().getTotal());
                    Long amountInCents = Math.round(amount * 1000);
                    item.setUnitAmount(amountInCents);
                    item.setTotalAmount(amountInCents);
//                    item.setTotalAmountExcludingTax(amountInCents);
                } catch (NumberFormatException e) {
                    log.warn("Failed to parse amount for item: {}", saleModel.getAmount().getTotal());
                }
            }

            if (saleModel.getAmount() != null && saleModel.getAmount().getCurrency() != null) {
                item.setCurrency(saleModel.getAmount().getCurrency());
            }

            items.add(item);
            request.setItems(items);

            // 保存支付记录
            userPayRecordService.savePaymentRecord(request);
            log.info("Successfully saved PayPal subscription user pay record for userId: {}, subscriptionId: {}, paymentId: {}",
                    user.getId(), saleModel.getBillingAgreementId(), saleModel.getId());

        } catch (Exception e) {
            log.error("Failed to save PayPal subscription user pay record for subscriptionId: {}, paymentId: {}",
                    saleModel.getBillingAgreementId(), saleModel.getId(), e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 根据计划等级和价格间隔获取支付详情枚举
     */
    private PaymentDetailEnum getPaymentDetailByPlanAndInterval(String planLevel, String priceInterval) {
        if (planLevel == null || priceInterval == null) {
            return null;
        }
        return PaymentDetailEnum.getByPlanLevelAndPriceInterval(planLevel, priceInterval);
    }
}
