package com.lx.pl.pay.paypal.model;

import com.google.common.collect.Lists;
import com.google.gson.JsonObject;
import com.lx.pl.util.UUID;
import com.paypal.base.rest.APIContext;
import com.paypal.base.rest.JSONFormatter;
import com.paypal.base.rest.PayPalRESTException;

import java.io.UnsupportedEncodingException;
import java.net.MalformedURLException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Collections;

public class Demo {
    public static void main(String[] args) throws Exception {
        APIContext apiContext = new APIContext("AdnYmFXWW69fEYCZKY2E5a_ZH0hq6BJnO9WZND-V2EZIj_5bTXLeygHlaGUjjVr93NlOsSsWy9AIZRyT",
                "EJ2-8Ot5acA7_w_ypwzl0H4e7UcVpqPuyCGMlboMwVaWpoazP64XjEE4d7b_kjw29xmDGVkXIcQAbK-l", "sandbox");

        PaypalSubscriptionModel detail = PaypalSubscriptionModel.detail(apiContext, "I-TRWFLHDENNX0");

        System.out.println(detail.toJSON());
        PaypalSubscriptionModel.cancel(apiContext, detail);
//        PaypalSubscriptionModel detai2l = PaypalSubscriptionModel.detail(apiContext, "I-AARCDRUAPR9C");
//
//        System.out.println("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@");
//        System.out.println(detai2l.toJSON());
//        queryPlanList(apiContext);
//        createProducts(apiContext);

//        createPlan(apiContext);

//        createSubscription(apiContext);
//
//        revise(apiContext);
//
//        refund(apiContext);


//        update(apiContext);


//        cancel(apiContext);
//        createOrderCheckout(apiContext);
//        orderDetail(apiContext);

    }

    private static void orderDetail(APIContext apiContext) {
        try {
            PaypalCheckoutOrderModel detail = PaypalCheckoutOrderModel.detail(apiContext, "4PG73457N1244820F");
            System.out.println(detail.toJSON());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void createPlan(APIContext apiContext) {
        try {
            /**
             * POST /v1/billing/plans
             *
             * {
             *   "product_id": "PROD-XXXXXXXXXXXX",  // 你上面创建产品时返回的 product_id
             *   "name": "My SaaS Plan - with Trial",
             *   "description": "Monthly subscription with 1-month free trial",
             *   "status": "ACTIVE",
             *   "billing_cycles": [
             *     {
             *       "frequency": {
             *         "interval_unit": "MONTH",
             *         "interval_count": 1
             *       },
             *       "tenure_type": "TRIAL",
             *       "sequence": 1,
             *       "total_cycles": 1,
             *       "pricing_scheme": {
             *         "fixed_price": {
             *           "value": "0",
             *           "currency_code": "USD"
             *         }
             *       }
             *     },
             *     {
             *       "frequency": {
             *         "interval_unit": "MONTH",
             *         "interval_count": 1
             *       },
             *       "tenure_type": "REGULAR",
             *       "sequence": 2,
             *       "total_cycles": 0,
             *       "pricing_scheme": {
             *         "fixed_price": {
             *           "value": "10",
             *           "currency_code": "USD"
             *         }
             *       }
             *     }
             *   ],
             *   "payment_preferences": {
             *     "auto_bill_outstanding": true,
             *     "setup_fee": {
             *       "value": "0",
             *       "currency_code": "USD"
             *     },
             *     "setup_fee_failure_action": "CONTINUE",
             *     "payment_failure_threshold": 3
             *   }
             * }
             */
            BillingCycle billingCycle = BillingCycle.builder().build();
            billingCycle.setFrequency(Frequency.builder().intervalCount(2).intervalUnit("DAY").build());
            billingCycle.setSequence(1);
            billingCycle.setTotalCycles(1);
            billingCycle.setTenureType("TRIAL");
            billingCycle.setPricingScheme(PricingScheme.builder().fixedPrice(FixedPrice.builder().currencyCode("USD").value("0").build()).build());

            BillingCycle billingCycle2 = BillingCycle.builder().build();
            billingCycle2.setFrequency(Frequency.builder().intervalCount(2).intervalUnit("DAY").build());
            billingCycle2.setSequence(2);
            billingCycle2.setTotalCycles(0);
            billingCycle2.setTenureType("REGULAR");
            billingCycle2.setPricingScheme(PricingScheme.builder().fixedPrice(FixedPrice.builder().currencyCode("USD").value("28.99").build()).build());

//            BillingCycle billingCycle3 = BillingCycle.builder().build();
//            billingCycle3.setFrequency(Frequency.builder().intervalCount(3).intervalUnit("DAY").build());
//            billingCycle3.setSequence(3);
//            billingCycle3.setTotalCycles(0);·
//            billingCycle3.setTenureType("REGULAR");
//            billingCycle3.setPricingScheme(PricingScheme.builder().fixedPrice(FixedPrice.builder().currencyCode("USD").value("6.99").build()).build());

            PayPalPlanModel build = PayPalPlanModel.builder()
                    .productId("PROD-01035182U18415526")
                    .status("ACTIVE")
                    .name("My SaaS Plan - with 2 bill cycle price 3")
                    .description("Monthly subscription with 1-day free trial")
                    .billingCycles(Lists.newArrayList(billingCycle, billingCycle2))
                    .paymentPreferences(PaymentPreferences.builder().autoBillOutstanding(true).paymentFailureThreshold(1).setupFeeFailureAction("CANCEL").build())
                    .build();
            PayPalPlanModel create = PayPalPlanModel.create(apiContext, build);
            System.out.println(create.toJSON());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void createProducts(APIContext apiContext) throws PayPalRESTException {
        String payload = "{\n" +
                "  \"name\": \"My SaaS Service\",\n" +
                "  \"description\": \"Access to premium features\",\n" +
                "  \"type\": \"SERVICE\",\n" +
                "  \"category\": \"SOFTWARE\"\n" +
                "}";
        PayPalPlanModel.createProduct(apiContext, payload);
    }

    private static void refund(APIContext apiContext) throws PayPalRESTException {
        PaypalCheckoutOrderModel.Amount amount = new PaypalCheckoutOrderModel.Amount();
        amount.setCurrencyCode("USD");
        amount.setValue("0.01");
        PaypalRefundModel paypalRefundModel = PaypalRefundModel.builder().id("4PG70752SD9438219").description("test666")
//                .reason("test666")
                .amount(amount).build();
        PaypalRefundModel refund = PaypalRefundModel.refund(apiContext, paypalRefundModel);
        System.out.println(refund.toJSON());
    }

    private static void revise(APIContext apiContext) throws PayPalRESTException {
        PaypalSubscriptionModel paypalSubscriptionModel = PaypalSubscriptionModel.builder()
                .id("I-BNXU9LSK1E38")
                .planId("P-4WF36099S1621671MM62XR6Q")
//                .plan(
//                        PayPalPlanModel.builder()
//                .paymentPreferences(PaymentPreferences.builder().setupFeeFailureAction("CANCEL").autoBillOutstanding(true).paymentFailureThreshold(2)
//                        .setupFee(SetupFee.builder().currencyCode("USD").value("5.22").build()).build()).build())
                .build();

        PaypalSubscriptionModel revise = PaypalSubscriptionModel.revise(apiContext, paypalSubscriptionModel);
        System.out.println(revise.toJSON());
    }


    private static void cancel(APIContext apiContext) throws PayPalRESTException {
        PaypalSubscriptionModel paypalSubscriptionModel = PaypalSubscriptionModel.builder().id("I-Y02C7PKAG1ML").reason("customer cancel subscription!!!").build();

        PaypalSubscriptionModel revise = PaypalSubscriptionModel.cancel(apiContext, paypalSubscriptionModel);
        System.out.println(revise.toJSON());
    }


    private static void update(APIContext apiContext) throws PayPalRESTException {
        PaypalSubscriptionModel paypalSubscriptionModel = PaypalSubscriptionModel.builder()
                .id("I-BNXU9LSK1E38")
                .build();
        JsonObject payload = new JsonObject();
        payload.addProperty("op", "replace");
        payload.addProperty("path", "start_time");
        payload.addProperty("value", "2025-04-14T12:33:37Z");

        String json = JSONFormatter.toJSON(payload);
        String[] payloadArray = new String[]{json};
        System.out.println(Arrays.toString(payloadArray));
//        PaypalSubscriptionModel revise = paypalSubscriptionModel.update(apiContext, payloadArray);
//        System.out.println(revise.toJSON());
    }

    private static void createSubscription(APIContext apiContext) throws MalformedURLException, UnsupportedEncodingException, PayPalRESTException {

        PaypalSubscriptionModel paypalSubscriptionModel = PaypalSubscriptionModel.builder()
                .planId("P-26T11627KS2645447NA7IJCY")
                .customId("{\"userId\":1204}")
                .plan(PayPalPlanModel.builder().billingCycles(Lists.newArrayList(BillingCycle.builder()
                                .sequence(1)
                                .pricingScheme(PricingScheme.builder().fixedPrice(FixedPrice.builder().currencyCode("USD").value("8.99").build()).build())
                                .build())).build())
//                .plan(build)
                .applicationContext(ApplicationContext.builder()
                        .returnUrl("http://www.baidu.com")
                        .cancelUrl("http://www.baidu.com")
                        .shippingPreference("NO_SHIPPING")
                        .userAction("SUBSCRIBE_NOW")
                        .build())
                .build();
        PaypalSubscriptionModel paypalSubscriptionModel1 = PaypalSubscriptionModel.create(apiContext, paypalSubscriptionModel);
        System.out.println(paypalSubscriptionModel1.toJSON());

    }


    private static void queryPlanList(APIContext apiContext) throws PayPalRESTException {
        PayPalPlanModel payPalPlanVo = PayPalPlanModel.builder().build();
        PlanHistory list = payPalPlanVo.list(apiContext, 20, 1);
        System.out.println(list.toJSON());
    }

    //ordercheckout
    private static void createOrderCheckout(APIContext apiContext) throws PayPalRESTException {

        // 构建支付来源 (PaymentSource)
        PaypalPaymentCaptureModel.PaymentSource paymentSource = PaypalPaymentCaptureModel.PaymentSource.builder().paypal(PaypalPaymentCaptureModel.PaymentSource.Paypal.builder()
                .experienceContext(PaypalPaymentCaptureModel.PaymentSource.ExperienceContext.builder().paymentMethodPreference("IMMEDIATE_PAYMENT_REQUIRED").landingPage("LOGIN")
                        .shippingPreference("NO_SHIPPING").userAction("PAY_NOW").returnUrl("https://www.baidu.com").cancelUrl("https://www.baidu.com").build()).build()).build();

        // 构建购买单元 (PurchaseUnit)
        PaypalCheckoutOrderModel.Items build = PaypalCheckoutOrderModel.Items.builder()
                .name("test")
                .description("test purchase lumen")
                .quantity("1")
                .unitAmount(PaypalCheckoutOrderModel.UnitAmount.builder()
                        .currencyCode("USD")
                        .value("5.55")
                        .build())
                .build();
        // 转数组
        PaypalCheckoutOrderModel.PurchaseUnit purchaseUnit = PaypalCheckoutOrderModel.PurchaseUnit.builder()
                .customId("1204" + "@" + UUID.fastUUID())
                // 商品的 reference_id
                .referenceId("5.500")
                .amount(PaypalCheckoutOrderModel.Amount.builder()
                        // 货币代码
                        .currencyCode("USD")
                        // 支付金额
                        .value("5.55")
                        .breakdown(PaypalCheckoutOrderModel.Breakdown.builder()
                                .itemTotal(PaypalCheckoutOrderModel.ItemTotal.builder()
                                        .currencyCode("USD")
                                        .value("5.55")
                                        .build())
                                .build())
                        .build())
                .items(new PaypalCheckoutOrderModel.Items[]{build})
                .description("Purchase " + 500 + " lumen (" + LocalDateTime.now(ZoneId.of("UTC")) + ")")
                .build();
        PaypalCheckoutOrderModel paypalCheckoutOrderModel = PaypalCheckoutOrderModel.builder()
                .intent("CAPTURE")
                .purchaseUnits(Collections.singletonList(purchaseUnit))
                .paymentSource(paymentSource)
                .build();
        PaypalCheckoutOrderModel order = PaypalCheckoutOrderModel.createOrder(apiContext, paypalCheckoutOrderModel);
        System.out.println(order.toJSON());
    }
}
