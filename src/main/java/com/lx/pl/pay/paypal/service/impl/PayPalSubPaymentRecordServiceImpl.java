package com.lx.pl.pay.paypal.service.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.pay.paypal.mapper.PayPalSubPaymentRecordMapper;
import com.lx.pl.pay.paypal.model.PaypalPaymentSaleModel;
import com.lx.pl.pay.paypal.model.domain.PayPalLogicSubscription;
import com.lx.pl.pay.paypal.model.domain.PayPalSubPaymentRecord;
import com.lx.pl.pay.paypal.service.PayPalLogicSubscriptionService;
import com.lx.pl.pay.paypal.service.PayPalSubPaymentRecordService;
import com.lx.pl.service.UserService;
import com.paypal.api.payments.Amount;
import com.paypal.api.payments.Currency;
import com.paypal.api.payments.Details;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Service
public class PayPalSubPaymentRecordServiceImpl extends ServiceImpl<PayPalSubPaymentRecordMapper, PayPalSubPaymentRecord> implements PayPalSubPaymentRecordService {
    protected Logger log = LoggerFactory.getLogger("paypal-pay-msg");


    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private UserService userService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PayPalSubPaymentRecord saveRecordIfNeed(PaypalPaymentSaleModel saleModel) {
        String billingAgreementId = saleModel.getBillingAgreementId();
        PayPalSubPaymentRecord existingRecord = this.queryByPaymentId(saleModel.getId());
        if (existingRecord != null) {
            log.info("{} record already exists {}", saleModel.getId(), existingRecord.getSubscriptionId());
            return null;
        }

        PayPalLogicSubscription payPalLogicSubscription = applicationContext.getBean(PayPalLogicSubscriptionService.class).queryBySubscriptionId(billingAgreementId);
        PayPalSubPaymentRecord payPalSubPaymentRecord = new PayPalSubPaymentRecord();
        payPalSubPaymentRecord.setPaymentId(saleModel.getId());
        payPalSubPaymentRecord.setSubscriptionId(billingAgreementId);
        if (payPalLogicSubscription != null) {
            payPalSubPaymentRecord.setPaypalLogicSubId(payPalLogicSubscription.getId());
            payPalSubPaymentRecord.setLoginName(payPalLogicSubscription.getLoginName());
            payPalSubPaymentRecord.setUserId(payPalLogicSubscription.getUserId());
        } else {
            String custom = saleModel.getCustom();
            if (custom != null) {
                JSONObject entries = JSONUtil.parseObj(custom);
                payPalSubPaymentRecord.setUserId(Long.valueOf(entries.getStr("userId")));
                User userById = userService.getUserById(payPalSubPaymentRecord.getUserId());
                if (userById != null) {
                    payPalSubPaymentRecord.setLoginName(userById.getLoginName());
                    log.info("user not found {}", payPalSubPaymentRecord.getUserId());
                }
            }
        }

        payPalSubPaymentRecord.setState(saleModel.getState());
        Amount amount = saleModel.getAmount();
        if (amount != null) {
            payPalSubPaymentRecord.setTotal(amount.getTotal());
            payPalSubPaymentRecord.setCurrency(amount.getCurrency());
            Details details = amount.getDetails();
            payPalSubPaymentRecord.setSubtotal(details.getSubtotal());

            payPalSubPaymentRecord.setFee(details.getFee());
        }
        Currency transactionFee = saleModel.getTransactionFee();
        if (transactionFee != null) {
            payPalSubPaymentRecord.setFee(transactionFee.getValue());
        }
        payPalSubPaymentRecord.setPayCreateTime(saleModel.getCreateTime());
        payPalSubPaymentRecord.setReasonCode(saleModel.getReasonCode());
        payPalSubPaymentRecord.setCreateTime(LocalDateTime.now());
        this.save(payPalSubPaymentRecord);
        log.info("{} record created {} {}", saleModel.getId(), payPalSubPaymentRecord.getId(), payPalLogicSubscription == null);
        return payPalLogicSubscription == null ? null : payPalSubPaymentRecord;
    }

    @Override
    public PayPalSubPaymentRecord queryByPaymentId(String paymentId) {
        return this.lambdaQuery().eq(PayPalSubPaymentRecord::getPaymentId, paymentId).one();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRefundId(String paymentId, String refundId) {
        this.lambdaUpdate().eq(PayPalSubPaymentRecord::getPaymentId, paymentId)
                .set(PayPalSubPaymentRecord::getRefundId, refundId)
                .set(PayPalSubPaymentRecord::getUpdateTime, LocalDateTime.now())
                .update();
    }
}
