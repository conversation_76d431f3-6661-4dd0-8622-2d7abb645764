package com.lx.pl.pay.paypal.model;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.RandomUtil;
import com.lx.pl.enums.PayPalErrorCode;
import com.lx.pl.exception.PayPalException;
import com.paypal.base.rest.APIContext;
import com.paypal.base.rest.HttpMethod;
import com.paypal.base.rest.PayPalRESTException;
import com.paypal.base.rest.PayPalResource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 */
public class CustomPaypalResource extends PayPalResource {
    static protected Logger log = LoggerFactory.getLogger("paypal-pay-msg");

    static <T> T executeWithRetry(APIContext apiContext, HttpMethod httpMethod, String resourcePath, String payload, Class<T> responseType) throws PayPalRESTException {
        int retryCount = 3;
        while (retryCount > 0) {
            try {
                T t = configureAndExecute(apiContext, httpMethod, resourcePath, payload, responseType);
                log.info("{} {} success, param: {}, response: {}", httpMethod.name(), resourcePath, payload, t);
                return t;
            } catch (Exception e) {
                log.error("{} {} Retry attempt failed, param: {}, retries left: {}", httpMethod.name(), resourcePath, payload, retryCount, e);
                if (e instanceof PayPalRESTException) {
                    throw new PayPalException(PayPalErrorCode.CALL_PAYPAL_API_ERROR);
                }
                ThreadUtil.sleep(RandomUtil.randomInt(100, 300));
                retryCount--;
                if (retryCount == 0) {
                    throw e;
                }
                // 记录重试日志,
            }
        }
        return null;
    }
}
