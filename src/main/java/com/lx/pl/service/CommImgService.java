package com.lx.pl.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.csp.sentinel.util.StringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.collect.Lists;
import com.lx.pl.constant.LogicConstants;
import com.lx.pl.db.mysql.community.activity.CommActivityRewardSelection;
import com.lx.pl.db.mysql.community.entity.*;
import com.lx.pl.db.mysql.community.mapper.BannerImgIosMapper;
import com.lx.pl.db.mysql.community.mapper.BannerImgMapper;
import com.lx.pl.db.mysql.gen.entity.PromptFile;
import com.lx.pl.db.mysql.gen.entity.PublicFileReview;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.db.mysql.gen.mapper.PromptFileMapper;
import com.lx.pl.db.mysql.gen.mapper.PublicFileReviewMapper;
import com.lx.pl.db.mysql.gen.repository.CommFileRepository;
import com.lx.pl.dto.*;
import com.lx.pl.enums.BannerImgStatusEnum;
import com.lx.pl.enums.LogicErrorCode;
import com.lx.pl.enums.PublicType;
import com.lx.pl.exception.LogicException;
import com.lx.pl.exception.ServerInternalException;
import com.lx.pl.util.AESUtil;
import com.lx.pl.util.DateUtils;
import com.lx.pl.util.JsonUtils;
import com.lx.pl.vo.BannerImgIosVO;
import com.lx.pl.vo.BannerImgVO;
import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.TextCriteria;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.lx.pl.service.FileScoreService.FILE_SCORE_KEY;


@Service
@Slf4j
public class CommImgService {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private CommFileRepository commFileRepository;
    @Lazy
    @Autowired
    private CommLikeService commLikeService;

    @Autowired
    private CommFollowService commFollowService;

    @Autowired
    private LoadBalanceService loadBalanceService;

    @Autowired
    private PromptFileMapper promptFileMapper;
    @Resource
    private RedisService<String> redisService;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private FileScoreService fileScoreService;
    @Resource
    private BannerImgMapper bannerImgMapper;

    @Autowired
    private CommActivityRewardSelectionService commActivityRewardSelectionService;

    @Autowired
    private PublicFileReviewMapper publicFileReviewMapper;

    @Autowired
    private BannerImgIosMapper bannerImgIosMapper;
    @Resource
    private VipTypeCacheService vipTypeCacheService;

    /**
     * 删除社区图片
     *
     * @param commFileId
     * @return
     */
    public Boolean deleteCommFileById(String commFileId, User user) {
        Query query = new Query();
        query.addCriteria(Criteria.where("accountInfo.userId").is(user.getId()).and("id").is(commFileId));
        CommFile commFile1 = mongoTemplate.findOne(query, CommFile.class);
        if (commFile1 == null) {
            throw new LogicException(LogicErrorCode.NOT_FOUND_COMM_FILE);
        }
        if (!Objects.isNull(commFile1.getPrizeLevel())) {
            log.error("删除获奖图片失败，该图片已获奖");
            throw new LogicException(LogicErrorCode.NOT_DELETE_WIN_IMAGE);
        }
        try {
            //用户发布到社区的图片数-1
            mongoTemplate.updateFirst(
                    new Query(Criteria.where("accountInfo.userId").is(user.getId())),
                    new Update().inc("publicImgNums", -1),
                    CommUser.class
            );


            List<CommFile> allAndRemove = mongoTemplate.findAllAndRemove(query, CommFile.class);
            if (CollUtil.isNotEmpty(allAndRemove)) {
                // 清楚redis value
                for (CommFile commFile : allAndRemove) {
                    // 组装value
                    String id = commFile.getId();
                    long timestamp = commFile.getCreateTime().toInstant(ZoneOffset.UTC).getEpochSecond();
                    String value = id + "_" + timestamp + "_" + commFile.getTrendingScore();
                    redisService.zrem(FILE_SCORE_KEY, value);
                }
            }
            return CollUtil.isNotEmpty(allAndRemove);
        } catch (Exception e) {
            log.error("删除社区图片报错，图片id为：{}", commFileId, e);
            throw new ServerInternalException("Failed to delete community image");
        }
    }

    /**
     * 分页查询社区图片
     *
     * @param lastFileId
     * @param lastLikeId
     * @param b
     * @param pageSize
     * @param tags
     * @param collationName
     * @param vagueKey
     * @param user
     * @return
     */
    public CommPageInfo<CommFile> getCommImgByPage(String lastFileId, String lastLikeId, boolean fromV2, Long lastScoreIndex, Integer pageSize, List<String> tags, String collationName, String vagueKey, User user) throws Exception {
        Query query = new Query();
        Boolean returnFlag = false;
        Long lastIndex = null;
        String lastId = "";
        // 设置排序规则
        if (StringUtil.isNotBlank(collationName)) {
            switch (collationName) {
                case "Hot":
                    buildHotQuery(lastFileId, tags, vagueKey, query);
                    break;
                case "Trending":
                    lastIndex = buildTrendingQueryNew(lastFileId, lastScoreIndex, pageSize, tags, vagueKey, query);
                    break;
                case "Featured":
                    /**
                     * 按照时间倒序进行排序
                     */
                    query.addCriteria(Criteria.where("featured").is(Boolean.TRUE));

                    // 如果提供了标签，则按标签进行过滤
                    if (!CollectionUtils.isEmpty(tags)) {
                        query.addCriteria(Criteria.where("tags").in(tags));
                    }

                    //模糊搜索关键字 全文索引
                    if (StringUtil.isNotBlank(vagueKey)) {
                        TextCriteria textCriteria = TextCriteria.forDefaultLanguage().matching(vagueKey);
                        query.addCriteria(textCriteria);
                    } else {
                        // 游标分页：基于上一页最后一条记录的 ID
                        if (StringUtil.isNotBlank(lastFileId)) {
                            query.addCriteria(Criteria.where("id").lt(new ObjectId(lastFileId))); // 仅获取 ID 小于游标的记录
                        }

                        query.with(Sort.by(Sort.Direction.DESC, "id"));
                    }

                    break;
                case "Likes":
                    /**
                     * 查询用户点赞过的图片集合
                     */
                    List<CommLike> commLikeList = commLikeService.findLikeCommFileIdByUserId(lastLikeId, pageSize, tags, vagueKey, user.getId());
                    if (!CollectionUtils.isEmpty(commLikeList)) {
                        //likes 按照点赞的id为最后一条
                        lastId = CollectionUtils.isEmpty(commLikeList) ? " " : commLikeList.get(commLikeList.size() - 1).getId();
                        List<String> commFileIds = commLikeList.stream().map(CommLike::getFileId).collect(Collectors.toList());

                        query.addCriteria(Criteria.where("id").in(commFileIds));
                    } else {
                        returnFlag = true;
                    }

                    break;
                case "New":
                    /**
                     * 按照时间倒序进行排序
                     */
                    // 如果提供了标签，则按标签进行过滤
                    if (!CollectionUtils.isEmpty(tags)) {
                        query.addCriteria(Criteria.where("tags").in(tags));
                    }

                    //模糊搜索关键字 全文索引
                    if (StringUtil.isNotBlank(vagueKey)) {
                        TextCriteria textCriteria = TextCriteria.forDefaultLanguage().matching(vagueKey);
                        query.addCriteria(textCriteria);
                    } else {
                        // 游标分页：基于上一页最后一条记录的 ID
                        if (StringUtil.isNotBlank(lastFileId)) {
                            query.addCriteria(Criteria.where("id").lt(new ObjectId(lastFileId))); // 仅获取 ID 小于游标的记录
                        }
                    }
                    query.with(Sort.by(Sort.Direction.DESC, "id"));
                    break;
                default:
                    query.with(Sort.by(Sort.Direction.DESC, "id"));
                    break;
            }
        }

        if (returnFlag) {
            return buildCommPageInfo(pageSize, null, "");
        }

        // 设置分页
        query.limit(pageSize);
        List<CommFile> commFileList = mongoTemplate.find(query, CommFile.class);

        //处理当前用户是否点赞了图片
        handleUserLikes(commFileList, user);
        //处理当前用户是否举报了图片
        handReportedImgs(commFileList, user);
        // 处理评分字段
        handleScoreField(commFileList, lastIndex);
        //处理当前用户是否关注了图片作者
        handleUserFollowedAuthor(commFileList, user);
        //处理图片作者的会员信息
        handleAuthorVipInfo(commFileList);

        //Hot 的 lastId 得拼上点赞数
        if (!CollectionUtils.isEmpty(commFileList)) {
            CommFile commFile = commFileList.get(commFileList.size() - 1);
            if ("New".equals(collationName)) {
                lastId = commFile.getId();
            }
            if ("Featured".equals(collationName)) {
                lastId = commFile.getId();
            }
            if ("Trending".equals(collationName)) {
                lastId = commFile.getFileLikeNums() + "#" + commFile.getId();
            }
            if ("Hot".equals(collationName)) {
                lastId = commFile.getFileLikeNums() + "#" + commFile.getId();
            }
            if ("Likes".equals(collationName)) {
                commFileList = commFileList.stream()
                        .sorted(Comparator.comparing(item -> item.getLikeId(), Comparator.reverseOrder()))
                        .collect(Collectors.toList());
            }
        }

        return buildCommPageInfo(pageSize, commFileList, lastId);
    }

    private void handleAuthorVipInfo(List<CommFile> commFileList) {
        if (CollectionUtils.isEmpty(commFileList)) {
            return;
        }

        //根据用户id分组
        Map<Long, List<CommFile>> userIdCommFileMap = commFileList.stream()
                .collect(Collectors.groupingBy(commFile -> commFile.getAccountInfo().getUserId()));

        //获取用户会员等级信息
        Map<Long, String> vipTypeMap = vipTypeCacheService.getVipType(new ArrayList<>(userIdCommFileMap.keySet()));
        userIdCommFileMap.forEach((userId, commFiles) -> commFiles.forEach(commFile -> commFile.getAccountInfo().setPlanLevel(vipTypeMap.get(userId))));
    }

    private void handleUserFollowedAuthor(List<CommFile> commFileList, User user) {
        if (CollectionUtils.isEmpty(commFileList)) {
            return;
        }

        commFileList.forEach(commFile -> commFile.setFollowedAuthor(commFollowService.judgeTargetUserFollow(commFile.getAccountInfo().getUserId(), user)));
    }

    /**
     * 分页查询社区图片（游客模式）
     */
    public CommPageInfo<CommFile> getCommImgByPageForVisitor(String lastFileId, boolean fromV2, Long lastScoreIndex, Integer pageSize, List<String> tags, String collationName, String vagueKey) throws Exception {
        Query query = new Query();
        Boolean returnFlag = false;
        Long lastIndex = null;
        String lastId = "";
        // 设置排序规则
        if (StringUtil.isNotBlank(collationName)) {
            switch (collationName) {
                case "Hot":
                    buildHotQuery(lastFileId, tags, vagueKey, query);
                    break;
                case "Trending":
                    lastIndex = buildTrendingQueryNew(lastFileId, lastScoreIndex, pageSize, tags, vagueKey, query);
                    break;
                case "Featured":
                    /**
                     * 按照时间倒序进行排序
                     */
                    query.addCriteria(Criteria.where("featured").is(Boolean.TRUE));

                    // 如果提供了标签，则按标签进行过滤
                    if (!CollectionUtils.isEmpty(tags)) {
                        query.addCriteria(Criteria.where("tags").in(tags));
                    }

                    //模糊搜索关键字 全文索引
                    if (StringUtil.isNotBlank(vagueKey)) {
                        TextCriteria textCriteria = TextCriteria.forDefaultLanguage().matching(vagueKey);
                        query.addCriteria(textCriteria);
                    } else {
                        // 游标分页：基于上一页最后一条记录的 ID
                        if (StringUtil.isNotBlank(lastFileId)) {
                            query.addCriteria(Criteria.where("id").lt(new ObjectId(lastFileId))); // 仅获取 ID 小于游标的记录
                        }

                        query.with(Sort.by(Sort.Direction.DESC, "id"));
                    }

                    break;
                case "New":
                    /**
                     * 按照时间倒序进行排序
                     */
                    // 如果提供了标签，则按标签进行过滤
                    if (!CollectionUtils.isEmpty(tags)) {
                        query.addCriteria(Criteria.where("tags").in(tags));
                    }

                    //模糊搜索关键字 全文索引
                    if (StringUtil.isNotBlank(vagueKey)) {
                        TextCriteria textCriteria = TextCriteria.forDefaultLanguage().matching(vagueKey);
                        query.addCriteria(textCriteria);
                    } else {
                        // 游标分页：基于上一页最后一条记录的 ID
                        if (StringUtil.isNotBlank(lastFileId)) {
                            query.addCriteria(Criteria.where("id").lt(new ObjectId(lastFileId))); // 仅获取 ID 小于游标的记录
                        }
                    }
                    query.with(Sort.by(Sort.Direction.DESC, "id"));
                    break;
                default:
                    query.with(Sort.by(Sort.Direction.DESC, "id"));
                    break;
            }
        }

        if (returnFlag) {
            return buildCommPageInfo(pageSize, null, "");
        }

        // 设置分页
        query.limit(pageSize);
        List<CommFile> commFileList = mongoTemplate.find(query, CommFile.class);

        // 处理评分字段
        handleScoreField(commFileList, lastIndex);

        //Hot 的 lastId 得拼上点赞数
        if (!CollectionUtils.isEmpty(commFileList)) {
            CommFile commFile = commFileList.get(commFileList.size() - 1);
            if ("New".equals(collationName)) {
                lastId = commFile.getId();
            }
            if ("Featured".equals(collationName)) {
                lastId = commFile.getId();
            }
            if ("Trending".equals(collationName)) {
                lastId = commFile.getFileLikeNums() + "#" + commFile.getId();
            }
            if ("Hot".equals(collationName)) {
                lastId = commFile.getFileLikeNums() + "#" + commFile.getId();
            }
            if ("Likes".equals(collationName)) {
                commFileList = commFileList.stream()
                        .sorted(Comparator.comparing(item -> item.getLikeId(), Comparator.reverseOrder()))
                        .collect(Collectors.toList());
            }
        }

        return buildCommPageInfo(pageSize, commFileList, lastId);
    }

    private void handleScoreField(List<CommFile> commFileList, Long lastScoreIndex) {
        if (!CollectionUtils.isEmpty(commFileList)) {
            commFileList.get(commFileList.size() - 1).setLastScoreIndex(lastScoreIndex);
        }
    }

    private Long buildTrendingQueryNew(String lastFileId, Long lastScoreIndex, Integer pageSize, List<String> tags, String vagueKey, Query query) {
        /**
         * 按照点赞数进行排序
         */
//        Map<String, String> fileScoreValueMap = new HashMap<>();
//        query.addCriteria(Criteria.where("featured").is(Boolean.TRUE));
        Long rtnIndex = null;
        List<Object> ids = new ArrayList<>();
        if (CollUtil.isEmpty(tags) && StringUtil.isBlank(vagueKey)) {
            long index = 0;
            Set<ZSetOperations.TypedTuple<String>> typedTuples = null;
            if (lastScoreIndex != null) {
//                index = redisService.getZSetReverseIndex(FILE_SCORE_KEY, scoreValue);
                index = lastScoreIndex;
                rtnIndex = index + pageSize;
                typedTuples = redisService.zrevrangeWithScores(FILE_SCORE_KEY, index + 1, rtnIndex);
            } else {
                rtnIndex = index + pageSize - 1;
                typedTuples = redisService.zrevrangeWithScores(FILE_SCORE_KEY, index, rtnIndex);
            }
            // 转id数组
            if (CollUtil.isNotEmpty(typedTuples)) {
                List<ObjectId> fileIds = typedTuples.stream()
                        .filter(tuple -> tuple.getValue() != null)
                        .map(tuple -> {
                            String value = tuple.getValue();
                            String id = value.split("_")[0];
//                            fileScoreValueMap.put(id, tuple.getValue());
                            return new ObjectId(id);
                        })
                        .collect(Collectors.toList());
                query.addCriteria(Criteria.where("id").in(fileIds));
            } else {
                if (lastFileId != null) {
                    // 游标分页：基于上一页最后一条记录的 ID
                    if (StringUtil.isNotBlank(lastFileId)) {
                        String[] parts = lastFileId.split("#");
                        if (parts.length == 2) {
                            // 从 lastFileId 中解析 fileLikeNums 和 id
                            Integer lastFileLikeNums = Integer.parseInt(parts[0]); // 第一个字段是点赞数
                            String lastHotFileId = parts[1]; // 第二个字段是 ID

                            // 添加复合边界条件
                            query.addCriteria(new Criteria().orOperator(
                                    // 条件 1：点赞数小于上一页最后一条记录的点赞数
                                    Criteria.where("fileLikeNums").lt(lastFileLikeNums),
                                    // 条件 2：点赞数相同，但 ID 小于上一页最后记录的 ID
                                    new Criteria().andOperator(
                                            Criteria.where("fileLikeNums").is(lastFileLikeNums),
                                            Criteria.where("id").lt(new ObjectId(lastHotFileId))
                                    )
                            ));
                        }
                    }
                }
            }
        } else {
            // 如果提供了标签，则按标签进行过滤
            if (!CollectionUtils.isEmpty(tags)) {
                query.addCriteria(Criteria.where("tags").in(tags));
            }

            if (StringUtil.isNotBlank(vagueKey)) {
                TextCriteria textCriteria = TextCriteria.forDefaultLanguage().matching(vagueKey);
                query.addCriteria(textCriteria);
            }
            // 游标分页：基于上一页最后一条记录的 ID
            if (StringUtil.isNotBlank(lastFileId)) {
                String[] parts = lastFileId.split("#");
                if (parts.length == 2) {
                    // 从 lastFileId 中解析 fileLikeNums 和 id
                    Integer lastFileLikeNums = Integer.parseInt(parts[0]); // 第一个字段是点赞数
                    String lastHotFileId = parts[1]; // 第二个字段是 ID

                    // 添加复合边界条件
                    query.addCriteria(new Criteria().orOperator(
                            // 条件 1：点赞数小于上一页最后一条记录的点赞数
                            Criteria.where("trendingScoreTime").lt(lastFileLikeNums),
                            // 条件 2：点赞数相同，但 ID 小于上一页最后记录的 ID
                            new Criteria().andOperator(
                                    Criteria.where("trendingScoreTime").is(lastFileLikeNums),
                                    Criteria.where("id").lt(new ObjectId(lastHotFileId))
                            )
                    ));
                }
            }
        }
        query.with(Sort.by(Sort.Direction.DESC, "trendingScoreTime"));
        query.with(Sort.by(Sort.Direction.DESC, "id"));
        return rtnIndex;
    }

    private Query buildHotQuery(String lastFileId, List<String> tags, String vagueKey, Query query) {
        /**
         * 按照点赞数进行排序
         */
        query.addCriteria(Criteria.where("featured").is(Boolean.TRUE));

        // 如果提供了标签，则按标签进行过滤
        if (!CollectionUtils.isEmpty(tags)) {
            query.addCriteria(Criteria.where("tags").in(tags));
        }

        //模糊搜索关键字 全文索引
        if (StringUtil.isNotBlank(vagueKey)) {
            TextCriteria textCriteria = TextCriteria.forDefaultLanguage().matching(vagueKey);
            query.addCriteria(textCriteria);
        } else {
            // 添加筛选条件：createTime 大于 30 天前
            query.addCriteria(Criteria.where("createTime").gte(DateUtils.getNumsDayDateBefore(new Date(), -30)));

            query.with(Sort.by(Sort.Direction.DESC, "fileLikeNums"));
            query.with(Sort.by(Sort.Direction.DESC, "id"));

            // 游标分页：基于上一页最后一条记录的 ID
            if (StringUtil.isNotBlank(lastFileId)) {
                String[] parts = lastFileId.split("#");
                if (parts.length == 2) {
                    // 从 lastFileId 中解析 fileLikeNums 和 id
                    Integer lastFileLikeNums = Integer.parseInt(parts[0]); // 第一个字段是点赞数
                    String lastHotFileId = parts[1]; // 第二个字段是 ID

                    // 添加复合边界条件
                    query.addCriteria(new Criteria().orOperator(
                            // 条件 1：点赞数小于上一页最后一条记录的点赞数
                            Criteria.where("fileLikeNums").lt(lastFileLikeNums),
                            // 条件 2：点赞数相同，但 ID 小于上一页最后记录的 ID
                            new Criteria().andOperator(
                                    Criteria.where("fileLikeNums").is(lastFileLikeNums),
                                    Criteria.where("id").lt(new ObjectId(lastHotFileId))
                            )
                    ));
                }
            }
        }
        return query;
    }

    public CommPageInfo<CommFile> getCommImgByCommonPage(String lastFileId, Integer pageSize) throws Exception {
        Query query = new Query();
        /**
         * 按照时间倒序进行排序
         */
        query.addCriteria(Criteria.where("featured").is(Boolean.TRUE));


        // 游标分页：基于上一页最后一条记录的 ID
        if (StringUtil.isNotBlank(lastFileId)) {
            query.addCriteria(Criteria.where("id").lt(new ObjectId(lastFileId))); // 仅获取 ID 小于游标的记录
        }

        query.with(Sort.by(Sort.Direction.DESC, "id"));
        // 设置分页
        query.limit(pageSize);

        List<CommFile> commFileList = mongoTemplate.find(query, CommFile.class);

        String lastId = "";
        if (!CollectionUtils.isEmpty(commFileList)) {
            CommFile lastCommFile = commFileList.get(commFileList.size() - 1);
            lastId = lastCommFile.getId();

            //查询模型相关的信息
            Map<String, ModelInformation.ModelAbout> modelAboutMap = new HashMap<>();
            List<ModelInformation.ModelAbout> modelAboutList = loadBalanceService.listModels();
            if (!CollectionUtils.isEmpty(modelAboutList)) {
                modelAboutMap = modelAboutList.stream().collect(Collectors.toMap(ModelInformation.ModelAbout::getModelId, Function.identity()));
            }

            for (CommFile commFile : commFileList) {
                GenGenericPara paras = JsonUtils.fromString(commFile.getGenInfo(), GenGenericPara.class);
                ModelInformation.ModelAbout modelAbout = modelAboutMap.get(paras.getModel_id());
                if (!Objects.isNull(modelAbout)) {
                    commFile.setModelAvatar(modelAbout.getModelAvatar());
                    commFile.setModelDisplay(modelAbout.getModelDisplay());
                }
            }
        }

        return buildCommPageInfo(pageSize, commFileList, lastId);
    }

    public CommPageInfo<CommFile> getPersonalCommImgByPage(String lastFileId, Integer pageSize, Long userId, String vagueKey, List<String> tags, User user) throws Exception {

        Query query = new Query();

        Long commUserId = !Objects.isNull(userId) ? userId : user.getId();
        query.addCriteria(Criteria.where("accountInfo.userId").is(commUserId)); // 精确匹配当前用户id

        // 如果提供了标签，则按标签进行过滤
        if (!CollectionUtils.isEmpty(tags)) {
            query.addCriteria(Criteria.where("tags").in(tags));
        }

        //模糊搜索关键字
        if (StringUtil.isNotBlank(vagueKey)) {
            TextCriteria textCriteria = TextCriteria.forDefaultLanguage().matching(vagueKey);
            query.addCriteria(textCriteria);
        } else {
            // 游标分页：基于上一页最后一条记录的 ID
            if (StringUtil.isNotBlank(lastFileId)) {
                query.addCriteria(Criteria.where("id").lt(new ObjectId(lastFileId))); // 仅获取 ID 小于游标的记录
            }

            query.with(Sort.by(Sort.Direction.DESC, "id"));
        }

        // 设置分页
        query.limit(pageSize);
        List<CommFile> commFileList = mongoTemplate.find(query, CommFile.class);
        String lastId = CollectionUtils.isEmpty(commFileList) ? "" : commFileList.get(commFileList.size() - 1).getId();

        //处理当前用户是否点赞了图片
        handleUserLikes(commFileList, user);
        //处理当前用户是否举报了图片
        handReportedImgs(commFileList, user);

        return buildCommPageInfo(pageSize, commFileList, lastId);
    }

    /**
     * 查询社区图片详情
     *
     * @param commFileId
     * @return
     */
    public CommFileDetail getCommFileDetailById(String commFileId) {
        CommFileDetail commFileDetail = new CommFileDetail();

        try {
            CommFile commFile = getCommFileById(commFileId);
            if (!Objects.isNull(commFile)) {
                BeanUtils.copyProperties(commFile, commFileDetail);

                //查询模型相关的信息
                Map<String, ModelInformation.ModelAbout> modelAboutMap = new HashMap<>();
                List<ModelInformation.ModelAbout> modelAboutList = loadBalanceService.listModels();
                if (!CollectionUtils.isEmpty(modelAboutList)) {
                    modelAboutMap = modelAboutList.stream().collect(Collectors.toMap(ModelInformation.ModelAbout::getModelId, Function.identity()));
                }
                GenGenericPara paras = JsonUtils.fromString(commFile.getGenInfo(), GenGenericPara.class);
                ModelInformation.ModelAbout modelAbout = modelAboutMap.get(paras.getModel_id());

                if (!Objects.isNull(modelAbout)) {
                    commFileDetail.setModelAvatar(modelAbout.getModelAvatar());
                    commFileDetail.setModelDisplay(modelAbout.getModelDisplay());
                }
            }

        } catch (Exception e) {
            log.error("获取图片：{} 详情失败", commFileId, e);
            throw new ServerInternalException("Failed to get detail");
        }

        return commFileDetail;
    }

    public CommFileDetail getCommFileParticularById(String commFileId, User user) {
        CommFileDetail commFileDetail = new CommFileDetail();

        try {
            CommFile commFile = getCommFileById(commFileId);

            if (!Objects.isNull(commFile)) {
                List<CommFile> commFileList = new ArrayList<>();
                commFileList.add(commFile);

                AccountInfo accountInfo = commFile.getAccountInfo();

                //处理当前用户是否点赞了图片
                handleUserLikes(commFileList, user);
                //处理当前用户是否举报了图片
                handReportedImgs(commFileList, user);

                commFile = commFileList.get(0);

                BeanUtils.copyProperties(commFile, commFileDetail);

                if (!Objects.isNull(commFileDetail)) {
                    commFileDetail.setFollowed(commFollowService.judgeTargetUserFollow(accountInfo.getUserId(), user));
                }
            }
        } catch (Exception e) {
            log.error("获取用户社区图片：{} 详情失败", commFileId, e);
            throw new ServerInternalException("Failed to get detail");
        }

        return commFileDetail;
    }


    /**
     * 查询社区图片
     *
     * @param commFileId
     * @return
     */
    public CommFile getCommFileById(String commFileId) {
        try {
            Query query = new Query();
            query.addCriteria(Criteria.where("id").is(commFileId));
            CommFile commFile = mongoTemplate.findOne(query, CommFile.class);
            return commFile;
        } catch (Exception e) {
            log.error("查询社区图片报错，图片id为：{}", commFileId, e);
            return null;
        }
    }


    /**
     * 更新图片的用户名称
     *
     * @param userId
     * @param newUsername
     */
    public void updateUsernameByUserId(Long userId, String newUsername) {
        try {
            // 构建查询条件，匹配 accountInfo.userId 或 replyAccInfo.userId
            Query query = new Query();
            query.addCriteria(new Criteria().orOperator(
                    Criteria.where("accountInfo.userId").is(userId)
            ));

            // 更新 accountInfo.username
            Update update = new Update();
            update.set("accountInfo.userName", newUsername);

            // 执行更新
            UpdateResult result = mongoTemplate.updateMulti(query, update, CommFile.class);
        } catch (Exception e) {
            log.error("更新用户：{} 的用户名称：{}报错", userId, newUsername, e);
            throw new ServerInternalException("Failed to update username");
        }
    }

    /**
     * 更新图片的用户头像
     *
     * @param userId
     * @param newUserAvatarUrl
     */
    public void updateUserAvatarUrlByUserId(Long userId, String newUserAvatarUrl) {
        try {
            // 构建查询条件，匹配 accountInfo.userId 或 replyAccInfo.userId
            Query query = new Query();
            query.addCriteria(new Criteria().orOperator(
                    Criteria.where("accountInfo.userId").is(userId)
            ));

            // 更新 accountInfo.username 或 replyAccInfo.username
            Update update = new Update();
            update.set("accountInfo.userAvatarUrl", newUserAvatarUrl);

            // 执行更新
            UpdateResult result = mongoTemplate.updateMulti(query, update, CommFile.class);
        } catch (Exception e) {
            log.error("更新用户：{} 的用户头像：{}报错", userId, newUserAvatarUrl, e);
            throw new ServerInternalException("Failed to update user avatar");
        }
    }

    // 构建分页结果对象
    private CommPageInfo<CommFile> buildCommPageInfo(Integer pageSize, List<CommFile> commFileList, String lastId) throws Exception {
        CommPageInfo<CommFile> commPageInfo = new CommPageInfo<>();
        commPageInfo.setEncryptResult(AESUtil.encryptString(JsonUtils.writeToString(commFileList)));
        commPageInfo.setPageSize(pageSize);
        commPageInfo.setLastId(lastId);
        return commPageInfo;
    }

    // 构建分页结果对象
    private PromptPageInfo<CommFile> buildPromptPageInfo(Integer pageNum, Integer pageSize, List<CommFile> commFileList, Integer total) throws Exception {
        PromptPageInfo<CommFile> promptPageInfo = new PromptPageInfo<>();
        promptPageInfo.setEncryptResult(AESUtil.encryptString(JsonUtils.writeToString(commFileList)));
        promptPageInfo.setPageNum(pageNum);
        promptPageInfo.setPageSize(pageSize);
        promptPageInfo.setTotal(total);
        return promptPageInfo;
    }

    /**
     * @Description: 处理当前用户是否点赞了图片
     * @Param: [commFileList, user]
     * @return: void
     * @Author: senlin_he
     * @Date: 2024/11/19
     */
    public void handleUserLikes(List<CommFile> commFileList, User user) {
        if (!CollectionUtils.isEmpty(commFileList)) {
            List<String> fileIdList = commFileList.stream().map(CommFile::getId).collect(Collectors.toList());

            Query likeQuery = new Query();
            likeQuery.addCriteria(Criteria.where("ownerAcc.userId").is(user.getId()));
            likeQuery.addCriteria(Criteria.where("fileId").in(fileIdList));
            List<CommLike> commLikeList = mongoTemplate.find(likeQuery, CommLike.class);

            if (!CollectionUtils.isEmpty(commLikeList)) {
                Map<String, String> likeMap = commLikeList.stream().collect(Collectors.toMap(CommLike::getFileId, CommLike::getId));

                // 当前用户点赞了图片，则设置为true
                for (CommFile commFile : commFileList) {
                    if (likeMap.containsKey(commFile.getId())) {
                        commFile.setLiked(Boolean.TRUE);
                        commFile.setLikeId(new ObjectId(likeMap.get(commFile.getId())));
                    }
                }
            }
        }
    }

    /**
     * @Description: 处理当前用户是否举报了图片
     * @Param: [commFileList, user]
     * @return: void
     * @Author: senlin_he
     * @Date: 2024/11/19
     */
    private void handReportedImgs(List<CommFile> commFileList, User user) {
        List<String> commImgIdList = commFileList.stream()
                .map(CommFile::getId)
                .collect(Collectors.toList());

        Query reportQuery = new Query();
        reportQuery.addCriteria(Criteria.where("ownerAcc.userId").is(user.getId()));
        reportQuery.addCriteria(Criteria.where("fileId").in(commImgIdList));


        List<CommImgReport> commImgReportList = mongoTemplate.find(reportQuery, CommImgReport.class);

        if (!CollectionUtils.isEmpty(commImgReportList)) {
            Set<String> reportedCommentIds = commImgReportList.stream()
                    .map(CommImgReport::getFileId)
                    .collect(Collectors.toSet());

            for (CommFile commFile : commFileList) {
                if (reportedCommentIds.contains(commFile.getId())) {
                    commFile.setReported(Boolean.TRUE);
                }
            }
        }
    }

    public Boolean dealHistoryCommFile(String tableName) {
        Integer batchSize = 1000; // 每批次处理记录数
        Long lastId = null; // 游标起始点
        while (true) {
            // 查询数据，使用游标分页
            List<CommHistoryFile> records = promptFileMapper.getExplorePromptFileList(lastId, batchSize, tableName);
            if (records.isEmpty()) {
                break; // 没有更多数据，退出循环
            }

            List<CommFile> commFileList = new ArrayList<>();
            for (CommHistoryFile commHistoryFile : records) {
                CommFile commFile = new CommFile();
                commFile.setFileLikeNums(0);
                commFile.setFileCommentNums(0);
                commFile.setFileId(commHistoryFile.getPromptFileId());
                commFile.setFileUrl(commHistoryFile.getFileUrl());
                commFile.setThumbnailUrl(commHistoryFile.getThumbnailUrl());
                commFile.setHighThumbnailUrl(commHistoryFile.getHighThumbnailUrl());
                commFile.setTags(commHistoryFile.getTags());
                commFile.setCreateTime(LocalDateTime.now());
                AccountInfo accountInfo = new AccountInfo();
                accountInfo.setUserId(commHistoryFile.getUserId());
                accountInfo.setUserName(commHistoryFile.getUserName());
                accountInfo.setUserLoginName(commHistoryFile.getUserLoginName());
                accountInfo.setUserAvatarUrl(commHistoryFile.getUserAvatarUrl());
                accountInfo.setWhetherPro(Boolean.FALSE);
                commFile.setAccountInfo(accountInfo);
                commFile.setGenInfo(commHistoryFile.getGenInfo());
                commFile.setRealWidth(commHistoryFile.getRealWidth());
                commFile.setRealHeight(commHistoryFile.getRealHeight());
                commFile.setPrompt(commHistoryFile.getPrompt());
                commFile.setDescribe("");
                commFile.setChallenge(commHistoryFile.getChallenge());
                commFile.setPublicType(commHistoryFile.getPublicType());
                commFile.setDeleted(Boolean.FALSE);
                commFileList.add(commFile);

                //图片点赞数据+1
                mongoTemplate.updateFirst(
                        new Query(Criteria.where("accountInfo.userId").is(commHistoryFile.getUserId())),
                        new Update().inc("publicImgNums", 1),
                        CommUser.class
                );
            }

            commFileRepository.insert(commFileList);
            commFileList.clear();

            // 更新游标
            lastId = records.get(records.size() - 1).getExploreId();
        }
        log.info("文件表：{} 对应的exploer数据，数据迁移完毕", tableName);
        return Boolean.TRUE;
    }

    public Boolean changePublicType(String publicType, String commFileId, User user) {
        try {
            Query query = new Query();
            query.addCriteria(Criteria.where("id").is(commFileId).and("accountInfo.userId").is(user.getId()));

            Update update = new Update();
            update.set("publicType", publicType);

            // 执行更新
            UpdateResult result = mongoTemplate.updateMulti(query, update, CommFile.class);
        } catch (Exception e) {
            log.error("更新图片：{} 的publicType报错", commFileId, e);
            throw new ServerInternalException("Failed to update");
        }

        return Boolean.TRUE;
    }

    /**
     * 修改图片prompt可见性
     *
     * @param commFileIds
     * @return
     */
    public Boolean changePublicTypeBatchSegmentation(String publicType, List<String> commFileIds, User user) {
        List<List<String>> partition = Lists.partition(commFileIds, 20);
        for (List<String> list : partition) {
            changePublicTypeBatch(publicType, list, user);
        }
        return Boolean.TRUE;
    }

    public void changePublicTypeBatch(String publicType, List<String> commFileIds, User user) {
        try {
            Query query = new Query();
            query.addCriteria(Criteria.where("id").in(commFileIds).and("accountInfo.userId").is(user.getId()));

            Update update = new Update();
            update.set("publicType", publicType);

            // 执行更新
            UpdateResult result = mongoTemplate.updateMulti(query, update, CommFile.class);
        } catch (Exception e) {
            log.error("更新图片：{} 的publicType报错", commFileIds, e);
            throw new ServerInternalException("Failed to update");
        }
    }


    /**
     * 删除社区图片(批量)
     *
     * @param commFileIds
     * @return
     */
    public Boolean deleteCommFileBatchSegmentation(List<String> commFileIds, User user) {
        List<List<String>> partition = Lists.partition(commFileIds, 20);
        for (List<String> list : partition) {
            deleteCommFileBatch(list, user);
        }
        return Boolean.TRUE;
    }

    public Boolean deleteCommFileBatch(List<String> commFileIds, User user) {
        try {
            Query query = new Query();
            query.addCriteria(Criteria.where("id").in(commFileIds).and("accountInfo.userId").is(user.getId()).and("prizeLevel").is(null));

            Long deletedCount = mongoTemplate.remove(query, CommFile.class).getDeletedCount();

            //用户发布到社区的图片数-deletedCount
            mongoTemplate.updateFirst(
                    new Query(Criteria.where("accountInfo.userId").is(user.getId())),
                    new Update().inc("publicImgNums", -deletedCount),
                    CommUser.class
            );

            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("删除社区图片报错，图片id为：{}", commFileIds, e);
            throw new ServerInternalException("Failed to delete community image");
        }
    }


    public Map<String, String> getCommFileUrlListByCommFileIds(List<String> fileIds) {
        try {
            Map<String, String> fileMap = new HashMap<>();

            if (CollectionUtils.isEmpty(fileIds)) {
                return fileMap;
            }

            Query query = new Query();
            query.addCriteria(Criteria.where("id").in(fileIds));
            query.fields().include("id").include("miniThumbnailUrl").include("thumbnailUrl");
            List<CommFile> commFileList = mongoTemplate.find(query, CommFile.class);

            //如果mini缩略图为空，则使用thumbnailUrl
            if (!CollectionUtils.isEmpty(commFileList)) {
                fileMap = commFileList.stream()
                        .collect(Collectors.toMap(
                                CommFile::getId,
                                file -> StringUtil.isNotBlank(file.getMiniThumbnailUrl()) ? file.getMiniThumbnailUrl() : file.getThumbnailUrl()
                        ));
            }
            return fileMap;
        } catch (Exception e) {
            log.error("查询社区图片报错，图片ids为：{}", String.valueOf(fileIds), e);
            return null;
        }
    }

    public Boolean shareOrRemix(String commFileId, String type, User user) {
        RLock lock = redissonClient.getLock("file:sr:" + commFileId);
        try {
            lock.lock();
            CommFile commFile = this.getCommFileById(commFileId);
            if (!Objects.isNull(commFile)) {
//                int interactionScore = fileScoreService.calculateInteractionScore(commFile.getFileLikeNums(), commFile.getRemixNums(), commFile.getFileCommentNums(), commFile.getShareNums());
//                commFile.setFileCommentNums(commFile.getFileCommentNums() + 1);
//                fileScoreService.updateSingleFileTrendingScore(commFile, interactionScore);
                switch (type) {
                    case "share":
                        commFile.setShareNums((commFile.getShareNums() == null ? 0 : commFile.getShareNums()) + 1);
                        mongoTemplate.updateFirst(
                                new Query(Criteria.where("id").is(commFileId)),
                                new Update().inc("shareNums", 1), CommFile.class);

                        break;
                    case "remix":
                        commFile.setRemixNums((commFile.getRemixNums() == null ? 0 : commFile.getRemixNums()) + 1);
                        mongoTemplate.updateFirst(
                                new Query(Criteria.where("id").is(commFileId)),
                                new Update().inc("remixNums", 1), CommFile.class);
                        break;
                    default:
                        throw new ServerInternalException("type error");
                }
                fileScoreService.sendFlushMessage(commFileId, commFile.getTrendingScore());
            }
            return Boolean.TRUE;
        } finally {
            if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
            }
        }
    }


    /**
     * 分页查询参赛图片
     *
     * @param activityId
     * @param lastFileId
     * @param pageSize
     * @param collationName
     * @param user
     * @return
     */
    public CommPageInfo<CommFile> getCommActivityImgByPage(Long activityId, String lastFileId, Integer pageSize, String collationName, User user) throws Exception {
        Query query = new Query();

        query.addCriteria(Criteria.where("activityId").is(activityId));

        // 设置排序规则
        if (StringUtil.isNotBlank(collationName)) {
            switch (collationName) {
                case "Hot":
                    query.with(Sort.by(Sort.Direction.DESC, "fileLikeNums"));
                    query.with(Sort.by(Sort.Direction.DESC, "id"));

                    // 游标分页：基于上一页最后一条记录的 ID
                    if (StringUtil.isNotBlank(lastFileId)) {
                        String[] parts = lastFileId.split("#");
                        if (parts.length == 2) {
                            // 从 lastFileId 中解析 fileLikeNums 和 id
                            Integer lastFileLikeNums = Integer.parseInt(parts[0]); // 第一个字段是点赞数
                            String lastHotFileId = parts[1]; // 第二个字段是 ID

                            // 添加复合边界条件
                            query.addCriteria(new Criteria().orOperator(
                                    // 条件 1：点赞数小于上一页最后一条记录的点赞数
                                    Criteria.where("fileLikeNums").lt(lastFileLikeNums),
                                    // 条件 2：点赞数相同，但 ID 小于上一页最后记录的 ID
                                    new Criteria().andOperator(
                                            Criteria.where("fileLikeNums").is(lastFileLikeNums),
                                            Criteria.where("id").lt(new ObjectId(lastHotFileId))
                                    )
                            ));
                        }
                    }
                    break;
                case "Time":
                    // 游标分页：基于上一页最后一条记录的 ID
                    if (StringUtil.isNotBlank(lastFileId)) {
                        query.addCriteria(Criteria.where("id").lt(new ObjectId(lastFileId))); // 仅获取 ID 小于游标的记录
                    }
                    query.with(Sort.by(Sort.Direction.DESC, "id"));
                    break;
                default:
                    query.with(Sort.by(Sort.Direction.DESC, "id"));
                    break;
            }
        }

        // 设置分页
        query.limit(pageSize);
        List<CommFile> commFileList = mongoTemplate.find(query, CommFile.class);
        String lastId = CollectionUtils.isEmpty(commFileList) ? "" : commFileList.get(commFileList.size() - 1).getId();

        //处理当前用户是否点赞了图片
        handleUserLikes(commFileList, user);
        //处理当前用户是否举报了图片
        handReportedImgs(commFileList, user);
        //处理图片作者的会员信息
        handleAuthorVipInfo(commFileList);


        return buildCommPageInfo(pageSize, commFileList, lastId);
    }

    /**
     * 分页查询获奖图片
     *
     * @param activityId
     * @param pageNum
     * @param pageSize
     * @param user
     * @return
     */
    public PromptPageInfo<CommFile> getWinCommActivityImgByPage(Long activityId, Integer pageNum, Integer pageSize, User user) throws Exception {
        Query query = new Query();

        query.addCriteria(Criteria.where("activityId").is(activityId));

        // 添加条件：prizeLevel 不为空
        query.addCriteria(Criteria.where("prizeLevel").ne(null));

        query.with(Sort.by(
                Sort.Order.asc("prizeLevel"),
                Sort.Order.desc("id") // 防止相同prizeLevel时的排序歧义
        ));

        if (pageNum < 1) {
            pageNum = 1;
        }

        // 设置分页
        int skip = (pageNum - 1) * pageSize;
        query.skip(skip).limit(pageSize);
        List<CommFile> commFileList = mongoTemplate.find(query, CommFile.class);

        //处理当前用户是否点赞了图片
        handleUserLikes(commFileList, user);
        //处理当前用户是否举报了图片
        handReportedImgs(commFileList, user);
        //查询activityId的记录数

        //处理图片作者的会员信息
        handleAuthorVipInfo(commFileList);

        Long count = commActivityRewardSelectionService.count(new LambdaQueryWrapper<CommActivityRewardSelection>().eq(CommActivityRewardSelection::getActivityId, activityId));


        return buildPromptPageInfo(pageNum, pageSize, commFileList, count.intValue());
    }

    public Boolean deleteActivityImg(String fileId, Integer type, User user) {
        String loginName = user.getLoginName();

        // 如果是删除审核中的图片 则删除审核记录 图片状态从审核中更新为未公开
        if (PublicType.review.getValue().equals(type)) {

            LambdaQueryWrapper<PublicFileReview> lqw = new LambdaQueryWrapper<>();
            lqw.eq(PublicFileReview::getFileId, fileId);
            lqw.eq(PublicFileReview::getLoginName, loginName);
            publicFileReviewMapper.delete(lqw);

            LambdaUpdateWrapper<PromptFile> luw = new LambdaUpdateWrapper<>();
            luw.eq(PromptFile::getId, fileId);
            luw.eq(PromptFile::getLoginName, loginName);
            luw.set(PromptFile::getIsPublic, PublicType.undisclosed.getValue());
            promptFileMapper.update(null, luw);
            return Boolean.TRUE;

//            if (promptFileMapper.update(null, luw) > 0) {
//                return Boolean.TRUE;
//            }


        } else if (PublicType.publicity.getValue().equals(type)) { // 如果是社区图片则删除mongodb 数据 如果是获奖图片不允许删除
            Query query = new Query();
            query.addCriteria(Criteria.where("accountInfo.userId").is(user.getId()).and("fileId").is(fileId));
            CommFile commFile = mongoTemplate.findOne(query, CommFile.class);
            if (commFile == null) {
                return Boolean.FALSE;
            }
            if (!Objects.isNull(commFile.getPrizeLevel())) {
                log.error("删除获奖图片失败，该图片已获奖");
                throw new LogicException(LogicErrorCode.NOT_DELETE_WIN_IMAGE);
            }
            //用户发布到社区的图片数-1
            mongoTemplate.updateFirst(
                    new Query(Criteria.where("accountInfo.userId").is(user.getId())),
                    new Update().inc("publicImgNums", -1),
                    CommUser.class
            );


            List<CommFile> allAndRemove = mongoTemplate.findAllAndRemove(query, CommFile.class);
            if (CollUtil.isNotEmpty(allAndRemove)) {
                // 清楚redis value
                for (CommFile one : allAndRemove) {
                    // 组装value
                    String id = one.getId();
                    long timestamp = one.getCreateTime().toInstant(ZoneOffset.UTC).getEpochSecond();
                    String value = id + "_" + timestamp + "_" + one.getTrendingScore();
                    redisService.zrem(FILE_SCORE_KEY, value);
                }
            }
            return CollUtil.isNotEmpty(allAndRemove);
//            return mongoTemplate.remove(query, CommFile.class).getDeletedCount() > 0;
        }
        return Boolean.FALSE;
    }

//    public Boolean addQualityCommFile(String commFileId,User user){
//        try {
//            //图片点赞数据+1
//            mongoTemplate.updateFirst(
//                    new Query(Criteria.where("id").is(commFileId)),
//                    new Update().set("featured", Boolean.TRUE),
//                    CommFile.class
//            );
//        }catch (Exception e){
//            log.error("更新图片：{} 的quality报错", commFileId, e);
//            throw new ServerInternalException("Failed to update");
//        }
//        return Boolean.TRUE;
//    }

    /**
     * 获取社区首页banner图
     */
    public List<BannerImgVO> getBannerImgList() {
        List<BannerImgVO> bannerImgVOList = null;
        //先从缓存获取
        try {
            String data = redisService.get(LogicConstants.BANNER_IMG_CACHE_KEY);
            if (StringUtils.isNotBlank(data)) {
                bannerImgVOList = JsonUtils.writeToList(data, BannerImgVO.class);
            }
        } catch (Exception e) {
            log.error("从缓存中获取社区首页banner图数据异常, ", e);
        }

        if (!CollectionUtils.isEmpty(bannerImgVOList)) {
            return bannerImgVOList;
        }

        //缓存获取失败，从数据库获取
        List<BannerImg> bannerImgList = bannerImgMapper.selectList(new LambdaQueryWrapper<BannerImg>()
                .eq(BannerImg::getIsDeleted, LogicConstants.DB_VALID)
                .eq(BannerImg::getStatus, BannerImgStatusEnum.PUBLISHED.getCode())
                .orderByAsc(BannerImg::getSort));

        if (CollectionUtils.isEmpty(bannerImgList)) {
            return null;
        }

        bannerImgVOList = new ArrayList<>();
        for (BannerImg bannerImg : bannerImgList) {
            BannerImgVO bannerImgVO = new BannerImgVO();
            BeanUtils.copyProperties(bannerImg, bannerImgVO);
            bannerImgVOList.add(bannerImgVO);
        }

        //写入缓存
        try {
            redisService.set(LogicConstants.BANNER_IMG_CACHE_KEY, JsonUtils.writeToString(bannerImgVOList));
        } catch (Exception e) {
            log.error("社区首页banner图数据写入redis缓存异常", e);
        }

        return bannerImgVOList;
    }

    /**
     * 获取社区首页banner图
     */
    public List<BannerImgIosVO> getBannerIosImgList() {
        List<BannerImgIosVO> bannerImgVOList = null;
        //先从缓存获取
        try {
            String data = redisService.get(LogicConstants.BANNER_IMG_CACHE_KEY_IOS);
            if (StringUtils.isNotBlank(data)) {
                bannerImgVOList = JsonUtils.writeToList(data, BannerImgIosVO.class);
            }
        } catch (Exception e) {
            log.error("从缓存中获取社区首页banner图数据异常, ", e);
        }

        if (!CollectionUtils.isEmpty(bannerImgVOList)) {
            return bannerImgVOList;
        }

        //缓存获取失败，从数据库获取
        List<BannerImgIos> bannerImgList = bannerImgIosMapper.selectList(new LambdaQueryWrapper<BannerImgIos>()
                .eq(BannerImgIos::getIsDeleted, Boolean.FALSE)
                .eq(BannerImgIos::getStatus, Boolean.TRUE)
                .orderByAsc(BannerImgIos::getSort));

        if (CollectionUtils.isEmpty(bannerImgList)) {
            return null;
        }

        bannerImgVOList = new ArrayList<>();
        for (BannerImgIos bannerImg : bannerImgList) {
            BannerImgIosVO bannerImgVO = new BannerImgIosVO();
            BeanUtils.copyProperties(bannerImg, bannerImgVO);
            bannerImgVOList.add(bannerImgVO);
        }

        //写入缓存
        try {
            redisService.set(LogicConstants.BANNER_IMG_CACHE_KEY_IOS, JsonUtils.writeToString(bannerImgVOList));
        } catch (Exception e) {
            log.error("社区首页banner图数据写入redis缓存异常", e);
        }

        return bannerImgVOList;
    }
}
