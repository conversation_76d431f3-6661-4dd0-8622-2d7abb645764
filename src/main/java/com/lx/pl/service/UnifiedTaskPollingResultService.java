package com.lx.pl.service;

import com.lx.pl.dto.flux.FluxResponse;
import com.lx.pl.dto.midjourney.MidjourneyResponse;
import com.lx.pl.dto.mq.TaskPollingVo;
import com.lx.pl.enums.FluxTaskStatus;
import com.lx.pl.enums.MidjourneyTaskStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.lx.pl.service.FluxService.FLUX_TASK_PREFIX;
import static com.lx.pl.service.MidjourneyService.TTAPI_MJ_TASK_PREFIX;


/**
 * 统一任务状态轮询服务
 * 支持MJ和Flux两种任务类型的轮询
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class UnifiedTaskPollingResultService {

    @Autowired
    private MidjourneyService midjourneyService;

    @Autowired
    private FluxService fluxService;

    @Autowired
    private SendPollingService sendPollingService;

    @Autowired
    private RedisService redisService;

    /**
     * 处理任务状态轮询（统一入口）
     */
    public void handleTaskStatusPolling(TaskPollingVo pollingVo) {
        String taskType = pollingVo.getTaskType();
        String taskId = pollingVo.getJobId();
        String loginName = pollingVo.getLoginName();

        log.debug("Processing {} task polling for taskId: {}, attempt: {}",
                taskType, taskId, pollingVo.getCurrentAttempt());

        try {
            // 根据任务类型调用不同的处理方法
            switch (taskType) {
                case "MJ":
                    handleMjTaskPolling(pollingVo);
                    break;
                case "FLUX":
                    handleFluxTaskPolling(pollingVo);
                    break;
                default:
                    log.warn("Unknown task type: {} for taskId: {}", taskType, taskId);
            }
        } catch (Exception e) {
            log.error("Error processing {} task polling for taskId: {}", taskType, taskId, e);
            handleNextPolling(pollingVo);
        }
    }

    /**
     * 处理MJ任务轮询
     */
    private void handleMjTaskPolling(TaskPollingVo pollingVo) {
        String jobId = pollingVo.getJobId();
        String loginName = pollingVo.getLoginName();

        // 检查任务是否还存在于Redis中
        String taskKey = TTAPI_MJ_TASK_PREFIX + jobId;
        if (!redisService.hasKey(taskKey) && !redisService.hasKey(jobId)) {
            log.info("MJ Task {} was cancelled or already processed by callback", jobId);
            return;
        }

        // 查询任务状态
        MidjourneyResponse.TaskStatusResponse taskStatus = midjourneyService.getTaskStatus(jobId);

        if (taskStatus == null) {
            log.warn("Failed to get MJ task status for jobId: {}, attempt: {}", jobId, pollingVo.getCurrentAttempt());
            handleNextPolling(pollingVo);
            return;
        }

        String status = taskStatus.getStatus();
        MidjourneyTaskStatus taskStatusEnum = MidjourneyTaskStatus.fromStatus(status);

        // 根据状态处理
        switch (taskStatusEnum) {
            case SUCCESS:
                log.info("MJ Task completed successfully - jobId: {}", jobId);
                midjourneyService.handleTaskSuccess(pollingVo.getTaskType(), jobId, loginName, taskStatus);
                return; // 任务完成，不再轮询

            case FAILED:
                log.warn("MJ Task failed - jobId: {}", jobId);
                midjourneyService.handleTaskFailure(jobId, loginName);
                return; // 任务失败，不再轮询

            case PENDING_QUEUE:
            case ON_QUEUE:
                log.debug("MJ Task in progress - jobId: {}, status: {}", jobId, status);
                handleNextPolling(pollingVo);
                break;

            default:
                log.debug("MJ Task status unchanged - jobId: {}, status: {}", jobId, status);
                handleNextPolling(pollingVo);
                break;
        }
    }

    /**
     * 处理Flux任务轮询
     */
    private void handleFluxTaskPolling(TaskPollingVo pollingVo) {
        String taskId = pollingVo.getJobId();
        String loginName = pollingVo.getLoginName();

        // 检查任务是否还存在于Redis中
        String taskKey = FLUX_TASK_PREFIX + taskId;
        String markId = redisService.stringGet(taskKey);
        if (markId == null || markId.trim().isEmpty()) {
            log.warn("Flux task not found in Redis, stopping polling: {}", taskId);
            return;
        }

        // 获取任务状态 - 使用pollingUrl或默认方法
        FluxResponse.TaskStatusResponse taskStatus = pollingVo.getTaskStatusResponse();
        if (taskStatus == null && pollingVo.getPollingUrl() != null && !pollingVo.getPollingUrl().trim().isEmpty()) {
            // 使用pollingUrl查询状态
            try {
                taskStatus = fluxService.getTaskResultByPollingUrl(pollingVo.getPollingUrl());
            } catch (Exception e) {
                log.error("Failed to get Flux task status by polling URL for taskId: {} and send mq message", taskId, e);
                fluxService.sendImageProcessPollingMessage(pollingVo);
                return;
            }
        } else {
            // 使用默认方法查询状态
//            taskStatus = fluxService.getTaskResult(taskId);
        }

        if (taskStatus == null) {
            log.warn("Failed to get Flux task status for taskId: {}", taskId);
            handleNextPolling(pollingVo);
            return;
        }

        FluxTaskStatus status = FluxTaskStatus.fromStatus(taskStatus.getStatus());
        log.debug("Flux task status: taskId={}, status={}, progress={}",
                taskId, status, taskStatus.getProgress());

        // 根据状态处理
        switch (status) {
            case READY:
                log.info("Flux task completed successfully - taskId: {}", taskId);
                fluxService.handleTaskSuccess(pollingVo.getTaskType(), taskId, loginName, taskStatus);
                return; // 任务完成，不再轮询
            case REQUEST_MODERATED:
                fluxService.handleTaskRequestModerated(taskId, loginName, taskStatus);
                return; // 任务完成，不再轮询
            case CONTENT_MODERATED:
                fluxService.handleTaskRequestModerated(taskId, loginName, taskStatus);
                return; // 任务完成，不再轮询
            case ERROR:
            case CANCELLED:
            case TASK_NOT_FOUND:
                log.warn("Flux task failed - taskId: {}, status: {}", taskId, status);
                fluxService.handleTaskFailure(taskId, loginName);
                return; // 任务失败，不再轮询
            case TIMEOUT:
                log.warn("Flux task time out - taskId: {}, status: {}", taskId, status);
                fluxService.handleTaskTimeout(taskId, loginName);
                return; // 任务超时，不再轮询

            case PENDING:
            case RUNNING:
                log.debug("Flux task in progress - taskId: {}, status: {}, progress: {}",
                        taskId, status, taskStatus.getProgress());
                handleNextPolling(pollingVo);
                break;

            default:
                log.debug("Flux task status unchanged - taskId: {}, status: {}", taskId, status);
                fluxService.handleTaskUnknownError(taskId, loginName);
                break;
        }
    }

    public void handleNextPolling(TaskPollingVo pollingVo) {
        int nextAttempt = pollingVo.getCurrentAttempt() + 1;

        if (nextAttempt >= pollingVo.getMaxAttempts()) {
            // 超时处理
            log.warn("{} task status polling timeout for taskId: {} after {} attempts",
                    pollingVo.getTaskType(), pollingVo.getJobId(), pollingVo.getMaxAttempts());

            // 根据任务类型调用不同的超时处理方法
            switch (pollingVo.getTaskType()) {
                case "MJ":
                    midjourneyService.handleTaskTimeout(pollingVo.getJobId(), pollingVo.getLoginName());
                    break;
                case "FLUX":
                    fluxService.handleTaskTimeout(pollingVo.getJobId(), pollingVo.getLoginName());
                    break;
                default:
                    log.warn("Unknown task type: {} for taskId: {}", pollingVo.getTaskType(), pollingVo.getJobId());
            }
        } else {
            sendPollingService.scheduleNextPolling(pollingVo, nextAttempt);
        }
    }
}
