package com.lx.pl.service;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.lx.pl.constant.LockPrefixConstant;
import com.lx.pl.constant.LogicConstants;
import com.lx.pl.db.mysql.gen.entity.PromptFile;
import com.lx.pl.db.mysql.gen.entity.PromptRecord;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.db.mysql.gen.entity.VipStandards;
import com.lx.pl.db.mysql.gen.mapper.*;
import com.lx.pl.db.mysql.pay.entity.LogLumenCost;
import com.lx.pl.dto.*;
import com.lx.pl.enums.*;
import com.lx.pl.exception.ServerInternalException;
import com.lx.pl.pay.apple.VipPlatform;
import com.lx.pl.pay.common.domain.PayLumenRecord;
import com.lx.pl.pay.common.domain.PromotionConfig;
import com.lx.pl.pay.common.domain.SubscriptionCurrent;
import com.lx.pl.pay.common.dto.PromotionConfigItemDto;
import com.lx.pl.pay.common.dto.PromotionConfigVo;
import com.lx.pl.pay.common.enums.PromotionType;
import com.lx.pl.pay.common.mapper.PayLumenRecordMapper;
import com.lx.pl.pay.common.service.PayLumenRecordService;
import com.lx.pl.pay.common.service.PromotionConfigItemService;
import com.lx.pl.pay.common.service.PromotionConfigService;
import com.lx.pl.pay.common.service.SubscriptionCurrentService;
import com.lx.pl.pay.stripe.dto.PaymentType;
import com.lx.pl.util.DoubleMathUtils;
import com.lx.pl.util.JsonUtils;
import com.lx.pl.util.LogicUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;

import static com.lx.pl.constant.LockPrefixConstant.OPERATE_PERMISSION_LOCK_PREFIX;

@Service
@Slf4j
public class VipService {

    @Value("${realistic.ModelId}")
    String realisticModelId;

    @Value("${anime.ModelId}")
    String animeModelId;

    @Value("${lineart.ModelId}")
    String lineartModelId;

    @Value("${pony.ModelId}")
    String ponyModelId;

    @Value("${fluxschell.modelId}")
    String fluxschellModelId;

    @Value("${fluxdev.modelId}")
    String fluxdevModelId;

    @Value("${art.ModelId}")
    String artModelId;

    public static final String USER_RECHARGE_TOTAL_LUMENS = "user_recharge_total_lumens"; // 用户目前充值的有效总点数

    public static final String USER_VIP_TOTAL_LUMENS = "user_vip_total_lumens"; // 用户目前vip赠送的有效总点数

    public static final String USER_RECHARGE_USE_LUMENS = "user_recharge_use_lumens"; // 用户目前充值的已经使用点数

    public static final String USER_VIP_USE_LUMENS = "user_vip_use_lumens"; // 用户目前vip赠送的已使用点数

    public static final String USER_GIFT_TOTAL_LUMENS = "user_gift_total_lumens"; // 用户目前赠送的有效总点数

    public static final String USER_GIFT_USE_LUMENS = "user_gift_use_lumens"; // 用户目前赠送的已经使用点数

    public static final String USER_VIP_STANDARDS = "user_vip_standards"; // 用vip的权限标准

    public static final String USER_TODAY_RELAX_CREATE_IMG_NUMS = "user_today_relax_create_img_nums";  //用户当天relax生图数量

    @Autowired
    VipMapper vipStandardsMapper;

    @Autowired
    UserMapper userMapper;

    @Autowired
    PromptRecordMapper promptRecordMapper;

    @Autowired
    PayLumenRecordMapper payLumenRecordMapper;

    @Autowired
    RedisService redisService;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    PayLumenRecordService payLumenRecordService;

    @Autowired
    private RedissonClient redissonClient;

    @Lazy
    @Autowired
    private GenService genService;

    @Autowired
    private SubscriptionCurrentService subscriptionCurrentService;

    @Autowired
    private LumenTaskService lumenTaskService;

    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private PromptFileMapper promptFileMapper;
    @Autowired
    private LumenService lumenService;
    @Resource
    private UserModelRightsMapper userModelRightsMapper;
    @Autowired
    private ModelService modelService;
    @Resource
    private LumenChangeRecordService lumenChangeRecordService;

    @Autowired
    private PromotionConfigService promotionConfigService;
    @Autowired
    private PromotionConfigItemService promotionConfigItemService;

    /**
     * 查询所有的vip标准信息
     *
     * @param user
     * @return
     */
    public List<VipStandards> getVipStandardsList(User user) {
        try {
            List<VipStandards> vipStandardsList = new ArrayList<>();
            String vipStandardsListResult = (String) redisService.get(USER_VIP_STANDARDS);

            if (StringUtil.isBlank(vipStandardsListResult)) {
                LambdaQueryWrapper<VipStandards> lrw = new LambdaQueryWrapper();
                lrw.isNotNull(VipStandards::getId);
                vipStandardsList = vipStandardsMapper.selectList(lrw);

                redisService.set(USER_VIP_STANDARDS, JsonUtils.writeToString(vipStandardsList));
            } else {
                vipStandardsList = JsonUtils.writeToList(vipStandardsListResult, VipStandards.class);
            }
            return vipStandardsList;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 为移动端做兼容，后续删除
     *
     * @param user
     * @return
     */
    public OldUserLumens getOldUserLumens(User user) {
        OldUserLumens oldUserLumens = new OldUserLumens();

        UserLumens userLumens = lumenService.getUserLumens(user);
        if (!Objects.isNull(userLumens)) {
            BeanUtils.copyProperties(userLumens, oldUserLumens, "rechargeLumens", "leftRechargeLumens");
            oldUserLumens.setRechargeLumens(userLumens.getRechargeLumens() + userLumens.getGiftLumens());
            oldUserLumens.setLeftRechargeLumens(userLumens.getLeftRechargeLumens() + userLumens.getLeftGiftLumens());
        }

        return oldUserLumens;
    }

    public Integer getUserRechargeLumens(User user) {
        Long nowTimeLong = System.currentTimeMillis() / 1000;

        //查询会员有效点数和充值有效点数
        LambdaQueryWrapper<PayLumenRecord> payRecordlqw = new LambdaQueryWrapper();
        payRecordlqw.eq(PayLumenRecord::getLoginName, user.getLoginName());
        payRecordlqw.eq(PayLumenRecord::getInvalid, 0);
        payRecordlqw.eq(PayLumenRecord::getType, LumenType.recharge.getValue());
        payRecordlqw.le(PayLumenRecord::getCurrentPeriodStart, nowTimeLong);
        payRecordlqw.gt(PayLumenRecord::getCurrentPeriodEnd, nowTimeLong);
        List<PayLumenRecord> payLumenRecordList = payLumenRecordMapper.selectList(payRecordlqw);

        Integer rechargeLumen = 0;
        if (!CollectionUtils.isEmpty(payLumenRecordList)) {
            for (PayLumenRecord payLumenRecord : payLumenRecordList) {
                rechargeLumen += payLumenRecord.getLumenQty();
            }

            //由于用户充值了lumen币，所以需要清除redis缓存
            resettingPersonalLumens(user.getLoginName());
        }

        return rechargeLumen;
    }

    public User dealUserVipStandards(User user) {
        if (Objects.isNull(user)) {
            return user;
        }

        // 当天开始时间戳（秒）
        Long nowTimeStartOfDaySecond = LocalDate.now(ZoneOffset.UTC).atStartOfDay(ZoneOffset.UTC).toEpochSecond();
        Long nowTimeLong = System.currentTimeMillis() / 1000;

        //是否有数据库变更标记
        int count = 0;
        //如果用户每日免费为空，则赠送10点  如果用户每日免费不为空，则判断时间是否一致，
        // 不一致则更新每日免费点数使用量和时间
        if (user.getDailyLumensTime() == null || !user.getDailyLumensTime().equals(nowTimeStartOfDaySecond)) {
            LambdaUpdateWrapper<User> luw = new LambdaUpdateWrapper();

            luw.set(User::getDailyLumensTime, nowTimeStartOfDaySecond);
            luw.set(User::getDailyLumens, 0);
            luw.set(User::getUseDailyLumens, 0);

            user.setDailyLumensTime(nowTimeStartOfDaySecond);
            user.setDailyLumens(0);
            user.setUseDailyLumens(0);
            user.setDailyUpdate(Boolean.TRUE);

            lumenTaskService.resetTask(user);

//            JsonNode userConfig = user.getUserConfig();
//            if (userConfig != null && userConfig.isObject()) {
//                ObjectNode objectNode = (ObjectNode) userConfig;
//                objectNode.put("relaxLimit", false);
//                user.setUserConfig(objectNode);
//                luw.set(User::getUserConfig, objectNode);
//            }

            luw.set(User::getUpdateTime, LocalDateTime.now());
            luw.eq(User::getId, user.getId());
            userMapper.update(null, luw);
            redisService.putDataToHash(LogicConstants.USER_DAILY_LUMEN_DIALOG_CONTROL_CACHE_KEY, user.getLoginName(), true);
            log.info("修改用户:{} 每天的lumen信息:{}", user.getLoginName(), luw.getCustomSqlSegment());
        }

        //判断用户vip是否已经过期，过期了，则置为普通用户
        if (!VipType.basic.getValue().equals(user.getVipType())
                || !Objects.isNull(user.getVipEndTime())
                || !Objects.isNull(user.getVipBeginTime())
                || !Objects.isNull(user.getPriceInterval())) {
            LambdaUpdateWrapper<User> luwNew = new LambdaUpdateWrapper<>();
            String srcVipType = user.getVipType();
            Long srcVipEndTime = user.getVipEndTime();
            SubscriptionCurrent logicValidHighSubscriptionsFromDb = subscriptionCurrentService.getLogicValidHighSubscriptionsFromDb(user.getId());
            if (!VipType.basic.getValue().equals(logicValidHighSubscriptionsFromDb.getPlanLevel())
                    && (!Objects.equals(logicValidHighSubscriptionsFromDb.getPlanLevel(), srcVipType) || !Objects.equals(logicValidHighSubscriptionsFromDb.getVipEndTime(), srcVipEndTime))) {
                // logic 不是basic 并且user的vipType 不等于 logic的vipType 或者 user的vipEndTime 不等于 logic的vipEndTime
                luwNew.set(logicValidHighSubscriptionsFromDb.getVipEndTime() != null, User::getVipEndTime, logicValidHighSubscriptionsFromDb.getVipEndTime());
                luwNew.set(User::getVipType, logicValidHighSubscriptionsFromDb.getPlanLevel());
                luwNew.set(User::getPriceInterval, logicValidHighSubscriptionsFromDb.getPriceInterval());
                luwNew.set(User::getUpdateTime, LocalDateTime.now());

                user.setVipType(logicValidHighSubscriptionsFromDb.getPlanLevel());
                user.setVipEndTime(logicValidHighSubscriptionsFromDb.getVipEndTime());
                luwNew.eq(User::getId, user.getId())
                        .eq(User::getVipEndTime, srcVipEndTime)
                        .eq(User::getVipType, srcVipType);

                userMapper.update(null, luwNew);
                log.info("修改用户vip权限1:{} 会员或者vip,sql:{}", user.getLoginName(), luwNew.getCustomSqlSegment());
            } else if (VipType.basic.getValue().equals(logicValidHighSubscriptionsFromDb.getPlanLevel())
                    && !Objects.equals(srcVipType, VipType.basic.getValue())) {
                // logic是basic 并且user的vipType 不等于basic
                luwNew.set(User::getVipType, VipType.basic.getValue());

                user.setVipType(VipType.basic.getValue());
                luwNew.eq(User::getId, user.getId())
                        .eq(User::getVipEndTime, srcVipEndTime)
                        .eq(User::getVipType, srcVipType);

                userMapper.update(null, luwNew);
                log.info("修改用户vip权限2:{} 会员或者vip,sql:{}", user.getLoginName(), luwNew.getCustomSqlSegment());
            }
        }

        return user;
    }

    /**
     * 检查两个日期是否为同一天（比较格式化后的日期字符串）。
     * 处理时区差异问题。
     */
    private boolean isSameDate(String dateTime, LocalDate currentDate) {
        try {
            LocalDate parsedDate = LocalDate.parse(dateTime, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            return parsedDate.isEqual(currentDate);
        } catch (DateTimeParseException e) {
            return false; // 无效格式，默认认为不是同一天
        }
    }

    public Boolean judgeUserFastCreate(ModelRightsVerifyResult modelRightsVerifyResult, User user, int batchSize, Boolean highPixels, OriginCreate originCreate, String fileUrl, Double scale) {
        try {
            if ((OriginCreate.create.equals(originCreate) ||  OriginCreate.picCreate.equals(originCreate) || OriginCreate.edit.equals(originCreate)) && modelRightsVerifyResult.isUseFreeTrial()) {
                return true;
            }
            //获取用户剩余的lumen点（预判）
            int userSurplusLumens = lumenService.getUserSurplusLumens(user);
            if (userSurplusLumens <= 0) {
                return false;
            }
            return judgeLumenEnoughByToolType(user.getLoginName(), userSurplusLumens, modelRightsVerifyResult, batchSize, highPixels, originCreate, fileUrl, scale);
        } catch (Exception e) {
            log.error("checkUserFastCreate", e);
            throw new RuntimeException("checkUserFastCreate Failed");
        }
    }

    private boolean judgeLumenEnoughByToolType(String loginName, int userSurplusLumens, ModelRightsVerifyResult verifyResult, int batchSize, Boolean highPixels,
                                               OriginCreate originCreate, String fileUrl, Double scale) {
        //去背景和扩图为1点
        if (Objects.equals(OriginCreate.removeBackground, originCreate)) {
            if (userSurplusLumens >= batchSize) {
                verifyResult.setCostLumen(batchSize);
                return true;
            }
            return false;
        } else if (Objects.equals(OriginCreate.hiresFix, originCreate)) {
            //超分按像素计算
            return judgeByPixel(loginName, userSurplusLumens, fileUrl, scale, verifyResult);
        } else {
            //其余按张数计算
            return judgeCreate(userSurplusLumens, verifyResult, batchSize, highPixels);
        }
    }

    private boolean judgeByPixel(String loginName, int userSurplusLumens, String fileUrl, Double scale, ModelRightsVerifyResult verifyResult) {
        if (StringUtils.isBlank(fileUrl)) {
            return false;
        }

        PromptFile promptFile = promptFileMapper.selectOne(new LambdaQueryWrapper<>(PromptFile.class)
                .eq(PromptFile::getLoginName, loginName)
                .eq(PromptFile::getFileUrl, fileUrl)
                .last("limit 1"));
        if (Objects.isNull(promptFile)) {
            return false;
        }

        int baseLumen = LogicUtil.calculateCostLumenByPixel((int) DoubleMathUtils.mul(promptFile.getWidth(), scale), (int) DoubleMathUtils.mul(promptFile.getHeight(), scale)) * verifyResult.getUseModel()
                .getCoefficientByPixel();
        if (baseLumen <= userSurplusLumens) {
            verifyResult.setCostLumen(baseLumen);
            return true;
        }
        return false;
    }

    private boolean judgeCreate(int userSurplusLumens, ModelRightsVerifyResult verifyResult, int batchSize, Boolean highPixels) {
        int costLumen;
        if (highPixels) {
            costLumen = 10 * batchSize;
        } else {
            costLumen = batchSize * verifyResult.getUseModel().getCoefficientByNum();
        }
        if (userSurplusLumens >= costLumen) {
            verifyResult.setCostLumen(costLumen);
            return true;
        }
        return false;
    }


    public Boolean judgeUserOperatePermission(User user, int lumenCost) {
        RLock lock = redissonClient.getLock(OPERATE_PERMISSION_LOCK_PREFIX + user.getLoginName());
        try {
            //获取用户剩余的lumen点（预判）
            int userSurplusLumens = lumenService.getUserSurplusLumens(user);
            return userSurplusLumens >= lumenCost ? Boolean.TRUE : Boolean.FALSE;
        } catch (Exception e) {
            log.error("checkUserOperatePermission", e);
            throw new RuntimeException("checkUserOperatePermission Failed");
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 更新对应的任务信息和用户信息,以及扣点
     *
     * @param markId
     * @param promptId
     * @param userLoginName
     * @param batchImgSize
     */
    public void updateMessageByMarkId(String markId, String promptId, String userLoginName, int batchImgSize, int efficientImgNum, int highEfficientCost) throws IOException {
        //查询用户信息
        LambdaUpdateWrapper<User> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(User::getLoginName, userLoginName);
        User user = userMapper.selectOne(lambdaUpdateWrapper);

        //查询生图任务
        LambdaQueryWrapper<PromptRecord> lqw = new LambdaQueryWrapper<PromptRecord>();
        lqw.eq(PromptRecord::getMarkId, markId);
        lqw.eq(PromptRecord::getLoginName, userLoginName);
        PromptRecord promptRecord = promptRecordMapper.selectOne(lqw);

        if (!Objects.isNull(promptRecord) && !Objects.isNull(user)) {
            boolean useFreeTrial = promptRecord.isUseFreeTrial();
            int costTargetLumens = 0;
            if (promptRecord.getFastHour()) {
                genService.dealNotFinishTash(user.getLoginName());
            }
            if (!useFreeTrial) {
                //fastHour任务,且是会员则刷新未完成的任务信息
                GenGenericPara genGenericPara = JsonUtils.fromJsonNode(promptRecord.getGenInfo(), GenGenericPara.class);


                /// 获取模型信息
                ModelInformation.ModelAbout modelAbout = modelService.baseModelInfoByIdAndPlatform(promptRecord.getModelId(), promptRecord.getPlatform(),user.getRegistCountry());
                if (modelAbout != null) {
                    // dev模型为12点数，非dev模型为1点数，removeBackground均为1点
                    if (OriginCreate.removeBackground.getValue().equals(promptRecord.getOriginCreate())) {
                        costTargetLumens = efficientImgNum;
                    } else if (Objects.equals(OriginCreate.hiresFix.getValue(), promptRecord.getOriginCreate())) {
                        //超分按像素计算
                        costTargetLumens = highEfficientCost * modelAbout.getCoefficientByPixel();
                    } else {
                        if (!Objects.isNull(genGenericPara.getHighPixels()) && genGenericPara.getHighPixels()
                                && OriginCreate.create.getValue().equals(promptRecord.getOriginCreate())) {
                            costTargetLumens = efficientImgNum * 10;
                        } else {
                            costTargetLumens = efficientImgNum * modelAbout.getCoefficientByNum();
                        }
                    }
                } else {
                    log.error("模型信息为空, 无法口lumen, markId:{}", markId);
                }
            }

            //更新任务的点数消耗和生图结束时间和更新时间
            LambdaUpdateWrapper<PromptRecord> luw = new LambdaUpdateWrapper<>();
            luw.eq(PromptRecord::getMarkId, markId);
            luw.eq(PromptRecord::getLoginName, userLoginName);
            luw.set(PromptRecord::getGenEndTime, LocalDateTime.now());
            luw.set(PromptRecord::getUpdateTime, LocalDateTime.now());
            luw.set(PromptRecord::getCostLumens, costTargetLumens);
            promptRecordMapper.update(null, luw);


            if (efficientImgNum == 0) {
                log.info("batchImgSize != efficientImgNum markId:{}, batchImgSize:{}, efficientImgNum:{}", markId, batchImgSize, efficientImgNum);
                // 撤回试用次数
//                genService.decrModelRight(promptRecord, userLoginName);
            } else {
                genService.saveOrIncrModelRightReal(promptRecord, user);
            }

            //如果是快速生图则对lumens 点数进行更新
            if (Boolean.TRUE.equals(promptRecord.getFastHour())) {
                String costDetails = StringUtils.isNotBlank(promptRecord.getFeatureName()) ? promptRecord.getFeatureName() : promptRecord.getOriginCreate();
                dealUserLumensCost(costTargetLumens, markId, batchImgSize, user, LumenChangeSourceEnum.CREATE.getValue(), costDetails, null);
//                modelService.refreshUserModelRightsCache(userLoginName);
            } else {
                //统计relax生图数量
                redisService.incrementHashValue(USER_TODAY_RELAX_CREATE_IMG_NUMS, userLoginName, batchImgSize);
            }
        }
    }

    public void dealUserLumensCost(int costTargetLumens, String markId, int batchImgSize, User user, String source, String detail, String batchId) {
        //扣点顺序依次为 每天免费点数，会员vip点数，充值点数
        int remainingLumens = costTargetLumens;

        //对扣点加上分布式锁
        RLock lock = redissonClient.getLock(LockPrefixConstant.DEAL_USER_LUMEN_LOCK_PREFIX + user.getLoginName());
        try {
            lock.lock();
            //获取最新user信息
            user = userMapper.selectOne(new LambdaQueryWrapper<>(User.class)
                    .eq(User::getLoginName, user.getLoginName()));
            int surplusDailyLumens = user.getDailyLumens() - user.getUseDailyLumens();
            // 1. 扣除免费点数
            if ((surplusDailyLumens) > 0) {
                int deducted = Math.min(surplusDailyLumens, remainingLumens);
                user.setUseDailyLumens(user.getUseDailyLumens() + deducted);
                remainingLumens -= deducted;
            }

            //2. 如果免费点数不够，扣除系统奖励点数
            if (remainingLumens > 0) {
                Integer systemRewardLumen = user.getSystemRewardLumen();
                if (systemRewardLumen != null && systemRewardLumen > 0) {
                    int min = Math.min(systemRewardLumen, remainingLumens);
                    user.setSystemRewardLumen(systemRewardLumen - min);
                    remainingLumens -= min;
                }
            }

            //3. 如果系统赠送点数不够，再扣除其他点数
            remainingLumens = dealNotDailyFreeLumens(remainingLumens, user);
        } catch (Exception e) {
            log.error("用户：{} 对任务：{} 扣点报错", user.getLoginName(), markId, e);
            throw new ServerInternalException("Reduce lumens Failed");
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

        //如果点数没有完全扣完，则打印错误日志
        if (!(remainingLumens == 0)) {
            log.error("用户:{},生图任务:{},扣点数有误", user.getLoginName(), markId);
        }

        user.setUpdateTime(LocalDateTime.now());
        user.setTotalImgNum(!Objects.isNull(user.getTotalImgNum()) ? (user.getTotalImgNum() + batchImgSize) : batchImgSize);
        userMapper.updateById(user);
        lumenChangeRecordService.saveOrUpdateLumenChangeRecord(user, costTargetLumens, LumenChangeTypeEnum.DEDUCT.getValue(),
                source, detail, batchId);
    }

    public int dealNotDailyFreeLumens(int remainingLumens, User user) {
        //数据发生变更的PayLumenRecord
        List<PayLumenRecord> updatePayLumenRecordList = new ArrayList<>();
        List<LogLumenCost> logLumenCostList = new ArrayList<>();

        Long nowTimeLong = System.currentTimeMillis() / 1000;
        //查询会员有效点数和充值有效点数
        LambdaQueryWrapper<PayLumenRecord> payRecordlqw = new LambdaQueryWrapper();
        payRecordlqw.eq(PayLumenRecord::getLoginName, user.getLoginName());
        payRecordlqw.eq(PayLumenRecord::getInvalid, 0);
        payRecordlqw.gt(PayLumenRecord::getLumenLeftQty, 0);
        payRecordlqw.le(PayLumenRecord::getCurrentPeriodStart, nowTimeLong);
        payRecordlqw.gt(PayLumenRecord::getCurrentPeriodEnd, nowTimeLong);
        payRecordlqw.orderByDesc(PayLumenRecord::getType);
        payRecordlqw.orderByAsc(PayLumenRecord::getCreateTime);
        payRecordlqw.last("LIMIT 20");
        List<PayLumenRecord> payLumenRecordList = payLumenRecordMapper.selectList(payRecordlqw);

        if (!CollectionUtils.isEmpty(payLumenRecordList)) {
            Integer vipLumenUse = 0;
            Integer rechargeLumenUse = 0;
            Integer giftLumenUse = 0;

            for (PayLumenRecord record : payLumenRecordList) {
                if (remainingLumens <= 0) {
                    // 如果 remainingLumens 已经扣除完毕，提前结束
                    break;
                }

                int lumenLeftQty = record.getLumenLeftQty();
                int lumenCost = 0;
                int lumenLeft = 0;

                if (lumenLeftQty >= remainingLumens) {
                    // 当前条目足够扣除 remainingLumens
                    //剩余点数
                    lumenLeft = lumenLeftQty - remainingLumens;
                    record.setLumenLeftQty(lumenLeft);
                    //消耗点数
                    lumenCost = remainingLumens;
                    remainingLumens = 0; // 扣除完毕，remainingLumens 置为 0
                } else {
                    // 当前条目不足以扣除 remainingLumens，扣光当前条目
                    remainingLumens -= lumenLeftQty;
                    //消耗点数
                    lumenCost = lumenLeftQty;
                    //剩余点数
                    lumenLeft = 0;
                    record.setLumenLeftQty(lumenLeft);
                }

                record.setUpdateTime(LocalDateTime.now());
                updatePayLumenRecordList.add(record);

                //组装消费的数据
                LogLumenCost logLumenCost = new LogLumenCost();
                logLumenCost.setUserId(user.getId());
                logLumenCost.setLoginName(user.getLoginName());
                logLumenCost.setPayLumenRecordId(record.getId());
                logLumenCost.setType(record.getType());
                logLumenCost.setLumenBefore(record.getLumenLeftQty() + lumenCost);
                logLumenCost.setLumenCost(lumenCost);
                logLumenCost.setLumenLeft(record.getLumenLeftQty());
                logLumenCost.setLogicPeriodEnd(record.getLogicPeriodEnd());
                logLumenCost.setLogicPeriodStart(record.getLogicPeriodStart());
                logLumenCost.setCreateTime(LocalDateTime.now());
                logLumenCostList.add(logLumenCost);

                //判断类型
                switch (record.getType()) {
                    case 1:
                        rechargeLumenUse += lumenCost;
                        break;
                    case 3:
                        giftLumenUse += lumenCost;
                        break;
                    case 2:
                        vipLumenUse += lumenCost;
                        break;
                }
            }

            //批量更新lumen任务表
            if (!CollectionUtils.isEmpty(updatePayLumenRecordList)) {
                batchUpdatePayLumenRecord(updatePayLumenRecordList);
            }

            //批量插入lumen消费表
            if (!CollectionUtils.isEmpty(logLumenCostList)) {
                batchInsertlogLumenCost(logLumenCostList);
            }

            //如果使用量的充值点数大于0，则更新redis
            if (rechargeLumenUse > 0) {
                redisService.incrementFieldInHash(USER_RECHARGE_USE_LUMENS, user.getLoginName(), Long.valueOf(rechargeLumenUse));
            }

            //如果使用量的vip点数大于0，则更新redis
            if (vipLumenUse > 0) {
                redisService.incrementFieldInHash(USER_VIP_USE_LUMENS, user.getLoginName(), Long.valueOf(vipLumenUse));
            }

            //如果使用量的gift点数大于0，则更新redis
            if (giftLumenUse > 0) {
                redisService.incrementFieldInHash(USER_GIFT_USE_LUMENS, user.getLoginName(), Long.valueOf(giftLumenUse));
            }

        }

        return remainingLumens;
    }

    //批量更新lumen任务表
    public void batchUpdatePayLumenRecord(List<PayLumenRecord> updatePayLumenRecordList) {
        payLumenRecordService.updateBatchById(updatePayLumenRecordList);
    }

    //批量插入lumen消费表
    public void batchInsertlogLumenCost(List<LogLumenCost> logLumenCostList) {
        mongoTemplate.insertAll(logLumenCostList);
    }


    public void resettingPersonalLumens(String loginName) {
        if (StringUtil.isBlank(loginName)) {
            return;
        }

        log.info("开始重置用户:{} 有效的点数和使用的点数", loginName);
        try {
            //删除用户有效的点数
            redisService.deleteFieldFromHash(USER_RECHARGE_TOTAL_LUMENS, loginName);
            redisService.deleteFieldFromHash(USER_RECHARGE_USE_LUMENS, loginName);
            redisService.deleteFieldFromHash(USER_GIFT_TOTAL_LUMENS, loginName);
            //删除用户使用的点数
            redisService.deleteFieldFromHash(USER_VIP_TOTAL_LUMENS, loginName);
            redisService.deleteFieldFromHash(USER_VIP_USE_LUMENS, loginName);
            redisService.deleteFieldFromHash(USER_GIFT_USE_LUMENS, loginName);
            log.info("结束重置用户:{} 有效的点数和使用的点数", loginName);
        } catch (Exception e) {
            log.error("重置用户:{} 有效的点数和使用的点数失败", loginName, e);
        }
    }

    public Map<String, Object> getRealUserVipInfo(Long id, String platform) {
        SubscriptionCurrent validSubscriptionsFromDb = subscriptionCurrentService.getValidHighSubscriptionsFromDb(id, platform);

        // 构建结果 Map
        Map<String, Object> result = new HashMap<>();
        if (validSubscriptionsFromDb != null) {
            result.put("vipType", validSubscriptionsFromDb.getPlanLevel());
            result.put("priceInterval", validSubscriptionsFromDb.getPriceInterval());
        }

        return result;
    }

    //判断是否可以试用
    public Boolean canTrail(Long userId) {
        String cacheKey = LogicConstants.BUY_PLAN_USER;
        String hashKey = userId.toString();

        // 先从 Redis 获取
        Boolean value = redisService.hasHashField(cacheKey, hashKey);

        if (!value) {
            // 缓存未命中，从数据库查询用户是否买过订阅计划，只查询一条即可
            LambdaQueryWrapper<SubscriptionCurrent> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SubscriptionCurrent::getUserId, userId)
                    .ne(SubscriptionCurrent::getVipPlatform, VipPlatform.GIFT.getPlatformName())
                    .last("LIMIT 1");
            SubscriptionCurrent subscription = subscriptionCurrentService.getOne(queryWrapper);
            if (subscription == null) {
                value = true;
            } else {
                value = false;
                redisService.putDataToHash(cacheKey, hashKey, false);
            }
        } else {
            // 缓存命中，直接返回结果
            value = false;
        }

        return value;
    }


    public Boolean canFirstGift(Long id) {
        return !payLumenRecordService.hasPurchasedLumen(id);
    }

    public void clearTrialFirst(String suerName) {
        String userId = applicationContext.getBean(UserService.class).queryUserIdByName(suerName);
        if (userId != null) {
            redisService.deleteFieldFromHash(LogicConstants.BUY_PLAN_USER, userId);
            redisService.deleteFieldFromHash("user:first:lumen_flag", userId);
        }

    }

    /**
     * 获取用户可以使用的最大折扣
     *
     * @param userId 用户ID
     * @return 最大折扣
     */
    public Pair<String, PromotionConfigVo> getUserMaxDiscount(Long userId, String productType, String planLevelAndPriceInterval) {
        List<PromotionConfig> allValidPromotions = promotionConfigService.getAllValidPromotions();
        if (allValidPromotions.isEmpty()) {
            log.info("没有找到有效的优惠配置");
            return null;
        }
        PromotionConfig anniversaryPromotion = allValidPromotions.stream()
                .filter(promotionConfig -> promotionConfig.getType().equals(PromotionType.ANNIVERSARY.getCode()))
                .findFirst()
                .orElse(null);
        SubscriptionCurrent logicValidHighSubscriptionsFromDb = subscriptionCurrentService.getLogicValidHighSubscriptionsFromDb(userId);
        String vipPlan;
        String vipType;

        if (planLevelAndPriceInterval == null) {
            planLevelAndPriceInterval = logicValidHighSubscriptionsFromDb.getPlanLevel() + "." + logicValidHighSubscriptionsFromDb.getPriceInterval();
        }
        if (logicValidHighSubscriptionsFromDb != null) {
            vipPlan = logicValidHighSubscriptionsFromDb.getPlanLevel();
            vipType = logicValidHighSubscriptionsFromDb.getVipPlatform();
        } else {
            vipPlan = "basic";
            vipType = null;
        }
        if (logicValidHighSubscriptionsFromDb != null) {
            log.info("maxDiscount 当前订阅： {} {} {}", userId, productType, logicValidHighSubscriptionsFromDb.getPlanLevel() + "." + logicValidHighSubscriptionsFromDb.getPriceInterval());
        }

        if (anniversaryPromotion != null && logicValidHighSubscriptionsFromDb != null && !"basic".equals(vipPlan) && !VipPlatform.GIFT.getPlatformName().equals(vipType)) {
            log.info("周年庆优惠: {} ({}% OFF)", anniversaryPromotion.getName(), anniversaryPromotion.getOff());
            PromotionConfigVo promotionConfigVo = new PromotionConfigVo();
            promotionConfigVo.setId(anniversaryPromotion.getId());
            promotionConfigVo.setOff(anniversaryPromotion.getOff());
            promotionConfigVo.setRedeemBy(anniversaryPromotion.getRedeemBy());
            List<PromotionConfigItemDto> items = promotionConfigItemService.queryByConfigIdWithCache(anniversaryPromotion.getId());
            promotionConfigVo.setPromotionConfigItems(items);
            String finalPlanLevelAndPriceInterval = planLevelAndPriceInterval;
            items.stream().filter(item -> {
                        if ("plan".equals(productType)) {
                            return (item.getPlanLevel() + "." + item.getPriceInterval()).equals(finalPlanLevelAndPriceInterval);
                        } else {
                            return item.getProductType().equals(productType) && item.getPlanLevel().equals(vipPlan);
                        }
                    }).findFirst()
                    .ifPresent(promotionConfigVo::setUsedPromotionConfigItem);
            return Pair.of(anniversaryPromotion.getType(), promotionConfigVo);
        }

        PromotionConfigVo maxOff = new PromotionConfigVo();
        maxOff.setOff(0);

        String rtType = "none";
        boolean isPlan = productType != null && PaymentType.PLAN.getType().equals(productType);
        boolean hasBoughtVip = subscriptionCurrentService.hasBoughtVipAndHasExpire(userId, 0);
        Boolean withTrial = this.canTrail(userId);

        log.info("maxDiscount status: {} {} {} {} {}", userId, productType, planLevelAndPriceInterval, withTrial, hasBoughtVip);

        for (PromotionConfig promotionConfig : allValidPromotions) {
            String type = promotionConfig.getType();
            PromotionType byCode = PromotionType.getByCode(type);
            if (byCode == null) {
                log.info("没有找到对应的优惠类型");
                continue;
            }
            switch (byCode) {
                case FIRST_BUY_SUB:
                    List<PromotionConfigItemDto> items = promotionConfigItemService.queryByConfigIdWithCache(promotionConfig.getId());
                    String finalPlanLevelAndPriceInterval1 = planLevelAndPriceInterval;
                    PromotionConfigItemDto usedPromotionConfigItem = items.stream().filter(item -> (item.getPlanLevel() + "." + item.getPriceInterval()).equals(finalPlanLevelAndPriceInterval1))
                            .findFirst()
                            .orElse(null);
                    if (isPlan && withTrial && usedPromotionConfigItem != null && usedPromotionConfigItem.getOff() > maxOff.getOff()) {
                        maxOff = new PromotionConfigVo(promotionConfig.getId(), promotionConfig.getOff());
                        maxOff.setRedeemBy(promotionConfig.getRedeemBy());
                        maxOff.setPromotionConfigItems(items);
                        maxOff.setUsedPromotionConfigItem(usedPromotionConfigItem);
                        rtType = type;
                    }
                    break;
                case LUMEN_OFF:
                    if (isPlan) {
                        break;
                    }
                    List<PromotionConfigItemDto> itemsLumen = promotionConfigItemService.queryByConfigIdWithCache(promotionConfig.getId());
                    String finalPlanLevelAndPriceInterval2 = planLevelAndPriceInterval;
                    PromotionConfigItemDto lumenOff = itemsLumen.stream()
                            .filter(item -> logicValidHighSubscriptionsFromDb != null && (item.getPlanLevel() + "." + item.getPriceInterval()).equals(finalPlanLevelAndPriceInterval2)).findFirst()
                            .orElse(null);
                    if (lumenOff != null && lumenOff.getOff() > maxOff.getOff()) {
                        maxOff = new PromotionConfigVo(promotionConfig.getId(), promotionConfig.getOff());
                        maxOff.setRedeemBy(promotionConfig.getRedeemBy());
//                        maxOff.setVipStandards(vipStandard);
                        maxOff.setPromotionConfigItems(itemsLumen);
                        maxOff.setUsedPromotionConfigItem(lumenOff);
                        rtType = type;
                    }
                    break;
                case OLD_VIP_BACK:
                    List<PromotionConfigItemDto> itemsOld = promotionConfigItemService.queryByConfigIdWithCache(promotionConfig.getId());
                    String finalPlanLevelAndPriceInterval3 = planLevelAndPriceInterval;
                    PromotionConfigItemDto oldVipBack = itemsOld.stream().filter(item -> (item.getPlanLevel() + "." + item.getPriceInterval()).equals(finalPlanLevelAndPriceInterval3)).findFirst()
                            .orElse(null);
                    if (isPlan && hasBoughtVip && oldVipBack != null && oldVipBack.getOff() > maxOff.getOff()) {
                        maxOff = new PromotionConfigVo(promotionConfig.getId(), promotionConfig.getOff());
                        maxOff.setRedeemBy(promotionConfig.getRedeemBy());
                        maxOff.setPromotionConfigItems(itemsOld);
                        maxOff.setUsedPromotionConfigItem(oldVipBack);
                        rtType = type;
                    }
                    break;
                default:
                    break;
            }
        }

        log.info("用户最大折扣: {} ({}) ", rtType, maxOff.getUsedPromotionConfigItem());
        if ((anniversaryPromotion != null && maxOff.getUsedPromotionConfigItem() != null && anniversaryPromotion.getOff() > maxOff.getUsedPromotionConfigItem().getOff())
                || (anniversaryPromotion != null && maxOff.getUsedPromotionConfigItem() == null)) {
            PromotionConfigVo promotionConfigVo = new PromotionConfigVo();
            promotionConfigVo.setId(anniversaryPromotion.getId());
            promotionConfigVo.setOff(anniversaryPromotion.getOff());
            promotionConfigVo.setRedeemBy(anniversaryPromotion.getRedeemBy());
            List<PromotionConfigItemDto> items = promotionConfigItemService.queryByConfigIdWithCache(anniversaryPromotion.getId());
            promotionConfigVo.setPromotionConfigItems(items);
            String finalPlanLevelAndPriceInterval = planLevelAndPriceInterval;
            items.stream().filter(item -> {
                        if ("plan".equals(productType)) {
                            return (item.getPlanLevel() + "." + item.getPriceInterval()).equals(finalPlanLevelAndPriceInterval);
                        } else {
                            return item.getProductType().equals(productType) && item.getPlanLevel().equals(vipPlan);
                        }
                    }).findFirst()
                    .ifPresent(promotionConfigVo::setUsedPromotionConfigItem);
            log.info("周年庆优惠: {} ({}% OFF)", anniversaryPromotion.getName(), anniversaryPromotion.getOff());
            return Pair.of(anniversaryPromotion.getType(), promotionConfigVo);
        }

        if (maxOff.getUsedPromotionConfigItem() != null) {
            log.info("用户最大折扣: {} ({}) ", rtType, maxOff.getUsedPromotionConfigItem().getOff());
        }
        return Pair.of(rtType, maxOff);
    }

}
