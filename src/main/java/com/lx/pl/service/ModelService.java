package com.lx.pl.service;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lx.pl.constant.LogicConstants;
import com.lx.pl.db.mysql.gen.entity.GptModelAbout;
import com.lx.pl.db.mysql.gen.entity.GptModelRule;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.db.mysql.gen.mapper.GptModelAboutMapper;
import com.lx.pl.db.mysql.gen.mapper.GptModelRuleMapper;
import com.lx.pl.db.mysql.gen.mapper.UserModelRightsMapper;
import com.lx.pl.dto.ModelInformation;
import com.lx.pl.dto.ModelRightsVerifyResult;
import com.lx.pl.dto.UserModelRightsCacheDTO;
import com.lx.pl.pay.common.domain.SubscriptionCurrent;
import com.lx.pl.pay.common.service.SubscriptionCurrentService;
import com.lx.pl.util.JsonUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/13
 * @description 模型相关Service
 */
@Slf4j
@Service
public class ModelService {
    @Resource
    RedisService redisService;
    @Resource
    private GptModelAboutMapper gptModelAboutMapper;
    @Resource
    private GptModelRuleMapper gptModelRuleMapper;
    @Resource
    private UserModelRightsMapper userModelRightsMapper;
    @Resource
    private SubscriptionCurrentService subscriptionCurrentService;

    /**
     * 获取模型列表
     */
    public List<ModelInformation.ModelAbout> listModels(String platform, User user) throws IOException {
        //获取模型基础信息
        List<ModelInformation.ModelAbout> modelAboutList = baseModelInfo(platform);
        //获取模型配置信息及用户模型权益信息
        modelConfigByUserCountry(modelAboutList, user);
        return modelAboutList;

    }

    public ModelInformation.ModelAbout baseModelInfoByIdAndPlatform(String modelId, String platform, String registCountry) throws IOException {
        List<ModelInformation.ModelAbout> resultList = baseModelInfo(platform);
        if (org.springframework.util.CollectionUtils.isEmpty(resultList)) {
            return null;
        } else {
            ModelInformation.ModelAbout modelAbout = resultList.stream().filter(modelAbout1 -> modelAbout1.getModelId().equals(modelId)).findFirst().orElse(null);
            if (modelAbout == null) {
                return null;
            }
            //获取模型配置
            List<GptModelRule> modelRuleList = getModelRuleList(modelId);
            GptModelRule adapt = findAdaptModelRule(modelRuleList, registCountry);
            if (adapt == null) {
                return null;
            }
            modelAbout.setCoefficientByNum(adapt.getCoefficientByNum());
            modelAbout.setCoefficientByPixel(adapt.getCoefficientByPixel());
            return modelAbout;
        }
    }

    /**
     * 获取模型列表
     */
    public List<ModelInformation.ModelAbout> baseModelInfo(String platform) throws IOException {
        List<ModelInformation.ModelAbout> modelAboutList = new ArrayList<>();

        //定义redis中不同数据的key
        String webModelListKey = "webModelListKey";
        String iosModelListKey = "iosModelListKey";
        String androidModelListKey = "androidModelListKey";
        String modelListResult = "";

        //根据不同的客户端类型，返回不同的模型列表
        if (StringUtil.isBlank(platform)) {
            return modelAboutList;
        }
        String clientType = platform.toLowerCase();
        if (StringUtil.isNotBlank(clientType)) {
            switch (clientType) {
                case "ios":
                    modelListResult = (String) redisService.get(iosModelListKey);
                    break;
                case "android":
                    modelListResult = (String) redisService.get(androidModelListKey);
                    break;
                case "web":
                    modelListResult = (String) redisService.get(webModelListKey);
                    break;
            }
        }

        if (StringUtil.isNotBlank(modelListResult)) {
            modelAboutList.addAll(JsonUtils.writeToList(modelListResult, ModelInformation.ModelAbout.class));
        } else {
            List<ModelInformation.ModelAbout> modelAbouts = buildModelList(clientType);
            if (StringUtil.isNotBlank(clientType)) {
                switch (clientType) {
                    case "ios":
                        redisService.set(iosModelListKey, JsonUtils.writeToString(modelAbouts));
                        break;
                    case "android":
                        redisService.set(androidModelListKey, JsonUtils.writeToString(modelAbouts));
                        break;
                    case "web":
                        redisService.set(webModelListKey, JsonUtils.writeToString(modelAbouts));
                        break;
                }
            }
            modelAboutList = modelAbouts;
        }
        return modelAboutList;

    }

    private void modelConfigByUserCountry(List<ModelInformation.ModelAbout> modelAboutList, User user) {
        if (CollectionUtils.isEmpty(modelAboutList)) {
            return;
        }
        try {
            for (ModelInformation.ModelAbout modelAboutDto : modelAboutList) {
                String modelId = modelAboutDto.getModelId();
                List<GptModelRule> modelRuleList = getModelRuleList(modelId);
                GptModelRule adaptRule = findAdaptModelRule(modelRuleList, user.getRegistCountry());
                if (adaptRule == null) {
                    continue;
                }
                modelAboutDto.setCoefficientByNum(adaptRule.getCoefficientByNum());
                modelAboutDto.setCoefficientByPixel(adaptRule.getCoefficientByPixel());
                modelAboutDto.setPayTrial(adaptRule.getPayTrial());
                modelAboutDto.setFreeTrial(adaptRule.getFreeTrial());
                UserModelRightsCacheDTO userModelRights = getUserModelRights(modelId, user.getLoginName());
                modelAboutDto.setUsedPayTrial(userModelRights == null ? 0 : userModelRights.getUsedPayTrial());
                modelAboutDto.setUsedFreeTrial(userModelRights == null ? 0 : userModelRights.getUsedFreeTrial());
            }
        } catch (Exception e) {
            log.error("获取模型配置信息异常, loginName: {}", user.getLoginName(), e);
        }
    }

    private GptModelRule findAdaptModelRule(List<GptModelRule> modelRuleList, String userRegisterCountry) {
        GptModelRule adaptRule = null;
        for (GptModelRule modelRule : modelRuleList) {
            if (modelRule.getDefaultRule() == 0 && StringUtils.isNotBlank(modelRule.getSuitableForCountry())
                    && modelRule.getSuitableForCountry().contains(userRegisterCountry)) {
                adaptRule = modelRule;
                break;
            }

            if (modelRule.getDefaultRule() == 1) {
                adaptRule = modelRule;
                if (StringUtils.isBlank(userRegisterCountry)) {
                    break;
                }
            }
        }
        return adaptRule;
    }

    @SneakyThrows
    private List<GptModelRule> getModelRuleList(String modelId) {
        List<GptModelRule> modelRuleList;
        Object data = redisService.getDataFromHash(LogicConstants.MODEL_RULES_CACHE_KEY, modelId);
        if (data != null) {
            modelRuleList = JsonUtils.writeToList((String) data, GptModelRule.class);
        } else {
            modelRuleList = gptModelRuleMapper.selectList(new LambdaQueryWrapper<>(GptModelRule.class)
                    .eq(GptModelRule::getModelId, modelId)
                    .eq(GptModelRule::getIsDeleted, LogicConstants.DB_VALID));
            redisService.putDataToHash(LogicConstants.MODEL_RULES_CACHE_KEY, modelId, JsonUtils.writeToString(modelRuleList));
        }

        return modelRuleList;
    }

    public List<ModelInformation.ModelAbout> buildModelList(String platform) {
        List<GptModelAbout> modelAboutList = gptModelAboutMapper.selectList(new LambdaQueryWrapper<GptModelAbout>().orderByAsc(GptModelAbout::getModelOrder));

        List<ModelInformation.ModelAbout> modelAboutDtos = new ArrayList<>();

        if (StringUtil.isNotBlank(platform)) {
            modelAboutList = modelAboutList.stream()
                    .filter(modelAbout -> {
                        String plat = modelAbout.getPlatform();
                        return plat != null && Arrays.asList(plat.split(",")).contains(platform);
                    })
                    .collect(Collectors.toList());
        }

        for (GptModelAbout gptModelAbout : modelAboutList) {
            ModelInformation.ModelAbout modelAboutDto = new ModelInformation.ModelAbout();
            BeanUtils.copyProperties(gptModelAbout, modelAboutDto);
            if (StringUtil.isNotBlank(platform)) {
                switch (platform) {
                    case "ios":
                        modelAboutDto.setModelOrder(gptModelAbout.getIosOrder());
                        break;
                    case "android":
                        modelAboutDto.setModelOrder(gptModelAbout.getAndroidOrder());
                        break;
                    default:
                        modelAboutDto.setModelOrder(gptModelAbout.getModelOrder());
                        break;
                }
            }
            if (com.lx.pl.util.StringUtils.isNotBlank(gptModelAbout.getDefaultConfig())) {
                try {
                    modelAboutDto.setDefaultConfig(JsonUtils.fromString(gptModelAbout.getDefaultConfig(), ModelInformation.DefaultConfig.class));
                } catch (Exception e) {
                    log.error("模型列表转换失败", e);
                    modelAboutDto.setDefaultConfig(ModelInformation.DefaultConfig.builder().width(1024).height(1024).steps(0).seed(-1l).build());
                }
            } else {
                modelAboutDto.setDefaultConfig(ModelInformation.DefaultConfig.builder().width(1024).height(1024).steps(0).seed(-1l).build());
            }
            if (com.lx.pl.util.StringUtils.isNotBlank(gptModelAbout.getSupportStyleList())) {
                try {
                    modelAboutDto.setSupportStyleList(JsonUtils.writeToList(gptModelAbout.getSupportStyleList(), ModelInformation.SupportStyle.class));
                } catch (Exception e) {
                    log.error("模型列表转换失败", e);
                    modelAboutDto.setSupportStyleList(new ArrayList<>());
                }
            } else {
                modelAboutDto.setSupportStyleList(new ArrayList<>());
            }
            modelAboutDtos.add(modelAboutDto);
        }

        //  最后对结果根据 modelOrder 排序
        modelAboutDtos.sort(Comparator.comparing(ModelInformation.ModelAbout::getModelOrder, Comparator.nullsLast(Integer::compareTo)));
        return modelAboutDtos;
    }

    @SneakyThrows
    public UserModelRightsCacheDTO getUserModelRights(String modelId, String loginName) {
        UserModelRightsCacheDTO userModelRights = null;
        List<UserModelRightsCacheDTO> modelRightsList = null;
        String cacheKey = generateUserModelRightsCacheKey(loginName);
        Object data = redisService.get(cacheKey);
        if (data != null) {
            modelRightsList = JsonUtils.writeToList((String) data, UserModelRightsCacheDTO.class);
        }
        if (CollectionUtils.isEmpty(modelRightsList)) {
            modelRightsList = userModelRightsMapper.selectUserModelRights(loginName);
        }
        if (CollectionUtils.isNotEmpty(modelRightsList)) {
            userModelRights = modelRightsList.stream().filter(modelRights -> Objects.equals(modelRights.getModelId(), modelId)).findFirst().orElse(null);
        }
        refreshUserModelRightsCache(loginName);
        Object notFinishTaskFreeTrial = redisService.getDataFromHash(LogicConstants.USER_NOT_FINISH_TASK_MODEL_FREE_TRIAL_KEY + loginName, modelId);
        Object notFinishTaskPayTrial = redisService.getDataFromHash(LogicConstants.USER_NOT_FINISH_TASK_MODEL_PAY_TRIAL_KEY + loginName, modelId);

        if (userModelRights == null) {
            userModelRights = new UserModelRightsCacheDTO();
            userModelRights.setModelId(modelId);
            userModelRights.setUsedFreeTrial(notFinishTaskFreeTrial == null ? 0 : (Integer) notFinishTaskFreeTrial);
            userModelRights.setUsedPayTrial(notFinishTaskPayTrial == null ? 0 : (Integer) notFinishTaskPayTrial);
        } else {
            userModelRights.setUsedFreeTrial(userModelRights.getUsedFreeTrial() + (notFinishTaskFreeTrial == null ? 0 : (Integer) notFinishTaskFreeTrial));
            userModelRights.setUsedPayTrial(userModelRights.getUsedPayTrial() + (notFinishTaskPayTrial == null ? 0 : (Integer) notFinishTaskPayTrial));
        }

        return userModelRights;
    }

    @SneakyThrows
    public List<UserModelRightsCacheDTO> getAllUserModelRights(String loginName) {
        String cacheKey = generateUserModelRightsCacheKey(loginName);
        List<UserModelRightsCacheDTO> modelRightsList = null;

        // 从缓存获取
        Object data = redisService.get(cacheKey);
        if (data != null) {
            modelRightsList = JsonUtils.writeToList((String) data, UserModelRightsCacheDTO.class);
        }

        // 缓存没数据则查数据库
        if (CollectionUtils.isEmpty(modelRightsList)) {
            modelRightsList = userModelRightsMapper.selectUserModelRights(loginName);
        }
        redisService.set(cacheKey, JsonUtils.writeToString(modelRightsList), 30, TimeUnit.MINUTES);
        // 去重：保证每个 modelId 只出现一次
        if (CollectionUtils.isNotEmpty(modelRightsList)) {
            modelRightsList = modelRightsList.stream()
                    .collect(Collectors.toMap(
                            UserModelRightsCacheDTO::getModelId,
                            Function.identity(),
                            (existing, replacement) -> existing
                    ))
                    .values()
                    .stream()
                    .collect(Collectors.toList()); //
        }

        return modelRightsList;
    }


    public void refreshUserModelRightsCache(String loginName) {
        String cacheKey = generateUserModelRightsCacheKey(loginName);
        try {
            redisService.delete(cacheKey);
            List<UserModelRightsCacheDTO> modelRightsList = userModelRightsMapper.selectUserModelRights(loginName);
            if (modelRightsList == null) {
                modelRightsList = new ArrayList<>();
            }
            redisService.delete(cacheKey);
            redisService.set(cacheKey, JsonUtils.writeToString(modelRightsList), 30, TimeUnit.MINUTES);
        } catch (Exception e) {
            log.error("刷新用户模型权益缓存失败, loginName: {}", loginName, e);
            redisService.delete(cacheKey);
        }
    }

    public boolean checkSurplusTrial(GptModelRule adapt, User user, UserModelRightsCacheDTO userModelRights, boolean freeTrial) {
        Map<String, Integer> trialMap = freeTrial ? adapt.getFreeTrial() : adapt.getPayTrial();
        if (trialMap == null || trialMap.isEmpty()) {
            return false;
        }

        Integer total = trialMap.get(user.getVipType());
        if (total == null || total <= 0) {
            return false;
        }

        int used = 0;
        if (userModelRights != null) {
            Integer dbUsed = freeTrial ? userModelRights.getUsedFreeTrial() : userModelRights.getUsedPayTrial();
            used += dbUsed == null ? 0 : dbUsed;
        }

//        Integer notFinishTaskUsed = freeTrial ? (Integer) redisService.getDataFromHash(LogicConstants.USER_NOT_FINISH_TASK_MODEL_FREE_TRIAL_KEY + user.getLoginName(), adapt.getModelId())
//                : (Integer) redisService.getDataFromHash(LogicConstants.USER_NOT_FINISH_TASK_MODEL_PAY_TRIAL_KEY + user.getLoginName(), adapt.getModelId());

//        used += notFinishTaskUsed == null ? 0 : notFinishTaskUsed;

        return used < total;
    }

    public ModelRightsVerifyResult checkModelRights(ModelInformation.ModelAbout modelAbout, User user) {
        String modelId = modelAbout.getModelId();
        ModelRightsVerifyResult modelRightsVerifyResult = new ModelRightsVerifyResult();
        modelRightsVerifyResult.setUseModel(modelAbout);
        modelRightsVerifyResult.setPassVerify(false);
        //获取模型配置
        List<GptModelRule> modelRuleList = getModelRuleList(modelId);
        GptModelRule adapt = findAdaptModelRule(modelRuleList, user.getRegistCountry());
        modelAbout.setCoefficientByNum(adapt.getCoefficientByNum());
        modelAbout.setCoefficientByPixel(adapt.getCoefficientByPixel());
        SubscriptionCurrent current = subscriptionCurrentService.getLogicValidHighSubscriptionsFromDb(user.getId());
        modelRightsVerifyResult.setVipType(current.getPlanLevel());
        if (modelAbout.getSuitableForMemberLevel().contains(current.getPlanLevel())) {
            modelRightsVerifyResult.setPassVerify(true);
            return modelRightsVerifyResult;
        }
        UserModelRightsCacheDTO userModelRights = getUserModelRights(modelId, user.getLoginName());
        //免费试用校验
        if (checkSurplusTrial(adapt, user, userModelRights, true)) {
            modelRightsVerifyResult.setUseFreeTrial(true);
            modelRightsVerifyResult.setPassVerify(true);
            return modelRightsVerifyResult;
        }

        //付费试用校验
        if (checkSurplusTrial(adapt, user, userModelRights, false)) {
            modelRightsVerifyResult.setUsePayTrial(true);
            modelRightsVerifyResult.setPassVerify(true);
            return modelRightsVerifyResult;
        }

        return modelRightsVerifyResult;
    }

    private String generateUserModelRightsCacheKey(String loginName) {
        return LogicConstants.USER_MODEL_RIGHTS_CACHE_KEY + loginName;
    }
}
