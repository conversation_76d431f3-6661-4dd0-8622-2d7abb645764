package com.lx.pl.service;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.lx.pl.db.mysql.gen.entity.Album;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.db.mysql.gen.mapper.AlbumMapper;
import com.lx.pl.db.mysql.gen.mapper.UserMapper;
import com.lx.pl.dto.AlbumParams;
import com.lx.pl.dto.AlbumResult;
import com.lx.pl.dto.PromptPageInfo;
import com.lx.pl.enums.UploadType;
import com.lx.pl.exception.BadRequestException;
import com.lx.pl.util.FileUtils;
import com.qcloud.cos.model.DeleteObjectsRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.lx.pl.service.ImgUploadCommonService.getFilePath;

@Slf4j
@Service
public class AlbumService {

    @Value("${album.folder}")
    String albumFolder;

    @Value("${albumThumb.folder}")
    String albumThumbFolder;

    @Autowired
    ImgUploadCommonService imgUploadCommonService;

    @Autowired
    AlbumMapper albumMapper;

    @Autowired
    UserMapper userMapper;

    @Resource(name = "taskExecutor")
    private ThreadPoolTaskExecutor taskExecutor;

    private static final List<String> SUPPORTED_FORMATS = Arrays.asList("jpeg", "jpg", "png", "webp", "jfif", "ico", "gif", "svg", "bmp");


    public AlbumResult uploadAlbumImg(MultipartFile albumImg, AlbumParams albumParams, User user) throws IOException {
        AlbumResult albumResult = new AlbumResult();
        File file = null;
        File thumbnailFile = null;
        try {
            file = FileUtils.transferToFile(albumImg, "albumImg");

            if (StringUtil.isNotBlank(file.getName()) && file.getName().length() > 200) {
                throw new BadRequestException("The uploaded image file name is too long!");
            }

            String formatName = FileUtils.detectImageFormat(file);
            if (StringUtil.isNotBlank(formatName) && !SUPPORTED_FORMATS.contains(formatName)) {
                throw new BadRequestException("The uploaded image format is not supported!");
            }
            file = FileUtils.convertFileToPng(file);
            //生成缩略图
            thumbnailFile = FileUtils.createThumbnail(file);

            String signedUrl = imgUploadCommonService.uploadToOssWithType(file, String.valueOf(user.getId()), UploadType.album.getValue());
            String thumbnailSignedUrl = imgUploadCommonService.uploadToOssWithType(thumbnailFile, String.valueOf(user.getId()), UploadType.album.getValue());
            /**
             * 相册数据入库
             */
            Album album = updateUserAlBum(albumParams, user, signedUrl, thumbnailSignedUrl);

            BeanUtils.copyProperties(album, albumResult);
            albumResult.setAlbumImgId(album.getId());

            return albumResult;
        } catch (Exception e) {
            log.error("用户：{} 上传图片到相册失败,报错信息：{}", user.getLoginName(), e.getMessage(), e);
            return null;
        } finally {
            //对于临时文件处理完后，进行删除
            if (file != null && file.delete()) {
                log.info("删除临时文件成功file：{}", file.getAbsolutePath());
            }
            if (thumbnailFile != null && thumbnailFile.delete()) {
                log.info("删除临时文件成功thumbnailFile：{}", thumbnailFile.getAbsolutePath());
            }
        }
    }

    /**
     * 更新用户相册信息
     *
     * @param albumParams        图片参数
     * @param user               用户信息
     * @param signedUrl          图片url
     * @param thumbnailSignedUrl 缩略图url
     * @return Album
     */
    @Transactional(rollbackFor = Exception.class)
    public Album updateUserAlBum(AlbumParams albumParams, User user, String signedUrl, String thumbnailSignedUrl) {
        Album album = new Album();
        album.setUserId(user.getId());
        album.setLoginName(user.getLoginName());
        album.setImgName(getFilePath(signedUrl));
        album.setThumbImgName(getFilePath(thumbnailSignedUrl));
        album.setImgUrl(signedUrl);
        album.setThumbImgUrl(thumbnailSignedUrl);
        album.setWidth(albumParams.getWidth());
        album.setHeight(albumParams.getHeight());
        album.setSize(albumParams.getSize());
        album.setRealWidth(albumParams.getRealWidth());
        album.setRealHeight(albumParams.getRealHeight());
        album.setCreateBy(user.getLoginName());
        album.setCreateTime(LocalDateTime.now());

        if (albumMapper.insert(album) > 0) {
            /**
             * 更新用户表上传图片到相册数量
             */
            LambdaUpdateWrapper<User> luw = new LambdaUpdateWrapper<>();
            luw.eq(User::getId, user.getId());
            luw.set(User::getAlbumImgNum, user.getAlbumImgNum() + 1);
            luw.set(User::getUpdateBy, user.getLoginName());
            luw.set(User::getUpdateTime, LocalDateTime.now());
            userMapper.update(null, luw);
        }

        return album;
    }


    public AlbumResult uploadAlbumImgV2(AlbumParams albumParams, User user) {
        AlbumResult albumResult = new AlbumResult();
        File file = null;
        File thumbnailFile = null;
        try {
            file = FileUtils.getFileByHttpURL(albumParams.getImgUrl());

            String formatName = FileUtils.detectImageFormat(file);

            if (StringUtil.isNotBlank(formatName) && !SUPPORTED_FORMATS.contains(formatName)) {
                throw new BadRequestException("The uploaded image format is not supported!");
            }
            file = FileUtils.convertFileToPng(file);
            //生成缩略图
            thumbnailFile = FileUtils.createThumbnail(file);

            String signedUrl = imgUploadCommonService.uploadToOssWithType(file, String.valueOf(user.getId()), UploadType.album.getValue());
            String thumbnailSignedUrl = imgUploadCommonService.uploadToOssWithType(thumbnailFile, String.valueOf(user.getId()), UploadType.album.getValue());

            taskExecutor.submit(() -> {
                List<DeleteObjectsRequest.KeyVersion> newKes = new ArrayList<>();
                String key = getFilePath(albumParams.getImgUrl());
                newKes.add(new DeleteObjectsRequest.KeyVersion(key));
                imgUploadCommonService.deleteObjects(newKes);
            });

            // 更新用户相册信息
            Album album = updateUserAlBum(albumParams, user, signedUrl, thumbnailSignedUrl);

            BeanUtils.copyProperties(album, albumResult);
            albumResult.setAlbumImgId(album.getId());

            return albumResult;
        } catch (Exception e) {
            log.error("用户：{} 上传图片到相册失败,报错信息 v2：{}", user.getLoginName(), e.getMessage(), e);
            return null;
        } finally {
            //对于临时文件处理完后，进行删除
            if (file != null && file.delete()) {
                log.info("删除临时文件成功file v2：{}", file.getAbsolutePath());
            }
            if (thumbnailFile != null && thumbnailFile.delete()) {
                log.info("删除临时文件成功thumbnailFile v2：{}", thumbnailFile.getAbsolutePath());
            }
        }
    }

    public Boolean deleteAlbumImg(Long id, User user) throws JsonProcessingException {
        try {
            int deleteCount = albumMapper.deleteById(id);

            /**
             * 更新用户表上传图片到相册数量
             */
            if (deleteCount > 0 && user.getAlbumImgNum() > 0) {
                LambdaUpdateWrapper<User> luw = new LambdaUpdateWrapper();
                luw.eq(User::getId, user.getId());
                luw.set(User::getAlbumImgNum, user.getAlbumImgNum() - 1);
                luw.set(User::getUpdateBy, user.getLoginName());
                luw.set(User::getUpdateTime, LocalDateTime.now());
                userMapper.update(null, luw);
            }

        } catch (Exception e) {
            log.error("用户：{} 删除相册中的图片失败,报错信息：", user.getLoginName(), e);
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    public PromptPageInfo<AlbumResult> getAlbumImgList(Integer pageNum, Integer pageSize, User user) {
        //分页查询任务
        LambdaQueryWrapper<Album> qw = new LambdaQueryWrapper<>();
        qw.eq(Album::getLoginName, user.getLoginName());
        qw.orderByDesc(Album::getCreateTime);
        qw.orderByDesc(Album::getId);

        IPage<Album> page = albumMapper.selectPage(new Page<>(pageNum, pageSize), qw);
        List<Album> albumList = page.getRecords();

        List<AlbumResult> albumResultList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(albumList)) {
            for (Album album : albumList) {
                AlbumResult albumResult = new AlbumResult();
                BeanUtils.copyProperties(album, albumResult);
                albumResult.setAlbumImgId(album.getId());
                albumResultList.add(albumResult);
            }
        }

        PromptPageInfo<AlbumResult> promptPageInfo = new PromptPageInfo<>();
        promptPageInfo.setResultList(albumResultList);
        promptPageInfo.setPageNum(pageNum);
        promptPageInfo.setPageSize(pageSize);

        return promptPageInfo;
    }
}
