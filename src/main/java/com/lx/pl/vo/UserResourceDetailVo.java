package com.lx.pl.vo;


import com.lx.pl.dto.ModelUsedDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/8/11
 * @description 用户资源详情
 */
@Data
@Schema(description = "用户资源详情")
public class UserResourceDetailVo {

    @Schema(description = "用户剩余可用lumen点数")
    private Integer remainingAvailableLumen;

    @Schema(description = "模型使用情况列表")
    private List<ModelUsedDto> modelUsedList;
}
