package com.lx.pl.db.mysql.gen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/7/14
 * @description 用户模型权益表
 */
@Data
@TableName(value = "user_model_rights", autoResultMap = true)
public class UserModelRights {

    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户登录名
     */
    private String userLoginName;

    /**
     * 模型id
     */
    private String modelId;

    /**
     * 已使用付费试用次数
     */
    private Integer usedPayTrial;

    /**
     * 已使用免费试用次数
     */
    private Integer usedFreeTrial;

    /**
     * 是否删除
     */
    private Integer isDeleted;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
