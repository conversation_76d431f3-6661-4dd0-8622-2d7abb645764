package com.lx.pl.db.mysql.gen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lx.pl.Handler.ModelRuleTypeHandler;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/7/3
 * @description 模型规则配置表
 */
@Data
@TableName(value = "gpt_model_rule", autoResultMap = true)
public class GptModelRule {

    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 模型id
     */
    private String modelId;

    /**
     * 按张数扣点系数
     */
    private Integer coefficientByNum;

    /**
     * 按像素扣点系数
     */
    private Integer coefficientByPixel;

    /**
     * 付费试用次数，key：会员等级，value：次数
     */
    @TableField(typeHandler = ModelRuleTypeHandler.class)
    private Map<String, Integer> payTrial;

    /**
     * 免费试用次数，key：会员等级，value：次数
     */
    @TableField(typeHandler = ModelRuleTypeHandler.class)
    private Map<String, Integer> freeTrial;

    /**
     * 模型规则适用国家，多个用英文逗号分隔
     */
    private String suitableForCountry;

    /**
     * 是否默认规则
     */
    private Integer defaultRule;

    /**
     * 是否删除
     */
    private Integer isDeleted;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
