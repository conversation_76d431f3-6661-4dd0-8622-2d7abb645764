package com.lx.pl.db.mysql.gen.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lx.pl.db.mysql.gen.entity.UserModelRights;
import com.lx.pl.dto.UserModelRightsCacheDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/14
 * @description
 */
public interface UserModelRightsMapper extends BaseMapper<UserModelRights> {
    int incrPayTrial(@Param("loginName") String loginName, @Param("modelId") String modelId, @Param("modifyCount") int modifyCount);

    int incrFreeTrial(@Param("loginName") String loginName, @Param("modelId") String modelId, @Param("modifyCount") int modifyCount);

    List<UserModelRightsCacheDTO> selectUserModelRights(@Param("loginName") String loginName);

    int decrFreeTrial( @Param("loginName") String loginName, @Param("modelId") String modelId, @Param("modifyCount") int modifyCount);
    int decrPayTrial( @Param("loginName") String loginName, @Param("modelId") String modelId, @Param("modifyCount") int modifyCount);
}
