package com.lx.pl.db.mysql.community.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 社区文件信息
 */
@Data
@ToString
@Document(collection = "files")
public class CommFile implements Serializable {
    @Id
    private String id;

    /**
     * 图片点赞数
     */
//    @Indexed
    private Integer fileLikeNums;

    /**
     * 图片一级评论数
     */
    private Integer fileFirstCommentNums;

    private Integer remixNums;

    private Integer shareNums;

    private Integer trendingScore;

    private Double trendingScoreTime;

    /**
     * 图片评论数
     */
    private Integer fileCommentNums;

    /**
     * 文件id，原来mysql对应的id，不应该以这个字段作为处理
     */
    private String fileId;

    /**
     * 文件路径
     */
    private String fileUrl;

    /**
     * 缩略图路径
     */
    private String thumbnailUrl;

    /**
     * 高清缩略图路径
     */
    private String highThumbnailUrl;

    /**
     * 小图路径
     */
    private String miniThumbnailUrl;

    /**
     * 30% 高清图
     */
    private String highMiniUrl;

    /**
     * 标签
     */
//    @Indexed
    private List<String> tags;

    /**
     * 发布社区时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 用户信息
     */
    private AccountInfo accountInfo;

    /**
     * 生图入参
     */
    private String genInfo;

    /**
     * 图片结果宽度
     */
    private Integer realWidth;

    /**
     * 图片结果高度
     */
    private Integer realHeight;

    /**
     * 生图提示词
     */
    private String prompt;

    /**
     * 图片描述
     */
    private String describe;

    /**
     * 生图挑战赛
     */
    private Challenge challenge;

    /**
     * 公开类型：everyone ： 所有人可见  myself ： 自己可见  fullLikes : 满足20点赞后可见
     */
    private String publicType;

    /**
     * 点赞id
     */
    @Transient
    private ObjectId likeId;

    /**
     * 是否删除
     */
    private Boolean deleted;

    /**
     * 活动id
     */
    private Long activityId;

    /**
     * 活动奖章
     */
    private Integer prizeLevel;

    /**
     * 是否活动删除
     */
    private Boolean activityDeleted;

    /**
     * 是否高质量
     */
    private Boolean featured;

    /**
     * 模型名称
     */
    @Transient
    private String modelDisplay;

    /**
     * 模型图标
     */
    @Transient
    private String modelAvatar;

    /**
     * 自己对图片是否点赞
     */
    @Transient
    private Boolean liked = Boolean.FALSE;

    /**
     * 自己对图片是否举报
     */
    @Transient
    private Boolean reported = Boolean.FALSE;

    @Transient
    private Long lastScoreIndex;

    /**
     * 浏览者是否已关注图片作者
     */
    @Transient
    private Boolean followedAuthor = Boolean.FALSE;
}






