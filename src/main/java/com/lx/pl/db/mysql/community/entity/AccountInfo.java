package com.lx.pl.db.mysql.community.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;

/**
 * 社区用户信息
 */
@Data
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "account")
@JsonIgnoreProperties({"vipEndTime"})
public class AccountInfo implements Serializable {

    /**
     * 用户id
     */
//    @Indexed
    private Long userId;
    /**
     * 用户昵称
     */
    private String userName;

    /**
     * 用户账号
     */
//    @Indexed
    private String userLoginName;

    /**
     * 头像文件路径
     */
    private String userAvatarUrl;

    /**
     * 是否为会员
     */
    private boolean whetherPro;

    /**
     * 会员类型
     */
    private String planLevel;

    private Long vipEndTime;
}