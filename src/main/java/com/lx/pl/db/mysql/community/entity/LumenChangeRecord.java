package com.lx.pl.db.mysql.community.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/6/7
 * @description 用户lumen变化记录
 */
@Data
@Document(collection = "lumen_change_record")
public class LumenChangeRecord {
    /**
     * 主键id
     */
    @Id
    private String id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 登录账号
     */
    private String userLoginName;

    /**
     * 变化类型
     * {@link com.lx.pl.enums.LumenChangeTypeEnum}
     */
    private String changeType;

    /**
     * 变化数量
     */
    private Integer changeLumen;

    /**
     * 变化来源
     * {@link com.lx.pl.enums.LumenChangeSourceEnum}
     */
    private String source;

    /**
     * 变化详情
     */
    private String detail;

    /**
     * 批次id，用于记录批次任务
     */
    private String batchId;

    /**
     * 关联id
     */
    private Long relationId;

    /**
     * 发生时间
     */
    private Long happenTimeSec;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新者
     */
    private String updateBy;
}
