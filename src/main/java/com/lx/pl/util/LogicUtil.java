package com.lx.pl.util;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.lx.pl.dto.alarm.AlarmDTO;
import com.lx.pl.pay.paypal.model.event.PaypalEventModel;
import com.lx.pl.pay.paypal.model.event.PaypalPaymentCaptureEvent;
import com.lx.pl.pay.paypal.model.event.PaypalPaymentSaleEvent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/6/6
 * @description
 */
@Slf4j
public class LogicUtil {

    /**
     * 根据像素计算点数
     */
    public static int calculateCostLumenByPixel(int picWidth, int picHeight) {
        //计算总像素在200W以下，200W-300W，300W-400W，400W以上的数量
        if (picWidth * picHeight <= 2000000) {
            return 1;
        } else if (picWidth * picHeight <= 3000000) {
            return 2;
        } else if (picWidth * picHeight <= 4000000) {
            return 3;
        } else {
            return 4;
        }
    }

    /**
     * 构建告警信息
     */
    public static AlarmDTO buildAlarmMessage(String type, String source, String detail, String userInfo, Exception e) {
        AlarmDTO alarmDTO = null;
        try {
            alarmDTO = AlarmDTO.builder()
                    .type(type)
                    .source(source)
                    .detail(detail)
                    .userInfo(userInfo)
                    .build();
            if (e != null) {
                //异常告警
                alarmDTO.setExceptionClassName(e.getClass().getName());
                StackTraceElement[] stackTraceElements = e.getStackTrace();
                if (stackTraceElements != null && stackTraceElements.length > 0) {
                    alarmDTO.setExceptionLineNum(stackTraceElements[0].getLineNumber());
                }
            }
        } catch (Exception e1) {
            log.error("构建告警信息异常, ", e1);
        }
        return alarmDTO;
    }

    /**
     * 从PayPal事件中获取用户信息
     */
    public static String getUserIdByPayPalEvent(PaypalEventModel eventModel) {
        if (eventModel == null) {
            return null;
        }

        String userId = null;
        try {
            String data = null;
            if (eventModel instanceof PaypalPaymentCaptureEvent) {
                PaypalPaymentCaptureEvent captureEvent = (PaypalPaymentCaptureEvent) eventModel;
                data = captureEvent.getModel().getCustomId();
            } else if (eventModel instanceof PaypalPaymentSaleEvent) {
                PaypalPaymentSaleEvent saleEvent = (PaypalPaymentSaleEvent) eventModel;
                data = saleEvent.getModel().getCustom();
            }
            if (data != null) {
                JSONObject jsonObject = JSONUtil.parseObj(data);
                userId = jsonObject.getStr("userId");
            }
        } catch (Exception e) {
            log.error("从PayPal事件中获取用户信息异常, eventModel: {}", JSONUtil.toJsonStr(eventModel), e);
        }

        return userId;
    }


    public static String convertLumenChangeRecordDetail(String source, String detail) {
        if (StringUtils.isBlank(detail)) {
            return detail;
        }
        String match = detail.toLowerCase();
        if (match.contains("ttp")) {
            return "Text to image";
        }

        if (match.contains("ptp")) {
            return "Image to Image";
        }

        if (match.contains("remove") && (match.contains("bg") || match.contains("background"))) {
            if (Objects.equals("Tools", source)) {
                return "Remove Background";
            }
            return "Remove BG";
        }

        if (match.contains("upscale")) {
            return "Upscale";
        }

        if (match.contains("inpaint")) {
            return "Inpaint";
        }

        if (match.contains("expand")) {
            return "Outpaint";
        }

        if (match.contains("linerecolor")) {
            return "Colorize";
        }

        if (match.contains("edit")) {
            return "Edit";
        }

        return detail;
    }
}
