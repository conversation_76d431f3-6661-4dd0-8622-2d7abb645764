package com.lx.pl.mq.consumer;

import com.fasterxml.jackson.core.type.TypeReference;
import com.lx.pl.dto.mq.ReadyServerVo;
import com.lx.pl.mq.listener.BaseMessageListener;
import com.lx.pl.service.GenService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 */
@Component
@Slf4j
@RocketMQMessageListener(topic = "${rocketmq.piclumen.topic:tp_piclumen_test}",
        consumerGroup = "${rocketmq.piclumen.ready.group:gid_piclumen_ready_test}",
        tag = "${rocketmq.piclumen.ready.tag:tag_piclumen_ready_test}",
        consumptionThreadCount = 8,
        maxCachedMessageCount = 12)
public class PiclumenComfyReadyListener extends BaseMessageListener {

    private static final Logger logger = LoggerFactory.getLogger("rocketmq-msg");

    @Resource
    private GenService genService;

    /**
     * 时间差阈值 ms
     */
    @Value("${piclumen.ready.timeDiffThreshold:5000}")
    private Integer timeDiffThreshold;

    @Override
    public void doWork(MessageView message) {
        try {
            long timestampNow = System.currentTimeMillis();
            logger.info("收到mq的消息id:{}", message.getMessageId());
            ReadyServerVo readyServerVo = this.getBody(message, new TypeReference<>() {
            });
            if (readyServerVo == null) {
                logger.error("消费mq的消息id:{},参数为空", message.getMessageId());
                return;
            }
//            Long timestamp = readyServerVo.getTimestamp();
//            if (timestampNow - timestamp > 60 * 1000){
//                logger.error("消费mq的消息id:{},时间戳过期", message.getMessageId());
//                return;
//            }
            genService.dealComfyReadyStatus(readyServerVo);
            logger.info("消费mq的消息id:{},完成", message.getMessageId());
        } catch (Exception e) {
            logger.error("消费mq的消息id:{},报错", message.getMessageId(), e);
        }
    }
}
