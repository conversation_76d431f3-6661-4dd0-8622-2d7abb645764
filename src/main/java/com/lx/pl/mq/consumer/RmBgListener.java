package com.lx.pl.mq.consumer;

import com.fasterxml.jackson.core.type.TypeReference;
import com.lx.pl.dto.mq.RmbgVo;
import com.lx.pl.mq.listener.BaseMessageListener;
import com.lx.pl.service.ToolRmbgResultService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 */
@Component
@Slf4j
@RocketMQMessageListener(topic = "${rocketmq.piclumen.rmbg.topic:tp_rmbg_dev}",
        consumerGroup = "${rocketmq.piclumen.rmbg.group:gid_rmbg-ok_dev}",
        tag = "${rocketmq.piclumen.rmbg-ok.tag:tag_rmbg-ok_dev}",
        consumptionThreadCount = 8,
        maxCachedMessageCount = 12)
public class RmBgListener extends BaseMessageListener {

    private static final Logger logger = LoggerFactory.getLogger("rocketmq-msg");

    @Resource
    private ToolRmbgResultService toolRmbgResultService;


    @Override
    public void doWork(MessageView message) {
        try {
            logger.info("收到mq的消息id:{}", message.getMessageId());
            RmbgVo readyServerVo = this.getBody(message, new TypeReference<>() {
            });
            if (readyServerVo == null) {
                logger.error("消费mq的消息id:{},参数为空", message.getMessageId());
                return;
            }
            toolRmbgResultService.dealRmbgReadyStatus(readyServerVo);
            logger.info("消费mq的消息id:{},完成", message.getMessageId());
        } catch (Exception e) {
            logger.error("消费mq的消息id:{},报错", message.getMessageId(), e);
        }
    }
}
