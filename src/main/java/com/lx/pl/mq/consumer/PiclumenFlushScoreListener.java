package com.lx.pl.mq.consumer;

import com.fasterxml.jackson.core.type.TypeReference;
import com.lx.pl.dto.ScoreFlushParams;
import com.lx.pl.mq.listener.BaseMessageListener;
import com.lx.pl.service.FileScoreService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * 线程参数根据实际调整
 */
@Component
@Slf4j
@RocketMQMessageListener(topic = "${rocketmq.piclumen.flush.topic:tp_piclumen_flush_test}",
        consumerGroup = "${rocketmq.piclumen.flush.group:gid_piclumen_flush_test}",
        tag = "${rocketmq.piclumen.flush.tag:tag_piclumen_flush_test}",
        consumptionThreadCount = 4,
        maxCachedMessageCount = 6)
public class PiclumenFlushScoreListener extends BaseMessageListener {

    private static final Logger logger = LoggerFactory.getLogger("rocketmq-msg");

    @Autowired
    FileScoreService fileScoreService;

    @Override
    public void doWork(MessageView message) {
        logger.info("收到mq的消息id:{}", message.getMessageId());
        ScoreFlushParams callBackParam = this.getBody(message, new TypeReference<>() {
        });
        fileScoreService.flushScore(callBackParam);
        logger.info("消费mq的消息id:{},完成", message.getMessageId());

    }
}
