package com.lx.pl.mq.consumer;

import com.fasterxml.jackson.core.type.TypeReference;
import com.lx.pl.dto.BackendCallBackParams;
import com.lx.pl.dto.generic.R;
import com.lx.pl.mq.listener.BaseMessageListener;
import com.lx.pl.service.GenService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * 线程参数根据实际调整
 */
@Component
@Slf4j
@RocketMQMessageListener(topic = "${rocketmq.piclumen.topic:tp_piclumen_test}",
        consumerGroup = "${rocketmq.piclumen.create.group:gid_piclumen_create_test}",
        tag = "${rocketmq.piclumen.create.tag:tag_piclumen_create_test}",
        consumptionThreadCount = 8,
        maxCachedMessageCount = 12)
public class PiclumenCreateListener extends BaseMessageListener {

    private static final Logger logger = LoggerFactory.getLogger("rocketmq-msg");

    @Autowired
    GenService genService;

    @Override
    public void doWork(MessageView message) {
        try {
            logger.info("收到mq的消息id:{}", message.getMessageId());
            R<BackendCallBackParams> callBackParam = this.getBody(message, new TypeReference<R<BackendCallBackParams>>() {
            });
            genService.callBack(callBackParam);
            logger.info("消费mq的消息id:{},完成", message.getMessageId());
        } catch (Exception e) {
            logger.error("消费mq的消息id:{},报错", message.getMessageId(), e);
        }
    }
}
