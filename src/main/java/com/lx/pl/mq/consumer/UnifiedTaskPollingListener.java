package com.lx.pl.mq.consumer;

import com.fasterxml.jackson.core.type.TypeReference;
import com.lx.pl.dto.mq.TaskPollingVo;
import com.lx.pl.mq.listener.BaseMessageListener;
import com.lx.pl.service.UnifiedTaskPollingResultService;
import com.lx.pl.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * MJ任务状态轮询消费者
 *
 * <AUTHOR>
 */
@Component
@Slf4j
@RocketMQMessageListener(topic = "${rocketmq.midjourney.polling.topic:tp_midjourney_polling_test}",
        consumerGroup = "${rocketmq.midjourney.polling.group:gid_midjourney_polling_test}",
        tag = "${rocketmq.midjourney.polling.tag:tag_midjourney_polling_test}",
        consumptionThreadCount = 4,
        maxCachedMessageCount = 6)
public class UnifiedTaskPollingListener extends BaseMessageListener {

    private static final Logger logger = LoggerFactory.getLogger("rocketmq-msg");

    @Resource
    private UnifiedTaskPollingResultService unifiedTaskPollingResultService;

    @Override
    public void doWork(MessageView message) {
        try {
            logger.info("收到统一任务轮询消息id:{}", message.getMessageId());
            TaskPollingVo pollingVo = this.getBody(message, new TypeReference<>() {
            });

            if (pollingVo == null) {
                logger.error("消费统一任务轮询消息id:{},参数为空", message.getMessageId());
                return;
            }
            logger.info("收到统一任务轮询消息, id: {}, pollingVo: {}", message.getMessageId(), JsonUtils.writeToString(pollingVo));
            // 处理任务状态轮询（统一入口）
            unifiedTaskPollingResultService.handleTaskStatusPolling(pollingVo);

            logger.info("消费统一任务轮询消息id:{},完成", message.getMessageId());
        } catch (Exception e) {
            logger.error("消费统一任务轮询消息id:{},报错", message.getMessageId(), e);
        }
    }
}
