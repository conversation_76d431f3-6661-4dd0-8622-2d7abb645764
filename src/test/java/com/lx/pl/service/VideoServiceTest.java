package com.lx.pl.service;

import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.db.mysql.gen.mapper.PromptRecordMapper;
import com.lx.pl.dto.CreatePicParams;
import com.lx.pl.dto.GenGenericPara;
import com.lx.pl.dto.ModelRightsVerifyResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestTemplate;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * VideoService 单元测试
 */
@ExtendWith(MockitoExtension.class)
class VideoServiceTest {

    @Mock
    private RestTemplate restTemplate;

    @Mock
    private PromptRecordMapper promptRecordMapper;

    @InjectMocks
    private VideoService videoService;

    private User testUser;
    private GenGenericPara genParameters;
    private CreatePicParams createPicParams;
    private ModelRightsVerifyResult modelRightsVerifyResult;

    @BeforeEach
    void setUp() {
        // 设置配置值
        ReflectionTestUtils.setField(videoService, "videoGenerationApiUrl", "http://test-api/video-generation");
        ReflectionTestUtils.setField(videoService, "defaultCallbackUrl", "http://test-callback");
        ReflectionTestUtils.setField(videoService, "videoModelId", "video123-test-model-id");

        // 创建测试用户
        testUser = new User();
        testUser.setId(1L);
        testUser.setLoginName("testuser");

        // 创建测试参数
        genParameters = new GenGenericPara();
        genParameters.setPrompt("A beautiful sunset over the ocean");
        genParameters.setNegative_prompt("blurry, low quality");
        genParameters.setModel_id("video123-test-model-id");

        // 创建视频生成参数
        GenGenericPara.VideoGenerationPara videoPara = new GenGenericPara.VideoGenerationPara();
        videoPara.setVideo_type("text_to_video");
        videoPara.setSeed(-1);
        videoPara.setWidth(832);
        videoPara.setHeight(480);
        videoPara.setAccelerated(false);
        genParameters.setVideoGenerationPara(videoPara);

        // 创建CreatePicParams
        createPicParams = new CreatePicParams();
        createPicParams.setMarkId("test-mark-id-123");
        createPicParams.setFastHour(true);
        createPicParams.setPlatform("web");

        // 创建ModelRightsVerifyResult
        modelRightsVerifyResult = new ModelRightsVerifyResult();
        modelRightsVerifyResult.setCostLumen(10);
    }

    @Test
    void testIsVideoModel_WithVideoModelId_ShouldReturnTrue() {
        // Given
        String videoModelId = "video123-test-model-id";

        // When
        boolean result = videoService.isVideoModel(videoModelId);

        // Then
        assertTrue(result);
    }

    @Test
    void testIsVideoModel_WithNonVideoModelId_ShouldReturnFalse() {
        // Given
        String nonVideoModelId = "image-model-id";

        // When
        boolean result = videoService.isVideoModel(nonVideoModelId);

        // Then
        assertFalse(result);
    }

    @Test
    void testIsVideoModel_WithNullModelId_ShouldReturnFalse() {
        // Given
        String nullModelId = null;

        // When
        boolean result = videoService.isVideoModel(nullModelId);

        // Then
        assertFalse(result);
    }

    @Test
    void testTextToVideoParameters() {
        // Given - 文生视频参数
        GenGenericPara.VideoGenerationPara videoPara = genParameters.getVideoGenerationPara();
        videoPara.setVideo_type("text_to_video");
        videoPara.setWidth(1024);
        videoPara.setHeight(576);

        // When - 验证参数设置
        assertEquals("text_to_video", videoPara.getVideo_type());
        assertEquals(1024, videoPara.getWidth());
        assertEquals(576, videoPara.getHeight());
        assertNull(videoPara.getImg_url()); // 文生视频不需要图片URL
    }

    @Test
    void testImageToVideoParameters() {
        // Given - 图生视频参数
        GenGenericPara.VideoGenerationPara videoPara = genParameters.getVideoGenerationPara();
        videoPara.setVideo_type("image_to_video");
        videoPara.setImg_url("https://example.com/test-image.jpg");
        videoPara.setWidth(null); // 图生视频不需要宽高
        videoPara.setHeight(null);

        // When - 验证参数设置
        assertEquals("image_to_video", videoPara.getVideo_type());
        assertEquals("https://example.com/test-image.jpg", videoPara.getImg_url());
        assertNull(videoPara.getWidth());
        assertNull(videoPara.getHeight());
    }

    @Test
    void testVideoGenerationParaDefaults() {
        // Given
        GenGenericPara.VideoGenerationPara videoPara = new GenGenericPara.VideoGenerationPara();

        // When - 设置默认值
        videoPara.setSeed(-1);
        videoPara.setAccelerated(false);

        // Then
        assertEquals(-1, videoPara.getSeed());
        assertEquals(false, videoPara.getAccelerated());
    }
}
