# 视频生成功能使用示例

## 概述

本文档展示了如何使用新增的视频生成功能，包括文生视频和图生视频两种模式。

**重要说明**: 视频生成功能已完全集成到现有的图片生成流程中，遵循相同的队列处理、权限控制和回调机制。

## API 接口

### 1. 通过 GenController.create 方法（推荐）

当 `model_id` 为视频生成模型时，会自动路由到视频生成功能。

**请求示例：**

```json
POST /api/gen/create
Content-Type: application/json

{
  "model_id": "video123-4567-8901-2345-678901234567",
  "prompt": "A woman dancing gracefully in a garden",
  "negative_prompt": "blurry, low quality, static",
  "videoGenerationPara": {
    "video_type": "text_to_video",
    "seed": -1,
    "width": 832,
    "height": 480,
    "callback_url": "http://127.0.0.1:8002/api/common/cb",
    "address": "************:7860",
    "accelerated": false
  }
}
```

### 2. 直接调用视频生成接口

**请求示例：**

```json
POST /api/gen/video-generation
Content-Type: application/json

{
  "model_id": "video123-4567-8901-2345-678901234567",
  "prompt": "A woman dancing gracefully in a garden",
  "negative_prompt": "blurry, low quality, static",
  "videoGenerationPara": {
    "video_type": "text_to_video",
    "seed": -1,
    "width": 832,
    "height": 480,
    "callback_url": "http://127.0.0.1:8002/api/common/cb",
    "address": "************:7860",
    "accelerated": false
  }
}
```

## 参数说明

### 基础参数

- `model_id`: 视频生成模型ID
- `prompt`: 正向提示词（必填）
- `negative_prompt`: 反向提示词（可选）

### 视频生成参数 (videoGenerationPara)

- `video_type`: 视频类型
  - `"text_to_video"`: 文生视频
  - `"image_to_video"`: 图生视频
- `seed`: 种子数，-1表示随机
- `width`: 视频宽度（文生视频时使用，默认832）
- `height`: 视频高度（文生视频时使用，默认480）
- `img_url`: 图片URL（图生视频时必填）
- `callback_url`: 回调URL
- `address`: 服务地址
- `accelerated`: 是否加速

## 使用场景

### 1. 文生视频 (Text to Video)

用于根据文本描述生成视频。

**示例请求：**

```json
{
  "model_id": "video123-4567-8901-2345-678901234567",
  "prompt": "A cat playing with a ball in a sunny garden",
  "negative_prompt": "blurry, static",
  "videoGenerationPara": {
    "video_type": "text_to_video",
    "seed": -1,
    "width": 832,
    "height": 480,
    "accelerated": false
  }
}
```

### 2. 图生视频 (Image to Video)

用于根据输入图片生成视频。注意：图生视频时不需要传 width 和 height。

**示例请求：**

```json
{
  "model_id": "video123-4567-8901-2345-678901234567",
  "prompt": "A woman dancing gracefully in a garden",
  "negative_prompt": "blurry, low quality, static",
  "videoGenerationPara": {
    "img_url": "https://uploads.piclumen.com/normal/20250618/16/d9980bfa8829479382e8a11d43f93f96.webp",
    "video_type": "image_to_video",
    "seed": -1,
    "callback_url": "http://127.0.0.1:8002/api/common/cb",
    "address": "************:7860",
    "accelerated": false
  }
}
```

## 响应格式

成功响应示例：

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "markId": "12345678-1234-1234-1234-123456789012",
    "fastHour": true,
    "featureName": "textToVideo",
    "apiResponse": {
      // Python后端API的响应数据
    }
  }
}
```

## 错误处理

常见错误及解决方案：

1. **Prompt cannot be empty!** - 提示词不能为空
2. **Video generation parameters required!** - 缺少视频生成参数
3. **Invalid video_type!** - video_type 必须是 'text_to_video' 或 'image_to_video'
4. **img_url is required for image_to_video!** - 图生视频需要提供图片URL
5. **Model not support!** - 模型不支持

## 配置说明

在 `application.properties` 中配置：

```properties
# 视频生成模型ID
video.modelId=video123-4567-8901-2345-678901234567

# 视频生成API地址（可选，有默认值）
video.generation.api.url=http://127.0.0.1:8002/api/gen/video-generation

# 默认回调URL（可选，有默认值）
video.generation.callback.url=http://127.0.0.1:8002/api/common/cb
```

## 功能特性

1. **统一接口**: 通过 GenController.create 方法统一处理，根据 model_id 自动路由
2. **队列处理**: 完全遵循现有图片生成流程，任务推送到队列由 DealTaskScheduler 处理
3. **参数验证**: 完整的参数验证，包括必填字段检查和格式验证
4. **权限控制**: 集成现有的用户权限和点数系统
5. **数据记录**: 自动保存 PromptRecord 记录，便于追踪和管理
6. **错误处理**: 完善的错误处理和日志记录
7. **类型区分**: 支持文生视频和图生视频两种模式，参数自动适配
8. **负载均衡**: 支持公平队列和非公平队列，以及relax等待队列
9. **回调机制**: 与现有图片生成使用相同的回调处理流程

## 技术架构

视频生成功能完全集成到现有架构中：

1. **请求接收**: GenController.create 或 GenController.videoGeneration
2. **参数验证**: 验证视频生成参数和用户权限
3. **数据保存**: 保存 PromptRecord 到数据库，包含视频生成参数
4. **队列推送**: 根据模型ID和功能类型推送到相应队列
5. **任务调度**: DealTaskScheduler.processTasks2() 从队列获取任务
6. **API调用**: LoadBalanceService.operateBackendApi() 调用Python后端
7. **结果处理**: 通过回调机制处理生成结果
