# 视频生成功能实现总结

## 概述

本文档总结了在GenController.create方法中增加文生视频和图生视频功能的完整实现。该实现完全遵循现有的图片生成流程，确保与现有架构的一致性。

## 实现的功能

### 1. 支持的视频类型
- **文生视频** (`text_to_video`): 根据文本提示词生成视频
- **图生视频** (`image_to_video`): 根据输入图片生成视频

### 2. 参数处理
- **图生视频**: 不需要传width和height，必须提供img_url
- **文生视频**: 需要width和height，不需要img_url
- **智能默认值**: 文生视频默认尺寸为832x480

## 代码修改详情

### 1. 数据结构扩展

#### GenGenericPara.java
```java
// 新增视频生成参数
@Schema(description = "视频生成参数")
private VideoGenerationPara videoGenerationPara;

// 新增内部类
public static class VideoGenerationPara {
    private String img_url;           // 图片URL（图生视频）
    private String video_type;        // 视频类型
    private Integer seed;             // 种子数
    private Integer width;            // 视频宽度（文生视频）
    private Integer height;           // 视频高度（文生视频）
    private String callback_url;      // 回调URL
    private String address;           // 服务地址
    private Boolean accelerated;      // 是否加速
}
```

#### FeaturesType.java
```java
// 新增功能类型
textToVideo("textToVideo", "文生视频"),
imageToVideo("imageToVideo", "图生视频");
```

### 2. 服务层实现

#### VideoService.java
- **generateVideo()**: 主要的视频生成方法，遵循现有流程
- **buildVideoGenerationRequest()**: 构建视频生成请求参数
- **savePromptRecord()**: 保存数据库记录
- **isVideoModel()**: 判断是否为视频模型

#### LoadBalanceService.java
- **judgeFeatures()**: 增加视频功能类型判断
- **invokeVideoGeneration()**: 调用Python视频生成接口
- **operateBackendApi()**: 增加视频生成的switch分支

### 3. 控制器层扩展

#### GenController.java
- **videoGeneration()**: 专用视频生成接口
- **create()**: 增加视频模型判断逻辑
- 完整的参数验证和权限控制

### 4. API接口扩展

#### BackendApi.java
```java
@POST("/api/gen/video-generation")
Response<BackendPromptResult> videoGeneration(@Body VideoGenerationParams videoParams);
```

### 5. 配置支持

#### application.properties
```properties
# 视频生成模型ID
video.modelId=video123-4567-8901-2345-678901234567
```

## 流程架构

### 完整的视频生成流程

1. **请求接收**
   - GenController.create (统一入口，推荐)
   - GenController.videoGeneration (专用接口)

2. **参数验证**
   - 基础参数验证 (prompt、model_id等)
   - 视频特定参数验证 (video_type、img_url等)
   - 用户权限和点数验证

3. **数据保存**
   - 保存PromptRecord到数据库
   - 包含完整的视频生成参数 (promptParams字段)
   - 设置正确的功能类型 (textToVideo/imageToVideo)

4. **队列推送**
   - 根据fastHour判断进入公平队列或非公平队列
   - 支持relax等待队列
   - 记录用户生图状态到Redis

5. **任务调度**
   - DealTaskScheduler.processTasks2() 从队列获取任务
   - 分配给可用的GPU服务器
   - 更新服务器状态

6. **API调用**
   - LoadBalanceService.operateBackendApi() 处理任务
   - 根据featureName调用对应的invoke方法
   - invokeVideoGeneration() 调用Python后端

7. **结果处理**
   - 通过现有的回调机制处理结果
   - 更新数据库记录
   - 通知前端用户

## 使用示例

### 1. 文生视频
```json
POST /api/gen/create
{
  "model_id": "video123-4567-8901-2345-678901234567",
  "prompt": "A cat playing with a ball in a sunny garden",
  "negative_prompt": "blurry, static",
  "resolution": {
    "width": 832,
    "height": 480,
    "batch_size": 1
  },
  "videoGenerationPara": {
    "video_type": "text_to_video",
    "seed": -1,
    "accelerated": false
  }
}
```

### 2. 图生视频
```json
POST /api/gen/create
{
  "model_id": "video123-4567-8901-2345-678901234567",
  "prompt": "A woman dancing gracefully in a garden",
  "negative_prompt": "blurry, low quality, static",
  "videoGenerationPara": {
    "img_url": "https://uploads.piclumen.com/normal/20250618/16/d9980bfa8829479382e8a11d43f93f96.webp",
    "video_type": "image_to_video",
    "seed": -1,
    "accelerated": false
  }
}
```

## 技术特点

### 1. 架构一致性
- 完全遵循现有图片生成流程
- 使用相同的队列处理机制
- 集成现有的权限和点数系统

### 2. 扩展性
- 易于添加新的视频模型
- 支持不同的视频生成参数
- 可配置的模型ID和API地址

### 3. 可靠性
- 完整的错误处理和日志记录
- 支持任务重试和失败处理
- 与现有监控和告警系统集成

### 4. 性能优化
- 支持负载均衡和并发控制
- 队列优先级管理
- 资源使用监控

## 测试建议

1. **单元测试**: 已创建VideoServiceTest验证核心功能
2. **集成测试**: 测试完整的视频生成流程
3. **性能测试**: 验证队列处理和并发性能
4. **错误处理测试**: 验证各种异常情况的处理

## 部署注意事项

1. **配置更新**: 确保video.modelId配置正确
2. **数据库**: PromptRecord表需要支持视频参数存储
3. **队列配置**: 确保Redis队列配置支持视频任务
4. **Python后端**: 确保后端API支持/api/gen/video-generation接口

## 总结

本实现成功将视频生成功能集成到现有的图片生成架构中，保持了代码的一致性和可维护性。通过遵循现有的设计模式和流程，确保了新功能的稳定性和可扩展性。用户可以通过统一的create接口来生成图片或视频，系统会根据model_id和参数自动选择合适的处理方式。
